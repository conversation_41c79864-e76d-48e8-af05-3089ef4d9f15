# Requirements Document

## Introduction

This feature integrates a secure backend authentication system using Apple Sign In to replace the current direct WebSocket connection approach. The new system will provide enhanced security by implementing proper token-based authentication, where users authenticate with Apple, exchange their Apple Identity Token for a backend access token, and use that token to establish secure WebSocket connections for speech recognition services.

## Requirements

### Requirement 1

**User Story:** As a SikTing user, I want to sign in with my Apple ID through a proper onboarding experience, so that I can securely access speech recognition services with proper authentication.

#### Acceptance Criteria

1. WHEN the user opens the app for the first time THEN the system SHALL present an onboarding page that introduces the core features of the app
2. WHEN the user is in the onboarding flow THEN they SHALL be able to swipe left from right through introduction pages
3. WHEN the user reaches the last onboarding page THEN the system SHALL present an Apple Sign In view
4. WHEN the user taps the Apple Sign In button THEN the system SHALL initiate the Apple authentication flow
5. WHEN Apple authentication is successful THEN the system SHALL receive an Apple Identity Token and proceed to the main app
6. WHEN Apple authentication fails THEN the system SHALL display an appropriate error message and allow retry
7. IF the user has previously signed in AND their session is still valid THEN the system SHALL skip onboarding and automatically authenticate them

### Requirement 2

**User Story:** As a SikTing user, I want my Apple Identity Token to be securely exchanged for a backend access token, so that I can access the speech recognition WebSocket service with proper authorization.

#### Acceptance Criteria

1. WHEN the system receives an Apple Identity Token THEN it SHALL send a POST request to `/api/token` endpoint with the token
2. WHEN the backend validates the Apple token successfully THEN it SHALL return an access token with expiration time
3. WHEN the backend token exchange fails THEN the system SHALL handle the error gracefully and prompt for re-authentication
4. WHEN the access token is received THEN the system SHALL store it securely for WebSocket authentication
5. IF the access token expires THEN the system SHALL automatically attempt to re-authenticate with Apple

### Requirement 3

**User Story:** As a SikTing user, I want to establish a secure WebSocket connection for speech recognition, so that my audio data is transmitted securely and my session is properly tracked.

#### Acceptance Criteria

1. WHEN the system has a valid access token THEN it SHALL connect to the WebSocket endpoint with the token as a query parameter
2. WHEN the WebSocket connection is established THEN the system SHALL be ready to send audio data for transcription
3. WHEN the WebSocket connection fails due to authentication THEN the system SHALL attempt to refresh the access token and reconnect
4. WHEN the WebSocket connection is lost THEN the system SHALL implement automatic reconnection with exponential backoff
5. IF the access token expires during a WebSocket session THEN the system SHALL gracefully handle the disconnection and re-authenticate

### Requirement 4

**User Story:** As a SikTing user, I want my audio data to be processed securely through the authenticated WebSocket connection, so that I receive accurate transcriptions while maintaining privacy and security.

#### Acceptance Criteria

1. WHEN the user starts recording THEN the system SHALL send audio data through the authenticated WebSocket connection
2. WHEN audio data is sent THEN it SHALL be properly formatted as Base64-encoded 16kHz mono PCM
3. WHEN the backend processes audio THEN it SHALL return transcription messages with confidence scores
4. WHEN transcription results are received THEN the system SHALL display both partial and final results appropriately
5. WHEN the user stops recording THEN the system SHALL properly close the audio stream while maintaining the WebSocket connection

### Requirement 5

**User Story:** As a SikTing user, I want proper error handling and user feedback, so that I understand what's happening when issues occur and can take appropriate action.

#### Acceptance Criteria

1. WHEN authentication fails THEN the system SHALL display clear error messages explaining the issue
2. WHEN network connectivity is lost THEN the system SHALL show connection status and attempt automatic reconnection
3. WHEN the backend service is unavailable THEN the system SHALL inform the user and suggest trying again later
4. WHEN rate limits are exceeded THEN the system SHALL display appropriate messaging and implement backoff strategies
5. IF any critical error occurs THEN the system SHALL log the error for debugging while protecting user privacy

### Requirement 6

**User Story:** As a SikTing user, I want my authentication state to be properly managed, so that I have a seamless experience without unnecessary re-authentication while maintaining security.

#### Acceptance Criteria

1. WHEN the app launches THEN the system SHALL check for existing valid authentication state
2. WHEN the access token is near expiration THEN the system SHALL proactively refresh authentication
3. WHEN the user explicitly signs out THEN the system SHALL clear all stored tokens and authentication state
4. WHEN the app goes to background THEN the system SHALL maintain authentication state for quick resume
5. IF the authentication state becomes invalid THEN the system SHALL gracefully prompt for re-authentication

### Requirement 7

**User Story:** As a SikTing user, I want to manage my authentication status through the User Profile section, so that I can easily view my sign-in state and manage my account.

#### Acceptance Criteria

1. WHEN the user navigates to the User Profile section AND is not signed in THEN the system SHALL display a sign-in action button
2. WHEN the user navigates to the User Profile section AND is already signed in THEN the system SHALL display user profile information including randomly generated avatar, user name, and user email
3. WHEN the user taps the sign-in button in the User Profile section THEN the system SHALL initiate the Apple authentication flow
4. WHEN the user is signed in THEN the User Profile section SHALL provide options to sign out
5. WHEN the user signs out THEN the system SHALL clear authentication state and return to the sign-in state in the User Profile section

### Requirement 8

**User Story:** As a SikTing user, I want the new authentication system to integrate seamlessly with existing app features, so that my current workflow and user experience are preserved while gaining enhanced security.

#### Acceptance Criteria

1. WHEN authentication is successful THEN all existing speech recognition features SHALL work as before
2. WHEN the user accesses transcription history THEN it SHALL continue to work with the new authentication system
3. WHEN the user changes settings THEN the authentication state SHALL be preserved
4. WHEN the user uses translation features THEN they SHALL continue to work independently of the new authentication
5. IF the user was previously using the app without authentication THEN the system SHALL provide a smooth migration path
