# Design Document

## Overview

This design document outlines the integration of a secure backend authentication system using Apple Sign In for the SikTing iOS application. The system will replace the current direct WebSocket connection approach with a token-based authentication flow that provides enhanced security and proper session management.

The integration involves creating new authentication services, modifying existing WebSocket connections to use access tokens, implementing an onboarding flow, and updating the user profile section to manage authentication state.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[iOS App] --> B[Apple Sign In]
    A --> C[Authentication Service]
    C --> D[Backend API]
    C --> E[WebSocket Service]
    E --> F[Speech Recognition Backend]

    B --> C
    D --> G[Token Validation]
    G --> H[Session Management]
    E --> I[Secure WebSocket Connection]

    subgraph "Authentication Flow"
        B
        C
        D
        G
        H
    end

    subgraph "Speech Recognition Flow"
        E
        F
        I
    end
```

### Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant A as iOS App
    participant AS as Apple Sign In
    participant B as Backend API
    participant WS as WebSocket Service

    U->>A: Open App (First Time)
    A->>U: Show Onboarding
    U->>A: Complete Onboarding
    A->>U: Show Apple Sign In
    U->>AS: Authenticate with Apple
    AS->>A: Return Identity Token
    A->>B: Exchange Token (POST /api/token)
    B->>A: Return Access Token
    A->>WS: Connect with Access Token
    WS->>A: WebSocket Connected
    A->>U: Ready for Speech Recognition
```

### Component Integration

The design integrates with existing SikTing components:

- **MainTabView**: Updated to handle authentication state
- **SettingsView**: Enhanced with user profile section
- **SpeechRecognitionViewModel**: Modified to use authenticated WebSocket
- **WebSocketManager**: Updated to support token-based authentication
- **NavigationCoordinator**: Enhanced to handle onboarding flow

## Components and Interfaces

### 1. Authentication Service

**File**: `SikTing/Features/Authentication/Services/SecureAuthenticationService.swift`

```swift
@MainActor
class SecureAuthenticationService: ObservableObject {
    @Published var authState: AuthenticationState = .notAuthenticated
    @Published var userInfo: UserInfo?
    @Published var isFirstLaunch: Bool = true

    // Core Methods
    func signInWithApple() async
    func signOut()
    func checkExistingAuthentication()
    func refreshTokenIfNeeded() async

    // Private Methods
    private func exchangeAppleToken(_ identityToken: String, userID: String, email: String?) async
    private func performTokenExchange(_ request: TokenRequest) async throws -> TokenResponse
}
```

### 2. Enhanced WebSocket Manager

**File**: `SikTing/Features/SpeechRecognition/Services/WebSocketManager.swift` (Enhanced)

The existing WebSocketManager will be enhanced to support token-based authentication as a wrapper around the current functionality:

```swift
class WebSocketManager: NSObject, ObservableObject {
    // Existing properties and methods remain unchanged

    // New authentication-aware connection method
    func connectWithToken(accessToken: String, to urlString: String? = nil, with config: FunASRConfiguration? = nil)

    // Enhanced connection method that builds authenticated URL
    private func buildAuthenticatedWebSocketURL(baseURL: String, accessToken: String) -> URL?

    // Token expiration handling
    private func handleTokenExpiration()
}
```

### 3. Onboarding Views

**File**: `SikTing/Features/Onboarding/Views/OnboardingView.swift`

```swift
struct OnboardingView: View {
    @State private var currentPage = 0
    @Binding var isPresented: Bool
    let onComplete: () -> Void

    var body: some View {
        TabView(selection: $currentPage) {
            // Feature introduction pages
            OnboardingPageView(...)
            // Final page with Apple Sign In
            AppleSignInPageView(...)
        }
    }
}
```

### 4. User Profile Section

**File**: `SikTing/Features/Settings/Views/UserProfileSection.swift`

```swift
struct UserProfileSection: View {
    @ObservedObject var authService: SecureAuthenticationService

    var body: some View {
        Group {
            if authService.authState.isAuthenticated {
                AuthenticatedUserView(userInfo: authService.userInfo)
            } else {
                SignInPromptView(onSignIn: authService.signInWithApple)
            }
        }
    }
}
```

### 5. Data Models

**File**: `SikTing/Features/Authentication/Models/AuthenticationModels.swift`

```swift
// Authentication States
enum AuthenticationState {
    case notAuthenticated
    case authenticating
    case authenticated(accessToken: String, expiresAt: Date)
    case failed(Error)

    var isAuthenticated: Bool { ... }
}

// User information with profile data
struct UserInfo {
    let userID: String
    let email: String?
    let accessToken: String
    let expiresAt: Date
    let displayName: String
    let avatarSeed: String // For generating consistent avatar

    var isValid: Bool { ... }
    var needsRefresh: Bool { ... }
}

// API Models
struct TokenRequest: Codable { ... }
struct TokenResponse: Codable { ... }
struct APIError: Codable, Error { ... }
```

## Data Models

### Authentication Models

```swift
// Core authentication state management
enum AuthenticationState {
    case notAuthenticated
    case authenticating
    case authenticated(accessToken: String, expiresAt: Date)
    case failed(Error)
}

// User information with profile data
struct UserInfo {
    let userID: String
    let email: String?
    let accessToken: String
    let expiresAt: Date
    let displayName: String
    let avatarSeed: String
}

// API communication models
struct TokenRequest: Codable {
    let identityToken: String
}

struct TokenResponse: Codable {
    let accessToken: String
    let expiresIn: Int
}
```

## Error Handling

### Authentication Errors

```swift
enum SecureAuthenticationError: Error, LocalizedError {
    case appleSignInFailed(String)
    case tokenExchangeFailed(String)
    case networkError(Error)
    case tokenExpired
    case rateLimitExceeded
    case serverUnavailable
    case invalidResponse

    var errorDescription: String? { ... }
    var userFriendlyMessage: String { ... }
    var shouldRetry: Bool { ... }
}
```

### WebSocket Errors

```swift
enum SecureWebSocketError: Error, LocalizedError {
    case authenticationRequired
    case tokenExpired
    case connectionFailed(String)
    case serverError(Int, String)
    case networkUnavailable

    var shouldReauthenticate: Bool { ... }
}
```

### Error Recovery Strategies

1. **Token Expiration**: Automatic re-authentication with Apple
2. **Network Errors**: Exponential backoff with retry
3. **Server Errors**: User notification with manual retry option
4. **Authentication Failures**: Clear session and prompt for re-authentication

## Testing Strategy

### Unit Tests

1. **Authentication Service Tests**

   - Apple Sign In flow simulation
   - Token exchange API calls
   - Session management
   - Error handling scenarios

2. **WebSocket Service Tests**

   - Authenticated connection establishment
   - Token expiration handling
   - Message sending/receiving
   - Reconnection logic

3. **Model Tests**
   - Data serialization/deserialization
   - Validation logic
   - State transitions

### Integration Tests

1. **End-to-End Authentication Flow**

   - Complete sign-in process
   - WebSocket connection with token
   - Speech recognition functionality
   - Session persistence

2. **Error Scenario Tests**
   - Network connectivity loss
   - Token expiration during session
   - Server unavailability
   - Invalid responses

### UI Tests

1. **Onboarding Flow**

   - Page navigation
   - Apple Sign In button interaction
   - Completion handling

2. **User Profile Section**
   - Sign-in state display
   - Authenticated state display
   - Sign-out functionality

### Mock Services

```swift
// Mock authentication service for testing
class MockSecureAuthenticationService: SecureAuthenticationService {
    var shouldSucceed = true
    var mockUserSession: UserSession?

    override func signInWithApple() async {
        // Simulate authentication flow
    }
}
```

## Security Considerations

### Token Security

1. **Storage**: Access tokens stored in memory only, not persisted
2. **Transmission**: HTTPS/WSS for all communications
3. **Expiration**: Short-lived tokens (5 minutes) with automatic refresh
4. **Validation**: Server-side token validation for all requests

### Network Security

1. **Certificate Pinning**: Implement for production builds
2. **TLS Validation**: Proper certificate validation
3. **Request Signing**: Optional additional security layer

### Privacy Protection

1. **Audio Data**: Not stored locally or on server
2. **User Data**: Minimal collection (Apple ID, email if granted)
3. **Session Data**: Metadata only for billing purposes
4. **Logging**: No sensitive data in logs

## Performance Considerations

### Memory Management

1. **Token Lifecycle**: Automatic cleanup of expired tokens
2. **View Controllers**: Weak references to prevent retain cycles

### Network Optimization

1. **Connection Pooling**: Reuse HTTP connections where possible

### Battery Optimization

1. **Reconnection Strategy**: Intelligent reconnection timing

## Migration Strategy

### Existing User Migration

1. **Graceful Transition**: Existing functionality preserved during migration
2. **Feature Flags**: Gradual rollout of authentication features

### Data Migration

1. **Settings Migration**: User preferences carried forward

## Configuration Management

### Environment Configuration

```swift
struct SecureBackendConfig {
    #if DEBUG
    static let baseURL = "https://dev.your-domain.com"
    static let enableLogging = true
    #else
    static let baseURL = "https://your-production-domain.com"
    static let enableLogging = false
    #endif

    static let appleBundleID = "com.superspacex.asr"
    static let tokenEndpoint = "/api/token"
    static let webSocketEndpoint = "/ws/asr"
    static let connectionTimeout: TimeInterval = 30
    static let tokenRefreshBuffer: TimeInterval = 60
}
```

This design provides a comprehensive approach to integrating secure backend authentication while maintaining the existing functionality and user experience of the SikTing application.
