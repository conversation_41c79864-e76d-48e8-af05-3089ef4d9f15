//
//  HistoryListViewModelTests.swift
//  SikTingTests
//
//  Created by Augment Agent on 2025-07-21.
//
//  Note: HistoryListViewModel is now primarily used as a base class for FloatingHistoryViewModel.
//  While HistoryListView has been removed, HistoryListViewModel contains core functionality
//  that is extended by FloatingHistoryViewModel for the current UI implementation.
//

import XCTest
import CoreData
import Combine
@testable import SikTing

class HistoryListViewModelTests: XCTestCase {
    
    // MARK: - Properties
    
    var viewModel: HistoryListViewModel!
    var mockStorageService: MockHistoryStorageService!
    var mockPaginationService: MockHistoryPaginationService!
    var mockMemoryService: MockMemoryManagementService!
    var cancellables: Set<AnyCancellable>!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        mockStorageService = MockHistoryStorageService()
        mockPaginationService = MockHistoryPaginationService()
        mockMemoryService = MockMemoryManagementService()
        cancellables = Set<AnyCancellable>()
        
        viewModel = HistoryListViewModel(
            storageService: mockStorageService,
            paginationService: mockPaginationService,
            memoryService: mockMemoryService
        )
    }
    
    override func tearDown() {
        cancellables.removeAll()
        viewModel = nil
        mockStorageService = nil
        mockPaginationService = nil
        mockMemoryService = nil
        super.tearDown()
    }
    
    // MARK: - Data Loading Tests
    
    func testLoadHistoryData() {
        // Given
        let expectation = XCTestExpectation(description: "Data loaded")
        let testSessions = createTestSessions(count: 5)
        mockStorageService.sessionsToReturn = testSessions
        
        // When
        viewModel.$sessions
            .dropFirst() // Skip initial empty value
            .sink { sessions in
                // Then
                XCTAssertEqual(sessions.count, 5)
                XCTAssertFalse(self.viewModel.isLoading)
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        viewModel.loadHistoryData()
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    func testLoadHistoryDataError() {
        // Given
        let expectation = XCTestExpectation(description: "Error handled")
        mockStorageService.shouldThrowError = true
        
        // When
        viewModel.$errorMessage
            .dropFirst() // Skip initial nil value
            .sink { errorMessage in
                // Then
                XCTAssertNotNil(errorMessage)
                XCTAssertFalse(self.viewModel.isLoading)
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        viewModel.loadHistoryData()
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    func testLoadingState() {
        // Given
        let expectation = XCTestExpectation(description: "Loading state tracked")
        mockStorageService.delayResponse = true
        
        var loadingStates: [Bool] = []
        
        // When
        viewModel.$isLoading
            .sink { isLoading in
                loadingStates.append(isLoading)
                if loadingStates.count == 3 { // false -> true -> false
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        viewModel.loadHistoryData()
        
        // Simulate completion
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.mockStorageService.completeDelayedResponse()
        }
        
        wait(for: [expectation], timeout: 2.0)
        
        // Then
        XCTAssertEqual(loadingStates, [false, true, false])
    }
    
    // MARK: - Tab Management Tests
    
    func testTabSelection() {
        // Given
        let testSessions = createTestSessions(count: 10)
        mockStorageService.sessionsToReturn = testSessions
        
        // When
        viewModel.selectedTab = .favorites
        
        // Then
        XCTAssertEqual(viewModel.selectedTab, .favorites)
        XCTAssertTrue(mockStorageService.fetchFavoritesWasCalled)
    }
    
    func testFilteredSessionsForRecents() {
        // Given
        let testSessions = createTestSessions(count: 5)
        mockStorageService.sessionsToReturn = testSessions
        viewModel.loadHistoryData()
        
        // When
        viewModel.selectedTab = .recents
        
        // Then
        XCTAssertEqual(viewModel.filteredSessions.count, 5)
    }
    
    func testFilteredSessionsForFavorites() {
        // Given
        let testSessions = createTestSessions(count: 5)
        testSessions[0].isFavourite = true
        testSessions[2].isFavourite = true
        mockStorageService.sessionsToReturn = testSessions
        viewModel.loadHistoryData()
        
        // When
        viewModel.selectedTab = .favorites
        
        // Then
        let favoriteSessions = viewModel.filteredSessions.filter { $0.isFavourite }
        XCTAssertEqual(favoriteSessions.count, 2)
    }
    
    func testFilteredSessionsForSaved() {
        // Given
        let testSessions = createTestSessions(count: 5)
        testSessions[1].isSaved = true
        testSessions[3].isSaved = true
        testSessions[4].isSaved = true
        mockStorageService.sessionsToReturn = testSessions
        viewModel.loadHistoryData()
        
        // When
        viewModel.selectedTab = .saved
        
        // Then
        let savedSessions = viewModel.filteredSessions.filter { $0.isSaved }
        XCTAssertEqual(savedSessions.count, 3)
    }
    
    // MARK: - Session Actions Tests
    
    func testToggleFavorite() {
        // Given
        let testSessions = createTestSessions(count: 1)
        let session = testSessions[0]
        session.isFavourite = false
        mockStorageService.sessionsToReturn = testSessions
        viewModel.loadHistoryData()
        
        // When
        viewModel.toggleFavorite(for: session)
        
        // Then
        XCTAssertTrue(mockStorageService.toggleFavoriteWasCalled)
        XCTAssertEqual(mockStorageService.lastToggledSession?.id, session.id)
    }
    
    func testToggleSaved() {
        // Given
        let testSessions = createTestSessions(count: 1)
        let session = testSessions[0]
        session.isSaved = false
        mockStorageService.sessionsToReturn = testSessions
        viewModel.loadHistoryData()
        
        // When
        viewModel.toggleSaved(for: session)
        
        // Then
        XCTAssertTrue(mockStorageService.toggleSavedWasCalled)
        XCTAssertEqual(mockStorageService.lastToggledSession?.id, session.id)
    }
    
    func testDeleteSession() {
        // Given
        let testSessions = createTestSessions(count: 3)
        let sessionToDelete = testSessions[1]
        mockStorageService.sessionsToReturn = testSessions
        viewModel.loadHistoryData()
        
        // When
        viewModel.deleteSession(sessionToDelete)
        
        // Then
        XCTAssertTrue(mockStorageService.deleteSessionWasCalled)
        XCTAssertEqual(mockStorageService.lastDeletedSession?.id, sessionToDelete.id)
    }
    
    // MARK: - Pagination Tests
    
    func testLoadNextPage() {
        // Given
        let testSessions = createTestSessions(count: 20)
        mockPaginationService.sessionsToReturn = testSessions
        
        // When
        viewModel.loadNextPage()
        
        // Then
        XCTAssertTrue(mockPaginationService.loadNextPageWasCalled)
    }
    
    func testShouldLoadMoreWhenNearEnd() {
        // Given
        let testSessions = createTestSessions(count: 25)
        mockStorageService.sessionsToReturn = testSessions
        viewModel.loadHistoryData()
        
        // When
        let shouldLoad = viewModel.shouldLoadMore(currentIndex: 22) // Near end of 25 items
        
        // Then
        XCTAssertTrue(shouldLoad)
    }
    
    func testShouldNotLoadMoreWhenNotNearEnd() {
        // Given
        let testSessions = createTestSessions(count: 25)
        mockStorageService.sessionsToReturn = testSessions
        viewModel.loadHistoryData()
        
        // When
        let shouldLoad = viewModel.shouldLoadMore(currentIndex: 10) // Not near end
        
        // Then
        XCTAssertFalse(shouldLoad)
    }
    
    // MARK: - Memory Management Tests
    
    func testMemoryOptimization() {
        // Given
        mockMemoryService.shouldOptimize = true
        
        // When
        viewModel.optimizeMemoryIfNeeded()
        
        // Then
        XCTAssertTrue(mockMemoryService.optimizeMemoryWasCalled)
    }
    
    func testMemoryPressureHandling() {
        // Given
        let expectation = XCTestExpectation(description: "Memory pressure handled")
        mockMemoryService.memoryPressureLevel = .critical
        
        // When
        viewModel.$memoryPressureLevel
            .dropFirst()
            .sink { level in
                // Then
                XCTAssertEqual(level, .critical)
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        viewModel.checkMemoryPressure()
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    // MARK: - Performance Metrics Tests
    
    func testPerformanceMetricsTracking() {
        // Given
        let expectation = XCTestExpectation(description: "Performance tracked")
        
        // When
        viewModel.$performanceMetrics
            .dropFirst()
            .sink { metrics in
                // Then
                XCTAssertNotNil(metrics)
                XCTAssertGreaterThan(metrics?.loadTime ?? 0, 0)
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        viewModel.loadHistoryData()
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    // MARK: - Search Integration Tests
    
    func testSearchModeToggle() {
        // When
        viewModel.isSearchMode = true
        
        // Then
        XCTAssertTrue(viewModel.isSearchMode)
        
        // When
        viewModel.isSearchMode = false
        
        // Then
        XCTAssertFalse(viewModel.isSearchMode)
    }
    
    // MARK: - Helper Methods
    
    private func createTestSessions(count: Int) -> [HistorySession] {
        return (0..<count).map { index in
            let session = HistorySession()
            session.id = UUID()
            session.title = "Test Session \(index)"
            session.createdAt = Date().addingTimeInterval(TimeInterval(-index * 3600))
            session.language = "en-US"
            session.duration = Double(index * 60)
            session.isFavourite = false
            session.isSaved = false
            return session
        }
    }
}

// MARK: - Mock Services

class MockHistoryStorageService: HistoryStorageService {
    var sessionsToReturn: [HistorySession] = []
    var shouldThrowError = false
    var delayResponse = false
    var fetchFavoritesWasCalled = false
    var toggleFavoriteWasCalled = false
    var toggleSavedWasCalled = false
    var deleteSessionWasCalled = false
    var lastToggledSession: HistorySession?
    var lastDeletedSession: HistorySession?
    
    private var delayedCompletion: (() -> Void)?
    
    override func fetchAllSessions() throws -> [HistorySession] {
        if shouldThrowError {
            throw HistoryError.fetchFailed
        }
        
        if delayResponse {
            // Store completion for later
            return []
        }
        
        return sessionsToReturn
    }
    
    override func fetchFavoriteSessions() throws -> [HistorySession] {
        fetchFavoritesWasCalled = true
        return sessionsToReturn.filter { $0.isFavourite }
    }
    
    override func toggleFavorite(for session: HistorySession) throws {
        toggleFavoriteWasCalled = true
        lastToggledSession = session
        session.isFavourite.toggle()
    }
    
    override func toggleSaved(for session: HistorySession) throws {
        toggleSavedWasCalled = true
        lastToggledSession = session
        session.isSaved.toggle()
    }
    
    override func deleteSession(_ session: HistorySession) throws {
        deleteSessionWasCalled = true
        lastDeletedSession = session
    }
    
    func completeDelayedResponse() {
        delayedCompletion?()
    }
}

class MockHistoryPaginationService: HistoryPaginationService {
    var sessionsToReturn: [HistorySession] = []
    var loadNextPageWasCalled = false
    
    override func loadNextPage(for tab: HistoryTab) -> [HistorySession] {
        loadNextPageWasCalled = true
        return sessionsToReturn
    }
}

class MockMemoryManagementService: MemoryManagementService {
    var shouldOptimize = false
    var optimizeMemoryWasCalled = false
    var memoryPressureLevel: MemoryPressureLevel = .normal
    
    override func shouldOptimizeMemory() -> Bool {
        return shouldOptimize
    }
    
    override func optimizeMemory() {
        optimizeMemoryWasCalled = true
    }
    
    override func getCurrentMemoryPressure() -> MemoryPressureLevel {
        return memoryPressureLevel
    }
}
