//
//  SearchFunctionalityTests.swift
//  SikTingTests
//
//  Created by Augment Agent on 2025-07-21.
//

import XCTest
import CoreData
@testable import SikTing

class SearchFunctionalityTests: XCTestCase {
    
    // MARK: - Properties
    
    var storageService: HistoryStorageService!
    var searchService: HistorySearchService!
    var testCoreDataStack: CoreDataStack!
    var testContext: NSManagedObjectContext!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        setupTestEnvironment()
        createTestData()
    }
    
    override func tearDown() {
        clearTestData()
        storageService = nil
        searchService = nil
        testContext = nil
        testCoreDataStack = nil
        super.tearDown()
    }
    
    private func setupTestEnvironment() {
        testCoreDataStack = CoreDataStack.inMemoryStack()
        testContext = testCoreDataStack.viewContext
        storageService = HistoryStorageService(coreDataStack: testCoreDataStack)
        searchService = HistorySearchService(storageService: storageService)
    }
    
    private func createTestData() {
        let sessions = [
            createSession(title: "Team Meeting Notes", content: "Discussed project roadmap and quarterly goals. Action items include updating documentation and scheduling follow-up meetings."),
            createSession(title: "Interview with John Smith", content: "Technical interview covering Swift programming, iOS development, and system design patterns."),
            createSession(title: "Spanish Language Lesson", content: "Hola, mi nombre es María. Estoy aprendiendo español y practicando conversación básica."),
            createSession(title: "Product Demo Recording", content: "Demonstration of new features including search functionality, export options, and user interface improvements."),
            createSession(title: "Customer Feedback Session", content: "User feedback on mobile app performance, battery usage, and feature requests for future releases."),
            createSession(title: "Code Review Discussion", content: "Reviewing pull request for authentication module. Discussed security best practices and error handling."),
            createSession(title: "Marketing Strategy Meeting", content: "Planning social media campaigns, content creation schedule, and target audience analysis."),
            createSession(title: "Bug Triage Session", content: "Prioritizing critical bugs in the iOS application. Memory leaks and crash reports need immediate attention.")
        ]
        
        for session in sessions {
            do {
                try storageService.saveSession(session)
            } catch {
                XCTFail("Failed to save test session: \(error)")
            }
        }
    }
    
    private func createSession(title: String, content: String) -> HistorySession {
        let session = HistorySession(context: testContext)
        session.id = UUID()
        session.title = title
        session.createdAt = Date()
        session.updatedAt = Date()
        session.language = title.contains("Spanish") ? "es-ES" : "en-US"
        session.duration = Double.random(in: 300...3600)
        session.isFavourite = false
        session.isSaved = false
        
        // Create entries with the content
        let sentences = content.components(separatedBy: ". ")
        for (index, sentence) in sentences.enumerated() {
            let entry = HistoryEntry(context: testContext)
            entry.id = UUID()
            entry.sessionId = session.id
            entry.text = sentence + (index < sentences.count - 1 ? "." : "")
            entry.timestamp = Date().addingTimeInterval(TimeInterval(index * 5))
            entry.confidence = Double.random(in: 0.8...0.98)
            entry.isPartial = false
            entry.language = session.language
            entry.session = session
            session.addToEntries(entry)
        }
        
        return session
    }
    
    private func clearTestData() {
        let sessionRequest: NSFetchRequest<NSFetchRequestResult> = HistorySession.fetchRequest()
        let deleteSessionsRequest = NSBatchDeleteRequest(fetchRequest: sessionRequest)
        
        let entryRequest: NSFetchRequest<NSFetchRequestResult> = HistoryEntry.fetchRequest()
        let deleteEntriesRequest = NSBatchDeleteRequest(fetchRequest: entryRequest)
        
        do {
            try testContext.execute(deleteEntriesRequest)
            try testContext.execute(deleteSessionsRequest)
            try testContext.save()
        } catch {
            XCTFail("Failed to clear test data: \(error)")
        }
    }
    
    // MARK: - Basic Search Tests
    
    func testBasicTitleSearch() throws {
        // When
        let results = try searchService.searchSessions(query: "meeting")
        
        // Then
        XCTAssertEqual(results.count, 2) // "Team Meeting Notes" and "Marketing Strategy Meeting"
        
        let titles = results.map { $0.title ?? "" }
        XCTAssertTrue(titles.contains("Team Meeting Notes"))
        XCTAssertTrue(titles.contains("Marketing Strategy Meeting"))
    }
    
    func testBasicContentSearch() throws {
        // When
        let results = try searchService.searchSessions(query: "iOS")
        
        // Then
        XCTAssertEqual(results.count, 2) // Interview and Bug Triage sessions
        
        let titles = results.map { $0.title ?? "" }
        XCTAssertTrue(titles.contains("Interview with John Smith"))
        XCTAssertTrue(titles.contains("Bug Triage Session"))
    }
    
    func testCaseInsensitiveSearch() throws {
        // When
        let lowerResults = try searchService.searchSessions(query: "swift")
        let upperResults = try searchService.searchSessions(query: "SWIFT")
        let mixedResults = try searchService.searchSessions(query: "Swift")
        
        // Then
        XCTAssertEqual(lowerResults.count, upperResults.count)
        XCTAssertEqual(upperResults.count, mixedResults.count)
        XCTAssertEqual(lowerResults.count, 1) // Interview session
    }
    
    func testEmptyQueryReturnsNoResults() throws {
        // When
        let results = try searchService.searchSessions(query: "")
        
        // Then
        XCTAssertEqual(results.count, 0)
    }
    
    func testNoMatchesFound() throws {
        // When
        let results = try searchService.searchSessions(query: "nonexistent")
        
        // Then
        XCTAssertEqual(results.count, 0)
    }
    
    // MARK: - Advanced Search Tests
    
    func testMultiWordSearch() throws {
        // When
        let results = try searchService.searchSessions(query: "project roadmap")
        
        // Then
        XCTAssertEqual(results.count, 1) // Team Meeting Notes
        XCTAssertEqual(results.first?.title, "Team Meeting Notes")
    }
    
    func testPartialWordSearch() throws {
        // When
        let results = try searchService.searchSessions(query: "auth")
        
        // Then
        XCTAssertEqual(results.count, 1) // Code Review Discussion (authentication)
        XCTAssertEqual(results.first?.title, "Code Review Discussion")
    }
    
    func testPhraseSearch() throws {
        // When
        let results = try searchService.searchSessions(query: "\"system design\"")
        
        // Then
        XCTAssertEqual(results.count, 1) // Interview session
        XCTAssertEqual(results.first?.title, "Interview with John Smith")
    }
    
    func testLanguageSpecificSearch() throws {
        // When
        let spanishResults = try searchService.searchSessions(query: "español")
        let englishResults = try searchService.searchSessions(query: "learning")
        
        // Then
        XCTAssertEqual(spanishResults.count, 1) // Spanish lesson
        XCTAssertEqual(englishResults.count, 1) // Spanish lesson (contains "learning")
        
        // Both should find the same session
        XCTAssertEqual(spanishResults.first?.id, englishResults.first?.id)
    }
    
    // MARK: - Search Result Ranking Tests
    
    func testTitleMatchRankedHigher() throws {
        // When
        let results = try searchService.searchSessions(query: "demo")
        
        // Then
        XCTAssertGreaterThan(results.count, 0)
        
        // Session with "demo" in title should be ranked higher
        let firstResult = results.first!
        XCTAssertEqual(firstResult.title, "Product Demo Recording")
    }
    
    func testRecentSessionsRankedHigher() throws {
        // Given - Create sessions with different dates
        let oldSession = createSession(title: "Old Meeting Notes", content: "Old content with search term")
        oldSession.createdAt = Date().addingTimeInterval(-86400 * 7) // 7 days ago
        
        let recentSession = createSession(title: "Recent Discussion", content: "Recent content with search term")
        recentSession.createdAt = Date() // Now
        
        try storageService.saveSession(oldSession)
        try storageService.saveSession(recentSession)
        
        // When
        let results = try searchService.searchSessions(query: "search term")
        
        // Then
        XCTAssertGreaterThan(results.count, 1)
        
        // Recent session should be ranked higher
        let firstResult = results.first!
        XCTAssertEqual(firstResult.title, "Recent Discussion")
    }
    
    func testHighConfidenceEntriesRankedHigher() throws {
        // Given
        let lowConfidenceSession = createSession(title: "Low Confidence Session", content: "Test content")
        let highConfidenceSession = createSession(title: "High Confidence Session", content: "Test content")
        
        // Set different confidence levels
        if let lowEntries = lowConfidenceSession.entries?.allObjects as? [HistoryEntry] {
            for entry in lowEntries {
                entry.confidence = 0.6
            }
        }
        
        if let highEntries = highConfidenceSession.entries?.allObjects as? [HistoryEntry] {
            for entry in highEntries {
                entry.confidence = 0.95
            }
        }
        
        try storageService.saveSession(lowConfidenceSession)
        try storageService.saveSession(highConfidenceSession)
        
        // When
        let results = try searchService.searchSessions(query: "test content")
        
        // Then
        XCTAssertGreaterThan(results.count, 1)
        
        // High confidence session should be ranked higher
        let firstResult = results.first!
        XCTAssertEqual(firstResult.title, "High Confidence Session")
    }
    
    // MARK: - Search Term Highlighting Tests
    
    func testSearchTermHighlighting() throws {
        // When
        let results = try searchService.searchSessionsWithHighlighting(query: "iOS")
        
        // Then
        XCTAssertGreaterThan(results.count, 0)
        
        let firstResult = results.first!
        XCTAssertNotNil(firstResult.highlightedContent)
        XCTAssertTrue(firstResult.highlightedContent?.contains("<mark>iOS</mark>") == true)
    }
    
    func testMultipleTermHighlighting() throws {
        // When
        let results = try searchService.searchSessionsWithHighlighting(query: "project roadmap")
        
        // Then
        XCTAssertGreaterThan(results.count, 0)
        
        let firstResult = results.first!
        XCTAssertNotNil(firstResult.highlightedContent)
        XCTAssertTrue(firstResult.highlightedContent?.contains("<mark>project</mark>") == true)
        XCTAssertTrue(firstResult.highlightedContent?.contains("<mark>roadmap</mark>") == true)
    }
    
    func testHighlightingPreservesContext() throws {
        // When
        let results = try searchService.searchSessionsWithHighlighting(query: "authentication")
        
        // Then
        XCTAssertGreaterThan(results.count, 0)
        
        let firstResult = results.first!
        XCTAssertNotNil(firstResult.highlightedContent)
        
        // Should include context around the highlighted term
        let highlighted = firstResult.highlightedContent!
        XCTAssertTrue(highlighted.contains("pull request for <mark>authentication</mark> module"))
    }
    
    // MARK: - Search Performance Tests
    
    func testSearchPerformanceWithLargeDataset() throws {
        // Given - Create a large dataset
        let largeDatasetSize = 1000
        
        for i in 0..<largeDatasetSize {
            let session = createSession(
                title: "Performance Test Session \(i)",
                content: "This is test content for performance testing with various keywords like search, performance, and optimization."
            )
            try storageService.saveSession(session)
        }
        
        // When & Then - Measure search performance
        measure {
            do {
                let results = try searchService.searchSessions(query: "performance")
                XCTAssertGreaterThan(results.count, 0)
            } catch {
                XCTFail("Search failed: \(error)")
            }
        }
    }
    
    func testComplexQueryPerformance() throws {
        // Given
        let complexQuery = "iOS development Swift programming authentication security"
        
        // When & Then
        measure {
            do {
                let results = try searchService.searchSessions(query: complexQuery)
                // Results count doesn't matter for performance test
                _ = results.count
            } catch {
                XCTFail("Complex search failed: \(error)")
            }
        }
    }
    
    func testConcurrentSearchPerformance() throws {
        // Given
        let expectation = XCTestExpectation(description: "Concurrent searches completed")
        expectation.expectedFulfillmentCount = 10
        
        let queries = ["meeting", "iOS", "project", "demo", "feedback", "code", "marketing", "bug", "authentication", "performance"]
        
        // When
        for query in queries {
            DispatchQueue.global(qos: .userInitiated).async {
                do {
                    let results = try self.searchService.searchSessions(query: query)
                    // Verify search completed successfully
                    _ = results.count
                    expectation.fulfill()
                } catch {
                    XCTFail("Concurrent search failed: \(error)")
                }
            }
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    // MARK: - Search Filtering Tests
    
    func testSearchWithLanguageFilter() throws {
        // When
        let englishResults = try searchService.searchSessions(query: "language", language: "en-US")
        let spanishResults = try searchService.searchSessions(query: "language", language: "es-ES")
        
        // Then
        XCTAssertGreaterThan(englishResults.count, 0)
        XCTAssertEqual(spanishResults.count, 0) // No Spanish sessions contain "language"
        
        // All results should be in English
        for session in englishResults {
            XCTAssertEqual(session.language, "en-US")
        }
    }
    
    func testSearchWithDateRangeFilter() throws {
        // Given
        let now = Date()
        let yesterday = now.addingTimeInterval(-86400)
        let tomorrow = now.addingTimeInterval(86400)
        
        // When
        let recentResults = try searchService.searchSessions(
            query: "meeting",
            dateRange: DateRange(start: yesterday, end: tomorrow)
        )
        
        // Then
        XCTAssertGreaterThan(recentResults.count, 0)
        
        // All results should be within date range
        for session in recentResults {
            guard let createdAt = session.createdAt else { continue }
            XCTAssertGreaterThanOrEqual(createdAt, yesterday)
            XCTAssertLessThanOrEqual(createdAt, tomorrow)
        }
    }
    
    func testSearchWithDurationFilter() throws {
        // When
        let shortResults = try searchService.searchSessions(
            query: "session",
            minDuration: 0,
            maxDuration: 1800 // 30 minutes
        )
        
        // Then
        XCTAssertGreaterThan(shortResults.count, 0)
        
        // All results should be within duration range
        for session in shortResults {
            XCTAssertLessThanOrEqual(session.duration, 1800)
        }
    }
    
    // MARK: - Error Handling Tests
    
    func testSearchWithInvalidQuery() throws {
        // When & Then - Should handle special characters gracefully
        let specialCharResults = try searchService.searchSessions(query: "!@#$%^&*()")
        XCTAssertEqual(specialCharResults.count, 0)
        
        let sqlInjectionResults = try searchService.searchSessions(query: "'; DROP TABLE sessions; --")
        XCTAssertEqual(sqlInjectionResults.count, 0)
    }
    
    func testSearchWithVeryLongQuery() throws {
        // Given
        let longQuery = String(repeating: "very long query ", count: 100)
        
        // When & Then - Should handle long queries without crashing
        let results = try searchService.searchSessions(query: longQuery)
        XCTAssertEqual(results.count, 0) // No matches expected
    }
    
    func testSearchWithUnicodeCharacters() throws {
        // When
        let unicodeResults = try searchService.searchSessions(query: "María")
        
        // Then
        XCTAssertEqual(unicodeResults.count, 1) // Spanish lesson
        XCTAssertEqual(unicodeResults.first?.title, "Spanish Language Lesson")
    }
}
