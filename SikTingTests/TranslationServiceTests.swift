//
//  TranslationServiceTests.swift
//  SikTingTests
//
//  Created by Augment Agent on 2025/7/21.
//

import XCTest
import Combine
@testable import SikTing

@MainActor
final class TranslationServiceTests: XCTestCase {

    var translationService: HybridTranslationService!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        // Initialize HybridTranslationService for unit tests
        translationService = HybridTranslationService()
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        cancellables = nil
        translationService = nil
        super.tearDown()
    }
    
    // MARK: - Language Mapping Tests
    
    func testTranslationLanguageFromRecognizedLanguage() {
        // Test language mapping from RecognizedLanguage to TranslationLanguage
        XCTAssertEqual(TranslationLanguage.from(recognizedLanguage: .english), .english)
        XCTAssertEqual(TranslationLanguage.from(recognizedLanguage: .chinese), .chineseSimplified)
        XCTAssertEqual(TranslationLanguage.from(recognizedLanguage: .cantonese), .cantonese)
        XCTAssertEqual(TranslationLanguage.from(recognizedLanguage: .japanese), .japanese)
        XCTAssertEqual(TranslationLanguage.from(recognizedLanguage: .unknown), .english)
    }
    
    func testTranslationLanguageProperties() {
        let spanish = TranslationLanguage.spanish
        XCTAssertEqual(spanish.rawValue, "spa_Latn")
        XCTAssertEqual(spanish.displayName, "Español")
        XCTAssertEqual(spanish.flag, "🇪🇸")
        XCTAssertEqual(spanish.id, "spa_Latn")
        
        let cantonese = TranslationLanguage.cantonese
        XCTAssertEqual(cantonese.rawValue, "yue_Hant")
        XCTAssertEqual(cantonese.displayName, "粵語")
        XCTAssertEqual(cantonese.flag, "🇭🇰")
    }
    
    // MARK: - Translation Service Configuration Tests
    
    func testTranslationServiceInitialization() {
        XCTAssertFalse(translationService.isTranslating)
        XCTAssertEqual(translationService.selectedTargetLanguage, .spanish)
        XCTAssertTrue(translationService.isTranslationEnabled)
    }
    
    func testTranslationServiceSettings() {
        // Test changing target language
        translationService.selectedTargetLanguage = .french
        XCTAssertEqual(translationService.selectedTargetLanguage, .french)
        
        // Test enabling/disabling translation
        translationService.isTranslationEnabled = false
        XCTAssertFalse(translationService.isTranslationEnabled)
        
        translationService.isTranslationEnabled = true
        XCTAssertTrue(translationService.isTranslationEnabled)
    }
    
    // MARK: - Translation Logic Tests
    
    func testTranslationWithEmptyText() async {
        do {
            _ = try await translationService.translate(
                text: "",
                from: .english,
                to: .spanish
            )
            XCTFail("Should throw error for empty text")
        } catch TranslationError.emptyText {
            // Expected error
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
    }
    
    func testTranslationWithSameSourceAndTarget() async {
        do {
            let result = try await translationService.translate(
                text: "Hello world",
                from: .english,
                to: .english
            )
            XCTAssertEqual(result, "Hello world", "Should return original text when source and target are the same")
        } catch {
            XCTFail("Should not throw error for same source and target: \(error)")
        }
    }
    
    func testTranslationWithWhitespaceOnlyText() async {
        do {
            _ = try await translationService.translate(
                text: "   \n\t  ",
                from: .english,
                to: .spanish
            )
            XCTFail("Should throw error for whitespace-only text")
        } catch TranslationError.emptyText {
            // Expected error
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
    }
    
    // MARK: - Integration Tests (require network)
    
    func testRealTranslationRequest() async {
        // This test requires network connectivity to the NLLB server
        // Skip if running in CI or without network
        guard ProcessInfo.processInfo.environment["CI"] == nil else {
            throw XCTSkip("Skipping network test in CI environment")
        }
        
        do {
            let result = try await translationService.translate(
                text: "Hello world",
                from: .english,
                to: .spanish
            )
            
            XCTAssertFalse(result.isEmpty, "Translation result should not be empty")
            XCTAssertNotEqual(result, "Hello world", "Translation should be different from original")
            print("✅ Translation test: 'Hello world' → '\(result)'")
            
        } catch {
            XCTFail("Translation request failed: \(error)")
        }
    }
    
    func testRealLanguageDetection() async {
        // This test requires network connectivity to the NLLB server
        guard ProcessInfo.processInfo.environment["CI"] == nil else {
            throw XCTSkip("Skipping network test in CI environment")
        }
        
        do {
            let detection = try await translationService.detectLanguage(text: "Hello world")
            
            XCTAssertGreaterThan(detection.confidence, 0.0, "Confidence should be greater than 0")
            XCTAssertLessThanOrEqual(detection.confidence, 1.0, "Confidence should be less than or equal to 1")
            print("✅ Language detection test: 'Hello world' → \(detection.language.displayName) (confidence: \(detection.confidence))")
            
        } catch {
            XCTFail("Language detection failed: \(error)")
        }
    }
    
    // MARK: - Error Handling Tests
    
    func testTranslationErrorEquality() {
        let error1 = TranslationError.emptyText
        let error2 = TranslationError.emptyText
        let error3 = TranslationError.invalidURL
        
        XCTAssertEqual(error1.localizedDescription, error2.localizedDescription)
        XCTAssertNotEqual(error1.localizedDescription, error3.localizedDescription)
    }
    
    func testTranslationStateEquality() {
        let state1 = TranslationState.idle
        let state2 = TranslationState.idle
        let state3 = TranslationState.translating
        let state4 = TranslationState.completed("Hello")
        let state5 = TranslationState.completed("Hello")
        let state6 = TranslationState.completed("World")
        
        XCTAssertEqual(state1, state2)
        XCTAssertNotEqual(state1, state3)
        XCTAssertEqual(state4, state5)
        XCTAssertNotEqual(state4, state6)
    }
    
    // MARK: - Performance Tests
    
    func testTranslationServicePerformance() {
        // Test that HybridTranslationService initialization is fast
        measure {
            let service = HybridTranslationService()
            XCTAssertNotNil(service)
        }
    }
}

// MARK: - Mock Translation Service for Testing

class MockTranslationService: HybridTranslationService {
    var shouldFail = false
    var mockTranslation = "Mocked translation"
    var mockLanguage = TranslationLanguage.spanish
    var mockConfidence = 0.95
    
    override func translate(text: String, from sourceLanguage: TranslationLanguage, to targetLanguage: TranslationLanguage) async throws -> String {
        if shouldFail {
            throw TranslationError.serverError(500)
        }
        
        // Simulate network delay
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        return mockTranslation
    }
    
    override func detectLanguage(text: String) async throws -> (language: TranslationLanguage, confidence: Double) {
        if shouldFail {
            throw TranslationError.serverError(500)
        }
        
        // Simulate network delay
        try await Task.sleep(nanoseconds: 50_000_000) // 0.05 seconds
        
        return (language: mockLanguage, confidence: mockConfidence)
    }
}
