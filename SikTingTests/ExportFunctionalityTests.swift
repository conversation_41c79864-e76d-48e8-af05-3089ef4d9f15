//
//  ExportFunctionalityTests.swift
//  SikTingTests
//
//  Created by Augment Agent on 2025-07-21.
//

import XCTest
import CoreData
import PDFKit
@testable import SikTing

class ExportFunctionalityTests: XCTestCase {
    
    // MARK: - Properties
    
    var exportService: HistoryExportService!
    var storageService: HistoryStorageService!
    var testCoreDataStack: CoreDataStack!
    var testContext: NSManagedObjectContext!
    var testSessions: [HistorySession]!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        setupTestEnvironment()
        createTestData()
    }
    
    override func tearDown() {
        clearTestData()
        exportService = nil
        storageService = nil
        testContext = nil
        testCoreDataStack = nil
        testSessions = nil
        super.tearDown()
    }
    
    private func setupTestEnvironment() {
        testCoreDataStack = CoreDataStack.inMemoryStack()
        testContext = testCoreDataStack.viewContext
        storageService = HistoryStorageService(coreDataStack: testCoreDataStack)
        exportService = HistoryExportService(storageService: storageService)
    }
    
    private func createTestData() {
        testSessions = [
            createTestSession(
                title: "Team Meeting Notes",
                content: "Discussed project roadmap and quarterly goals. Action items include updating documentation and scheduling follow-up meetings.",
                language: "en-US",
                duration: 1800
            ),
            createTestSession(
                title: "Interview Recording",
                content: "Technical interview covering Swift programming, iOS development, and system design patterns. Candidate demonstrated strong problem-solving skills.",
                language: "en-US",
                duration: 3600
            ),
            createTestSession(
                title: "Spanish Lesson",
                content: "Hola, mi nombre es María. Estoy aprendiendo español y practicando conversación básica. ¿Cómo estás hoy?",
                language: "es-ES",
                duration: 2700
            )
        ]
        
        for session in testSessions {
            do {
                try storageService.saveSession(session)
            } catch {
                XCTFail("Failed to save test session: \(error)")
            }
        }
    }
    
    private func createTestSession(title: String, content: String, language: String, duration: Double) -> HistorySession {
        let session = HistorySession(context: testContext)
        session.id = UUID()
        session.title = title
        session.createdAt = Date()
        session.updatedAt = Date()
        session.language = language
        session.duration = duration
        session.isFavourite = false
        session.isSaved = false
        
        // Create entries from content
        let sentences = content.components(separatedBy: ". ")
        for (index, sentence) in sentences.enumerated() {
            let entry = HistoryEntry(context: testContext)
            entry.id = UUID()
            entry.sessionId = session.id
            entry.text = sentence + (index < sentences.count - 1 ? "." : "")
            entry.timestamp = Date().addingTimeInterval(TimeInterval(index * 10))
            entry.confidence = Double.random(in: 0.85...0.98)
            entry.isPartial = false
            entry.language = language
            entry.session = session
            session.addToEntries(entry)
        }
        
        return session
    }
    
    private func clearTestData() {
        let sessionRequest: NSFetchRequest<NSFetchRequestResult> = HistorySession.fetchRequest()
        let deleteSessionsRequest = NSBatchDeleteRequest(fetchRequest: sessionRequest)
        
        let entryRequest: NSFetchRequest<NSFetchRequestResult> = HistoryEntry.fetchRequest()
        let deleteEntriesRequest = NSBatchDeleteRequest(fetchRequest: entryRequest)
        
        do {
            try testContext.execute(deleteEntriesRequest)
            try testContext.execute(deleteSessionsRequest)
            try testContext.save()
        } catch {
            XCTFail("Failed to clear test data: \(error)")
        }
    }
    
    // MARK: - Text Export Tests
    
    func testExportSingleSessionAsText() throws {
        // Given
        let session = testSessions.first!
        
        // When
        let exportedData = try exportService.exportSession(session, format: .text)
        let exportedText = String(data: exportedData, encoding: .utf8)!
        
        // Then
        XCTAssertTrue(exportedText.contains(session.title!))
        XCTAssertTrue(exportedText.contains("Team Meeting Notes"))
        XCTAssertTrue(exportedText.contains("Discussed project roadmap"))
        XCTAssertTrue(exportedText.contains("Action items include"))
        
        // Verify metadata is included
        XCTAssertTrue(exportedText.contains("Duration:"))
        XCTAssertTrue(exportedText.contains("Language:"))
        XCTAssertTrue(exportedText.contains("Created:"))
    }
    
    func testExportMultipleSessionsAsText() throws {
        // When
        let exportedData = try exportService.exportSessions(testSessions, format: .text)
        let exportedText = String(data: exportedData, encoding: .utf8)!
        
        // Then
        for session in testSessions {
            XCTAssertTrue(exportedText.contains(session.title!))
        }
        
        // Verify sessions are separated
        XCTAssertTrue(exportedText.contains("---")) // Session separator
        
        // Verify all content is included
        XCTAssertTrue(exportedText.contains("Team Meeting Notes"))
        XCTAssertTrue(exportedText.contains("Interview Recording"))
        XCTAssertTrue(exportedText.contains("Spanish Lesson"))
    }
    
    func testTextExportFormatting() throws {
        // Given
        let session = testSessions.first!
        
        // When
        let exportedData = try exportService.exportSession(session, format: .text)
        let exportedText = String(data: exportedData, encoding: .utf8)!
        
        // Then
        let lines = exportedText.components(separatedBy: .newlines)
        
        // Verify structure
        XCTAssertTrue(lines.first?.contains("Team Meeting Notes") == true) // Title
        XCTAssertTrue(lines.contains { $0.contains("Duration:") }) // Metadata
        XCTAssertTrue(lines.contains { $0.contains("Language:") })
        XCTAssertTrue(lines.contains { $0.contains("Created:") })
        
        // Verify content formatting
        XCTAssertTrue(exportedText.contains("\n\nTranscription:\n"))
    }
    
    // MARK: - JSON Export Tests
    
    func testExportSingleSessionAsJSON() throws {
        // Given
        let session = testSessions.first!
        
        // When
        let exportedData = try exportService.exportSession(session, format: .json)
        let jsonObject = try JSONSerialization.jsonObject(with: exportedData) as! [String: Any]
        
        // Then
        XCTAssertEqual(jsonObject["title"] as? String, session.title)
        XCTAssertEqual(jsonObject["language"] as? String, session.language)
        XCTAssertEqual(jsonObject["duration"] as? Double, session.duration)
        XCTAssertNotNil(jsonObject["id"])
        XCTAssertNotNil(jsonObject["createdAt"])
        XCTAssertNotNil(jsonObject["entries"])
        
        // Verify entries structure
        let entries = jsonObject["entries"] as! [[String: Any]]
        XCTAssertGreaterThan(entries.count, 0)
        
        let firstEntry = entries.first!
        XCTAssertNotNil(firstEntry["id"])
        XCTAssertNotNil(firstEntry["text"])
        XCTAssertNotNil(firstEntry["timestamp"])
        XCTAssertNotNil(firstEntry["confidence"])
    }
    
    func testExportMultipleSessionsAsJSON() throws {
        // When
        let exportedData = try exportService.exportSessions(testSessions, format: .json)
        let jsonObject = try JSONSerialization.jsonObject(with: exportedData) as! [String: Any]
        
        // Then
        XCTAssertNotNil(jsonObject["exportDate"])
        XCTAssertNotNil(jsonObject["totalSessions"])
        XCTAssertEqual(jsonObject["totalSessions"] as? Int, testSessions.count)
        
        let sessions = jsonObject["sessions"] as! [[String: Any]]
        XCTAssertEqual(sessions.count, testSessions.count)
        
        // Verify each session has required fields
        for sessionData in sessions {
            XCTAssertNotNil(sessionData["id"])
            XCTAssertNotNil(sessionData["title"])
            XCTAssertNotNil(sessionData["language"])
            XCTAssertNotNil(sessionData["entries"])
        }
    }
    
    func testJSONExportDataIntegrity() throws {
        // Given
        let session = testSessions.first!
        
        // When
        let exportedData = try exportService.exportSession(session, format: .json)
        let jsonObject = try JSONSerialization.jsonObject(with: exportedData) as! [String: Any]
        
        // Then - Verify all data is preserved
        XCTAssertEqual(jsonObject["title"] as? String, session.title)
        XCTAssertEqual(jsonObject["language"] as? String, session.language)
        XCTAssertEqual(jsonObject["duration"] as? Double, session.duration)
        XCTAssertEqual(jsonObject["isFavourite"] as? Bool, session.isFavourite)
        XCTAssertEqual(jsonObject["isSaved"] as? Bool, session.isSaved)
        
        // Verify entries data integrity
        let entries = jsonObject["entries"] as! [[String: Any]]
        let originalEntries = session.entries?.allObjects as? [HistoryEntry] ?? []
        
        XCTAssertEqual(entries.count, originalEntries.count)
        
        for entryData in entries {
            XCTAssertNotNil(entryData["text"])
            XCTAssertNotNil(entryData["confidence"])
            XCTAssertNotNil(entryData["isPartial"])
        }
    }
    
    // MARK: - PDF Export Tests
    
    func testExportSingleSessionAsPDF() throws {
        // Given
        let session = testSessions.first!
        
        // When
        let exportedData = try exportService.exportSession(session, format: .pdf)
        let pdfDocument = PDFDocument(data: exportedData)
        
        // Then
        XCTAssertNotNil(pdfDocument)
        XCTAssertGreaterThan(pdfDocument!.pageCount, 0)
        
        // Verify content is in PDF
        let firstPage = pdfDocument!.page(at: 0)!
        let pageContent = firstPage.string!
        
        XCTAssertTrue(pageContent.contains(session.title!))
        XCTAssertTrue(pageContent.contains("Team Meeting Notes"))
        XCTAssertTrue(pageContent.contains("Duration:"))
        XCTAssertTrue(pageContent.contains("Language:"))
    }
    
    func testExportMultipleSessionsAsPDF() throws {
        // When
        let exportedData = try exportService.exportSessions(testSessions, format: .pdf)
        let pdfDocument = PDFDocument(data: exportedData)
        
        // Then
        XCTAssertNotNil(pdfDocument)
        XCTAssertGreaterThan(pdfDocument!.pageCount, 0)
        
        // Verify all sessions are included
        let fullContent = pdfDocument!.string!
        for session in testSessions {
            XCTAssertTrue(fullContent.contains(session.title!))
        }
    }
    
    func testPDFExportFormatting() throws {
        // Given
        let session = testSessions.first!
        
        // When
        let exportedData = try exportService.exportSession(session, format: .pdf)
        let pdfDocument = PDFDocument(data: exportedData)
        
        // Then
        XCTAssertNotNil(pdfDocument)
        
        let firstPage = pdfDocument!.page(at: 0)!
        let pageContent = firstPage.string!
        
        // Verify proper formatting
        XCTAssertTrue(pageContent.contains("Transcription History Export"))
        XCTAssertTrue(pageContent.contains("Session Details"))
        XCTAssertTrue(pageContent.contains("Transcription Content"))
        
        // Verify metadata formatting
        XCTAssertTrue(pageContent.contains("Duration: 30:00"))
        XCTAssertTrue(pageContent.contains("Language: en-US"))
    }
    
    // MARK: - Batch Export Tests
    
    func testBatchExportAllFormats() throws {
        // Given
        let sessions = Array(testSessions.prefix(2)) // Export first 2 sessions
        
        // When
        let textData = try exportService.exportSessions(sessions, format: .text)
        let jsonData = try exportService.exportSessions(sessions, format: .json)
        let pdfData = try exportService.exportSessions(sessions, format: .pdf)
        
        // Then
        XCTAssertGreaterThan(textData.count, 0)
        XCTAssertGreaterThan(jsonData.count, 0)
        XCTAssertGreaterThan(pdfData.count, 0)
        
        // Verify different formats produce different data
        XCTAssertNotEqual(textData, jsonData)
        XCTAssertNotEqual(jsonData, pdfData)
        XCTAssertNotEqual(textData, pdfData)
    }
    
    func testBatchExportWithFiltering() throws {
        // Given
        let favoriteSessions = testSessions.filter { $0.isFavourite }
        let englishSessions = testSessions.filter { $0.language == "en-US" }
        
        // When
        let favoriteExport = try exportService.exportSessions(favoriteSessions, format: .json)
        let englishExport = try exportService.exportSessions(englishSessions, format: .json)
        
        // Then
        let favoriteJson = try JSONSerialization.jsonObject(with: favoriteExport) as! [String: Any]
        let englishJson = try JSONSerialization.jsonObject(with: englishExport) as! [String: Any]
        
        XCTAssertEqual(favoriteJson["totalSessions"] as? Int, favoriteSessions.count)
        XCTAssertEqual(englishJson["totalSessions"] as? Int, englishSessions.count)
    }
    
    func testLargeBatchExportPerformance() throws {
        // Given - Create many sessions
        var largeSessions: [HistorySession] = []
        for i in 0..<100 {
            let session = createTestSession(
                title: "Performance Test Session \(i)",
                content: "This is test content for performance testing with session number \(i). It contains multiple sentences to simulate real transcription data.",
                language: "en-US",
                duration: Double(i * 30)
            )
            largeSessions.append(session)
            try storageService.saveSession(session)
        }
        
        // When & Then - Measure export performance
        measure {
            do {
                let exportedData = try exportService.exportSessions(largeSessions, format: .json)
                XCTAssertGreaterThan(exportedData.count, 0)
            } catch {
                XCTFail("Batch export failed: \(error)")
            }
        }
    }
    
    // MARK: - Export Options Tests
    
    func testExportWithCustomOptions() throws {
        // Given
        let session = testSessions.first!
        let options = ExportOptions(
            includeMetadata: true,
            includeTimestamps: true,
            includeConfidenceScores: true,
            dateFormat: .iso8601,
            timeFormat: .twentyFourHour
        )
        
        // When
        let exportedData = try exportService.exportSession(session, format: .json, options: options)
        let jsonObject = try JSONSerialization.jsonObject(with: exportedData) as! [String: Any]
        
        // Then
        XCTAssertNotNil(jsonObject["metadata"])
        
        let entries = jsonObject["entries"] as! [[String: Any]]
        let firstEntry = entries.first!
        
        XCTAssertNotNil(firstEntry["timestamp"])
        XCTAssertNotNil(firstEntry["confidence"])
        
        // Verify timestamp format
        let timestamp = firstEntry["timestamp"] as! String
        XCTAssertTrue(timestamp.contains("T")) // ISO 8601 format
    }
    
    func testExportWithoutOptionalData() throws {
        // Given
        let session = testSessions.first!
        let options = ExportOptions(
            includeMetadata: false,
            includeTimestamps: false,
            includeConfidenceScores: false,
            dateFormat: .short,
            timeFormat: .twelveHour
        )
        
        // When
        let exportedData = try exportService.exportSession(session, format: .json, options: options)
        let jsonObject = try JSONSerialization.jsonObject(with: exportedData) as! [String: Any]
        
        // Then
        XCTAssertNil(jsonObject["metadata"])
        
        let entries = jsonObject["entries"] as! [[String: Any]]
        let firstEntry = entries.first!
        
        XCTAssertNil(firstEntry["timestamp"])
        XCTAssertNil(firstEntry["confidence"])
        XCTAssertNotNil(firstEntry["text"]) // Text should always be included
    }
    
    // MARK: - Error Handling Tests
    
    func testExportEmptySession() throws {
        // Given
        let emptySession = HistorySession(context: testContext)
        emptySession.id = UUID()
        emptySession.title = "Empty Session"
        emptySession.createdAt = Date()
        emptySession.language = "en-US"
        emptySession.duration = 0
        // No entries added
        
        try storageService.saveSession(emptySession)
        
        // When
        let exportedData = try exportService.exportSession(emptySession, format: .json)
        let jsonObject = try JSONSerialization.jsonObject(with: exportedData) as! [String: Any]
        
        // Then
        XCTAssertEqual(jsonObject["title"] as? String, "Empty Session")
        
        let entries = jsonObject["entries"] as! [[String: Any]]
        XCTAssertEqual(entries.count, 0)
    }
    
    func testExportWithInvalidFormat() {
        // Given
        let session = testSessions.first!
        
        // When & Then
        XCTAssertThrowsError(try exportService.exportSession(session, format: .unsupported)) { error in
            XCTAssertTrue(error is ExportError)
            if let exportError = error as? ExportError {
                XCTAssertEqual(exportError, .unsupportedFormat)
            }
        }
    }
    
    func testExportWithCorruptedData() throws {
        // Given
        let session = testSessions.first!
        
        // Corrupt the session data
        session.title = nil
        session.language = nil
        
        // When & Then
        XCTAssertThrowsError(try exportService.exportSession(session, format: .json)) { error in
            XCTAssertTrue(error is ExportError)
        }
    }
    
    // MARK: - File Size and Compression Tests
    
    func testExportFileSizes() throws {
        // Given
        let session = testSessions.first!
        
        // When
        let textData = try exportService.exportSession(session, format: .text)
        let jsonData = try exportService.exportSession(session, format: .json)
        let pdfData = try exportService.exportSession(session, format: .pdf)
        
        // Then
        XCTAssertGreaterThan(textData.count, 0)
        XCTAssertGreaterThan(jsonData.count, 0)
        XCTAssertGreaterThan(pdfData.count, 0)
        
        // JSON should typically be larger than text due to structure
        XCTAssertGreaterThan(jsonData.count, textData.count)
        
        // PDF should typically be largest due to formatting
        XCTAssertGreaterThan(pdfData.count, textData.count)
    }
    
    func testExportWithCompression() throws {
        // Given
        let sessions = testSessions
        let options = ExportOptions(enableCompression: true)
        
        // When
        let compressedData = try exportService.exportSessions(sessions, format: .json, options: options)
        let uncompressedData = try exportService.exportSessions(sessions, format: .json)
        
        // Then
        XCTAssertLessThan(compressedData.count, uncompressedData.count)
        
        // Verify compressed data can be decompressed
        let decompressedData = try Data(compressedData).decompressed()
        XCTAssertEqual(decompressedData.count, uncompressedData.count)
    }
}
