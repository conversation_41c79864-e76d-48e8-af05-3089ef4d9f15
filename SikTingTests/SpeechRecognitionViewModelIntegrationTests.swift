//
//  SpeechRecognitionViewModelIntegrationTests.swift
//  SikTingTests
//
//  Created by <PERSON><PERSON> on 2025/7/18.
//

import XCTest
@testable import SikTing

/// Tests for SmartWordMerger integration with SpeechRecognitionViewModel
@MainActor
class SpeechRecognitionViewModelIntegrationTests: XCTestCase {
    
    var viewModel: SpeechRecognitionViewModel!
    
    override func setUp() {
        super.setUp()
        viewModel = SpeechRecognitionViewModel()
    }
    
    override func tearDown() {
        viewModel = nil
        super.tearDown()
    }
    
    // MARK: - Integration Tests
    
    func testOnlineModeWithWordMerging() {
        // Test that online mode applies word merging
        let firstResponse = TranscriptionResponse(
            text: "<|en|><|NEUTRAL|><|Speech|> Hello wor",
            timestamp: Date().timeIntervalSince1970,
            mode: "online",
            isFinal: false
        )
        
        let secondResponse = TranscriptionResponse(
            text: "<|en|><|NEUTRAL|><|Speech|> ld how are you",
            timestamp: Date().timeIntervalSince1970,
            mode: "online",
            isFinal: false
        )
        
        // Process first partial result
        viewModel.processTranscriptionForTesting(firstResponse)
        
        // Verify first entry was created
        XCTAssertEqual(viewModel.transcriptionEntries.count, 1)
        let firstEntry = viewModel.transcriptionEntries[0]
        XCTAssertEqual(firstEntry.displayText, "Hello wor")
        XCTAssertFalse(firstEntry.isFinal)
        
        // Process second partial result
        viewModel.processTranscriptionForTesting(secondResponse)
        
        // Verify merging occurred - "wor" + "ld" should merge to "world"
        XCTAssertEqual(viewModel.transcriptionEntries.count, 1)
        let mergedEntry = viewModel.transcriptionEntries[0]
        XCTAssertEqual(mergedEntry.displayText, "world how are you")
        XCTAssertFalse(mergedEntry.isFinal)
    }
    
    func testOfflineModeSkipsMerging() {
        // Test that offline mode skips word merging
        let offlineResponse = TranscriptionResponse(
            text: "<|en|><|NEUTRAL|><|Speech|> Hello world how are you",
            timestamp: Date().timeIntervalSince1970,
            mode: "offline",
            isFinal: true
        )
        
        // Process offline result
        viewModel.processTranscriptionForTesting(offlineResponse)
        
        // Verify entry was created without merging logic
        XCTAssertEqual(viewModel.transcriptionEntries.count, 1)
        let entry = viewModel.transcriptionEntries[0]
        XCTAssertEqual(entry.displayText, "Hello world how are you")
        XCTAssertTrue(entry.isFinal)
    }
    
    func testFinalResultClearsPreviousPartial() {
        // Test that final results clear the previous partial state
        let partialResponse = TranscriptionResponse(
            text: "<|en|><|NEUTRAL|><|Speech|> Hello wor",
            timestamp: Date().timeIntervalSince1970,
            mode: "online",
            isFinal: false
        )
        
        let finalResponse = TranscriptionResponse(
            text: "<|en|><|NEUTRAL|><|Speech|> Hello world complete",
            timestamp: Date().timeIntervalSince1970,
            mode: "online",
            isFinal: true
        )
        
        // Process partial result
        viewModel.processTranscriptionForTesting(partialResponse)
        XCTAssertEqual(viewModel.transcriptionEntries.count, 1)
        
        // Process final result
        viewModel.processTranscriptionForTesting(finalResponse)
        
        // Should still have one entry, but updated with final text
        XCTAssertEqual(viewModel.transcriptionEntries.count, 1)
        let finalEntry = viewModel.transcriptionEntries[0]
        XCTAssertEqual(finalEntry.displayText, "Hello world complete")
        XCTAssertTrue(finalEntry.isFinal)
    }
    
    func testChineseLanguageSkipsMerging() {
        // Test that Chinese language skips merging
        let chineseResponse = TranscriptionResponse(
            text: "<|zh|><|NEUTRAL|><|Speech|> 你好世界",
            timestamp: Date().timeIntervalSince1970,
            mode: "online",
            isFinal: false
        )
        
        // Process Chinese result
        viewModel.processTranscriptionForTesting(chineseResponse)
        
        // Verify entry was created
        XCTAssertEqual(viewModel.transcriptionEntries.count, 1)
        let entry = viewModel.transcriptionEntries[0]
        XCTAssertEqual(entry.displayText, "你好世界")
        XCTAssertFalse(entry.isFinal)
    }
    
    func testStateResetOnStopRecording() {
        // Test that stopping recording resets merging state
        let partialResponse = TranscriptionResponse(
            text: "<|en|><|NEUTRAL|><|Speech|> Hello wor",
            timestamp: Date().timeIntervalSince1970,
            mode: "online",
            isFinal: false
        )
        
        // Process partial result
        viewModel.processTranscriptionForTesting(partialResponse)
        XCTAssertEqual(viewModel.transcriptionEntries.count, 1)
        
        // Stop recording (this should reset state)
        viewModel.stopRecording()
        
        // Verify transcriptions are cleared
        XCTAssertEqual(viewModel.transcriptionEntries.count, 0)
    }
    
    func testStateResetOnClearTranscriptions() {
        // Test that clearing transcriptions resets merging state
        let partialResponse = TranscriptionResponse(
            text: "<|en|><|NEUTRAL|><|Speech|> Hello wor",
            timestamp: Date().timeIntervalSince1970,
            mode: "online",
            isFinal: false
        )
        
        // Process partial result
        viewModel.processTranscriptionForTesting(partialResponse)
        XCTAssertEqual(viewModel.transcriptionEntries.count, 1)
        
        // Clear transcriptions
        viewModel.clearTranscriptions()
        
        // Verify transcriptions are cleared
        XCTAssertEqual(viewModel.transcriptionEntries.count, 0)
    }
}