//
//  HistorySearchViewModelTests.swift
//  SikTingTests
//
//  Created by Augment Agent on 2025-07-21.
//

import XCTest
import CoreData
import Combine
@testable import SikTing

class HistorySearchViewModelTests: XCTestCase {
    
    // MARK: - Properties
    
    var viewModel: HistorySearchViewModel!
    var mockStorageService: MockHistoryStorageService!
    var cancellables: Set<AnyCancellable>!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        mockStorageService = MockHistoryStorageService()
        cancellables = Set<AnyCancellable>()
        
        viewModel = HistorySearchViewModel(storageService: mockStorageService)
    }
    
    override func tearDown() {
        cancellables.removeAll()
        viewModel = nil
        mockStorageService = nil
        super.tearDown()
    }
    
    // MARK: - Search Functionality Tests
    
    func testSearchWithQuery() {
        // Given
        let expectation = XCTestExpectation(description: "Search completed")
        let testSessions = createTestSessions()
        mockStorageService.searchResults = testSessions.filter { $0.title?.contains("meeting") == true }
        
        // When
        viewModel.$searchResults
            .dropFirst() // Skip initial empty value
            .sink { results in
                // Then
                XCTAssertEqual(results.count, 1)
                XCTAssertEqual(results.first?.title, "Team Meeting Notes")
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        viewModel.searchQuery = "meeting"
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    func testSearchWithEmptyQuery() {
        // Given
        let expectation = XCTestExpectation(description: "Search cleared")
        
        // When
        viewModel.$searchResults
            .dropFirst()
            .sink { results in
                // Then
                XCTAssertTrue(results.isEmpty)
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        viewModel.searchQuery = ""
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    func testSearchDebouncing() {
        // Given
        let expectation = XCTestExpectation(description: "Search debounced")
        expectation.expectedFulfillmentCount = 1 // Should only search once after debounce
        
        let testSessions = createTestSessions()
        mockStorageService.searchResults = testSessions
        
        var searchCount = 0
        viewModel.$searchResults
            .dropFirst()
            .sink { _ in
                searchCount += 1
                if searchCount == 1 {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When - Rapid typing simulation
        viewModel.searchQuery = "m"
        viewModel.searchQuery = "me"
        viewModel.searchQuery = "mee"
        viewModel.searchQuery = "meet"
        viewModel.searchQuery = "meeting"
        
        wait(for: [expectation], timeout: 2.0)
        
        // Then - Should only have searched once due to debouncing
        XCTAssertEqual(searchCount, 1)
    }
    
    func testSearchLoading() {
        // Given
        let expectation = XCTestExpectation(description: "Loading state tracked")
        mockStorageService.delaySearchResponse = true
        
        var loadingStates: [Bool] = []
        
        // When
        viewModel.$isSearching
            .sink { isSearching in
                loadingStates.append(isSearching)
                if loadingStates.count == 3 { // false -> true -> false
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        viewModel.searchQuery = "test"
        
        // Simulate completion
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.mockStorageService.completeDelayedSearch()
        }
        
        wait(for: [expectation], timeout: 2.0)
        
        // Then
        XCTAssertEqual(loadingStates, [false, true, false])
    }
    
    // MARK: - Filter Tests
    
    func testLanguageFilter() {
        // Given
        let testSessions = createTestSessions()
        mockStorageService.searchResults = testSessions
        viewModel.searchQuery = "test"
        
        // When
        viewModel.selectedLanguage = "es-ES"
        
        // Then
        let spanishSessions = viewModel.filteredResults.filter { $0.language == "es-ES" }
        XCTAssertEqual(spanishSessions.count, 1)
        XCTAssertEqual(spanishSessions.first?.title, "Spanish Lesson")
    }
    
    func testDateRangeFilter() {
        // Given
        let testSessions = createTestSessions()
        mockStorageService.searchResults = testSessions
        viewModel.searchQuery = "test"
        
        let now = Date()
        let yesterday = now.addingTimeInterval(-86400)
        
        // When
        viewModel.dateRange = DateRange(start: yesterday, end: now)
        
        // Then
        let recentSessions = viewModel.filteredResults.filter { session in
            guard let createdAt = session.createdAt else { return false }
            return createdAt >= yesterday && createdAt <= now
        }
        XCTAssertGreaterThan(recentSessions.count, 0)
    }
    
    func testDurationFilter() {
        // Given
        let testSessions = createTestSessions()
        mockStorageService.searchResults = testSessions
        viewModel.searchQuery = "test"
        
        // When
        viewModel.minDuration = 300 // 5 minutes
        viewModel.maxDuration = 1800 // 30 minutes
        
        // Then
        let filteredSessions = viewModel.filteredResults.filter { session in
            session.duration >= 300 && session.duration <= 1800
        }
        XCTAssertGreaterThan(filteredSessions.count, 0)
    }
    
    func testFavoriteFilter() {
        // Given
        let testSessions = createTestSessions()
        testSessions[0].isFavourite = true
        mockStorageService.searchResults = testSessions
        viewModel.searchQuery = "test"
        
        // When
        viewModel.showFavoritesOnly = true
        
        // Then
        let favoriteSessions = viewModel.filteredResults.filter { $0.isFavourite }
        XCTAssertEqual(favoriteSessions.count, 1)
        XCTAssertEqual(favoriteSessions.first?.title, "Team Meeting Notes")
    }
    
    // MARK: - Sort Tests
    
    func testSortByDate() {
        // Given
        let testSessions = createTestSessions()
        mockStorageService.searchResults = testSessions
        viewModel.searchQuery = "test"
        
        // When
        viewModel.sortOption = .date
        viewModel.sortAscending = false
        
        // Then
        let sortedResults = viewModel.sortedResults
        XCTAssertGreaterThan(sortedResults.count, 1)
        
        // Verify descending date order
        for i in 0..<(sortedResults.count - 1) {
            let current = sortedResults[i].createdAt ?? Date.distantPast
            let next = sortedResults[i + 1].createdAt ?? Date.distantPast
            XCTAssertGreaterThanOrEqual(current, next)
        }
    }
    
    func testSortByTitle() {
        // Given
        let testSessions = createTestSessions()
        mockStorageService.searchResults = testSessions
        viewModel.searchQuery = "test"
        
        // When
        viewModel.sortOption = .title
        viewModel.sortAscending = true
        
        // Then
        let sortedResults = viewModel.sortedResults
        XCTAssertGreaterThan(sortedResults.count, 1)
        
        // Verify ascending title order
        for i in 0..<(sortedResults.count - 1) {
            let current = sortedResults[i].title ?? ""
            let next = sortedResults[i + 1].title ?? ""
            XCTAssertLessThanOrEqual(current, next)
        }
    }
    
    func testSortByDuration() {
        // Given
        let testSessions = createTestSessions()
        mockStorageService.searchResults = testSessions
        viewModel.searchQuery = "test"
        
        // When
        viewModel.sortOption = .duration
        viewModel.sortAscending = false
        
        // Then
        let sortedResults = viewModel.sortedResults
        XCTAssertGreaterThan(sortedResults.count, 1)
        
        // Verify descending duration order
        for i in 0..<(sortedResults.count - 1) {
            let current = sortedResults[i].duration
            let next = sortedResults[i + 1].duration
            XCTAssertGreaterThanOrEqual(current, next)
        }
    }
    
    // MARK: - Search History Tests
    
    func testSearchHistoryTracking() {
        // Given
        let queries = ["meeting", "interview", "lecture"]
        
        // When
        for query in queries {
            viewModel.searchQuery = query
            viewModel.addToSearchHistory(query)
        }
        
        // Then
        XCTAssertEqual(viewModel.searchHistory.count, 3)
        XCTAssertTrue(viewModel.searchHistory.contains("meeting"))
        XCTAssertTrue(viewModel.searchHistory.contains("interview"))
        XCTAssertTrue(viewModel.searchHistory.contains("lecture"))
    }
    
    func testSearchHistoryLimit() {
        // Given
        let maxHistoryCount = 10
        
        // When
        for i in 0..<15 {
            viewModel.addToSearchHistory("query \(i)")
        }
        
        // Then
        XCTAssertLessThanOrEqual(viewModel.searchHistory.count, maxHistoryCount)
    }
    
    func testClearSearchHistory() {
        // Given
        viewModel.addToSearchHistory("test query")
        XCTAssertFalse(viewModel.searchHistory.isEmpty)
        
        // When
        viewModel.clearSearchHistory()
        
        // Then
        XCTAssertTrue(viewModel.searchHistory.isEmpty)
    }
    
    // MARK: - Error Handling Tests
    
    func testSearchError() {
        // Given
        let expectation = XCTestExpectation(description: "Error handled")
        mockStorageService.shouldThrowSearchError = true
        
        // When
        viewModel.$errorMessage
            .dropFirst()
            .sink { errorMessage in
                // Then
                XCTAssertNotNil(errorMessage)
                XCTAssertFalse(self.viewModel.isSearching)
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        viewModel.searchQuery = "error test"
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    // MARK: - Performance Tests
    
    func testSearchPerformance() {
        // Given
        let largeSessions = (0..<1000).map { index in
            let session = HistorySession()
            session.id = UUID()
            session.title = "Performance Test Session \(index)"
            session.createdAt = Date()
            session.language = "en-US"
            session.duration = Double(index)
            return session
        }
        mockStorageService.searchResults = largeSessions
        
        // When & Then
        measure {
            viewModel.searchQuery = "Performance"
        }
    }
    
    // MARK: - Helper Methods
    
    private func createTestSessions() -> [HistorySession] {
        let sessions = [
            createSession(title: "Team Meeting Notes", language: "en-US", duration: 1800, createdAt: Date()),
            createSession(title: "Interview Recording", language: "en-US", duration: 3600, createdAt: Date().addingTimeInterval(-3600)),
            createSession(title: "Spanish Lesson", language: "es-ES", duration: 2700, createdAt: Date().addingTimeInterval(-7200)),
            createSession(title: "Project Discussion", language: "en-US", duration: 900, createdAt: Date().addingTimeInterval(-10800))
        ]
        return sessions
    }
    
    private func createSession(title: String, language: String, duration: Double, createdAt: Date) -> HistorySession {
        let session = HistorySession()
        session.id = UUID()
        session.title = title
        session.language = language
        session.duration = duration
        session.createdAt = createdAt
        session.isFavourite = false
        session.isSaved = false
        return session
    }
}

// MARK: - Mock Service Extension

extension MockHistoryStorageService {
    var searchResults: [HistorySession] = []
    var shouldThrowSearchError = false
    var delaySearchResponse = false
    private var delayedSearchCompletion: (() -> Void)?
    
    func searchSessions(query: String) throws -> [HistorySession] {
        if shouldThrowSearchError {
            throw HistoryError.searchFailed
        }
        
        if delaySearchResponse {
            return []
        }
        
        return searchResults.filter { session in
            session.title?.lowercased().contains(query.lowercased()) == true
        }
    }
    
    func completeDelayedSearch() {
        delayedSearchCompletion?()
    }
}
