//
//  FloatingHistoryViewModelTests.swift
//  SikTingTests
//
//  Created by Augment Agent on 2025/1/25.
//

import XCTest
import Combine
@testable import SikTing

final class FloatingHistoryViewModelTests: XCTestCase {
    
    var viewModel: FloatingHistoryViewModel!
    var mockHistoryService: MockHistoryStorageService!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        mockHistoryService = MockHistoryStorageService()
        viewModel = FloatingHistoryViewModel(historyStorageService: mockHistoryService)
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        cancellables.removeAll()
        viewModel = nil
        mockHistoryService = nil
        super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testFloatingHistoryViewModelInitialization() {
        // Given & When
        let viewModel = FloatingHistoryViewModel()
        
        // Then
        XCTAssertEqual(viewModel.selectedTab, .recents)
        XCTAssertTrue(viewModel.filteredSessions.isEmpty)
        XCTAssertFalse(viewModel.isFilteringInProgress)
        XCTAssertFalse(viewModel.isShowingEmptyState)
    }
    
    // MARK: - Tab Selection Tests
    
    func testTabSelectionUpdatesFilteredSessions() {
        // Given
        let mockSessions = createMockSessions()
        mockHistoryService.mockSessions = mockSessions
        viewModel.loadSessions()
        
        let expectation = XCTestExpectation(description: "Filtered sessions should update")
        
        viewModel.$filteredSessions
            .dropFirst() // Skip initial value
            .sink { sessions in
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // When
        viewModel.selectedTab = .favorites
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        
        // Should only contain favorite sessions
        let favoriteSessions = viewModel.filteredSessions.filter { $0.isFavorite }
        XCTAssertEqual(viewModel.filteredSessions.count, favoriteSessions.count)
    }
    
    func testRecentsTabFiltering() {
        // Given
        let mockSessions = createMockSessions()
        mockHistoryService.mockSessions = mockSessions
        viewModel.loadSessions()
        
        // When
        viewModel.selectedTab = .recents
        
        // Then
        // Should contain all sessions (recents shows all)
        XCTAssertEqual(viewModel.filteredSessions.count, mockSessions.count)
    }
    
    func testFavoritesTabFiltering() {
        // Given
        let mockSessions = createMockSessions()
        mockHistoryService.mockSessions = mockSessions
        viewModel.loadSessions()
        
        // When
        viewModel.selectedTab = .favorites
        
        // Then
        // Should only contain favorite sessions
        let expectedCount = mockSessions.filter { $0.isFavorite }.count
        XCTAssertEqual(viewModel.filteredSessions.count, expectedCount)
    }
    
    func testSavedTabFiltering() {
        // Given
        let mockSessions = createMockSessions()
        mockHistoryService.mockSessions = mockSessions
        viewModel.loadSessions()
        
        // When
        viewModel.selectedTab = .saved
        
        // Then
        // Should only contain saved sessions
        let expectedCount = mockSessions.filter { $0.isSaved }.count
        XCTAssertEqual(viewModel.filteredSessions.count, expectedCount)
    }
    
    // MARK: - Session Action Tests
    
    func testToggleFavorite() {
        // Given
        let mockSessions = createMockSessions()
        mockHistoryService.mockSessions = mockSessions
        viewModel.loadSessions()
        
        let session = mockSessions.first!
        let initialFavoriteState = session.isFavorite
        
        // When
        viewModel.toggleFavorite(for: session)
        
        // Then
        XCTAssertEqual(session.isFavorite, !initialFavoriteState)
        XCTAssertTrue(mockHistoryService.updateSessionCalled)
    }
    
    func testToggleSaved() {
        // Given
        let mockSessions = createMockSessions()
        mockHistoryService.mockSessions = mockSessions
        viewModel.loadSessions()
        
        let session = mockSessions.first!
        let initialSavedState = session.isSaved
        
        // When
        viewModel.toggleSaved(for: session)
        
        // Then
        XCTAssertEqual(session.isSaved, !initialSavedState)
        XCTAssertTrue(mockHistoryService.updateSessionCalled)
    }
    
    func testDeleteSession() {
        // Given
        let mockSessions = createMockSessions()
        mockHistoryService.mockSessions = mockSessions
        viewModel.loadSessions()
        
        let session = mockSessions.first!
        let initialCount = viewModel.allSessions.count
        
        // When
        viewModel.deleteSession(session)
        
        // Then
        XCTAssertEqual(viewModel.allSessions.count, initialCount - 1)
        XCTAssertTrue(mockHistoryService.deleteSessionCalled)
    }
    
    // MARK: - Empty State Tests
    
    func testEmptyStateForRecentsTab() {
        // Given
        mockHistoryService.mockSessions = []
        viewModel.loadSessions()
        
        // When
        viewModel.selectedTab = .recents
        
        // Then
        XCTAssertTrue(viewModel.isShowingEmptyState)
        XCTAssertFalse(viewModel.hasSessionsForCurrentTab)
    }
    
    func testEmptyStateForFavoritesTab() {
        // Given
        let mockSessions = createMockSessions(favoriteCount: 0)
        mockHistoryService.mockSessions = mockSessions
        viewModel.loadSessions()
        
        // When
        viewModel.selectedTab = .favorites
        
        // Then
        XCTAssertTrue(viewModel.isShowingEmptyState)
        XCTAssertFalse(viewModel.hasSessionsForCurrentTab)
    }
    
    func testEmptyStateForSavedTab() {
        // Given
        let mockSessions = createMockSessions(savedCount: 0)
        mockHistoryService.mockSessions = mockSessions
        viewModel.loadSessions()
        
        // When
        viewModel.selectedTab = .saved
        
        // Then
        XCTAssertTrue(viewModel.isShowingEmptyState)
        XCTAssertFalse(viewModel.hasSessionsForCurrentTab)
    }
    
    // MARK: - Performance Optimization Tests
    
    func testVisibleRangeUpdate() {
        // Given
        let mockSessions = createMockSessions(count: 100)
        mockHistoryService.mockSessions = mockSessions
        viewModel.loadSessions()
        
        // When
        viewModel.updateVisibleRange(startIndex: 10, endIndex: 20)
        
        // Then
        XCTAssertEqual(viewModel.visibleRange, 10..<20)
    }
    
    func testLazyLoadingOptimization() {
        // Given
        let mockSessions = createMockSessions(count: 1000)
        mockHistoryService.mockSessions = mockSessions
        viewModel.loadSessions()
        
        // When
        let shouldLoad = viewModel.shouldLoadSession(at: 5)
        let shouldNotLoad = viewModel.shouldLoadSession(at: 500)
        
        // Then
        XCTAssertTrue(shouldLoad) // Within threshold
        // shouldNotLoad depends on optimization level
    }
    
    // MARK: - Search Integration Tests
    
    func testSearchActivation() {
        // Given
        let mockSessions = createMockSessions()
        mockHistoryService.mockSessions = mockSessions
        viewModel.loadSessions()
        
        // When
        viewModel.isSearchActive = true
        
        // Then
        XCTAssertTrue(viewModel.isSearchActive)
    }
    
    func testSearchResultsFiltering() {
        // Given
        let mockSessions = createMockSessions()
        mockHistoryService.mockSessions = mockSessions
        viewModel.loadSessions()
        
        viewModel.searchResults = [mockSessions.first!]
        viewModel.isSearchActive = true
        
        // When
        viewModel.selectedTab = .favorites
        
        // Then
        // Should filter search results, not all sessions
        XCTAssertEqual(viewModel.filteredSessions.count, 1)
    }
    
    // MARK: - Animation Integration Tests
    
    func testAnimationManagerIntegration() {
        // Given & When
        let animation = viewModel.getOptimizedAnimation(for: .tabSwitch)
        
        // Then
        XCTAssertNotNil(animation)
    }
    
    // MARK: - Performance Tests
    
    func testFilteringPerformance() {
        // Given
        let largeMockSessions = createMockSessions(count: 1000)
        mockHistoryService.mockSessions = largeMockSessions
        viewModel.loadSessions()
        
        // When & Then
        measure {
            viewModel.selectedTab = .favorites
            viewModel.selectedTab = .saved
            viewModel.selectedTab = .recents
        }
    }
    
    // MARK: - Helper Methods
    
    private func createMockSessions(count: Int = 10, favoriteCount: Int = 3, savedCount: Int = 2) -> [HistorySession] {
        var sessions: [HistorySession] = []
        
        for i in 0..<count {
            let session = HistorySession(context: mockHistoryService.context)
            session.id = UUID()
            session.title = "Session \(i)"
            session.createdAt = Date().addingTimeInterval(-Double(i * 3600)) // 1 hour apart
            session.isFavorite = i < favoriteCount
            session.isSaved = i < savedCount
            sessions.append(session)
        }
        
        return sessions
    }
}

// MARK: - Mock History Storage Service

class MockHistoryStorageService: HistoryStorageService {
    var mockSessions: [HistorySession] = []
    var updateSessionCalled = false
    var deleteSessionCalled = false
    var saveContextCalled = false
    
    override func fetchAllSessions() -> [HistorySession] {
        return mockSessions
    }
    
    override func updateSession(_ session: HistorySession) {
        updateSessionCalled = true
        super.updateSession(session)
    }
    
    override func deleteSession(_ session: HistorySession) {
        deleteSessionCalled = true
        if let index = mockSessions.firstIndex(of: session) {
            mockSessions.remove(at: index)
        }
    }
    
    override func saveContext() {
        saveContextCalled = true
    }
}
