//
//  SmartWordMergerComprehensiveTests.swift
//  SikTingTests
//
//  Created by <PERSON><PERSON> on 2025/7/19.
//  Consolidated test suite for SmartWordMerger functionality
//

import XCTest
@testable import SikTing

/// Comprehensive test suite for SmartWordMerger that consolidates all testing scenarios
class SmartWordMergerComprehensiveTests: XCTestCase {
    
    var smartWordMerger: SmartWordMerger!
    
    override func setUp() {
        super.setUp()
        let config = MergingConfiguration(
            enableWordMerging: true,
            enableCaching: false,
            enableLanguageDetection: true,
            enablePerformanceLogging: false,
            enablePerformanceMonitoring: false,
            maxCacheSize: 100,
            dictionaryTimeout: 1.0,
            maxTokenLength: 50,
            performanceReportingInterval: 300.0,
            fallbackToSpacing: true,
            aggressiveMerging: false,
            preserveOriginalSpacing: false,
            enabledLanguages: [.english],
            chineseWordSegmentation: false,
            japaneseWordSegmentation: false,
            showPerformanceWarnings: false,
            enableDebugLogging: false
        )
        smartWordMerger = SmartWordMerger(configuration: config)
    }
    
    override func tearDown() {
        smartWordMerger = nil
        super.tearDown()
    }
    
    // MARK: - Basic Functionality Tests

    func testBasicWordMerging() {
        let testCases = [
            // Test merging within current partial (new algorithm)
            (previous: nil, current: "some thing", expected: "something"),
            (previous: nil, current: "any where", expected: "anywhere"),
            (previous: nil, current: "every one", expected: "everyone"),
            (previous: nil, current: "some body", expected: "somebody"),
            (previous: nil, current: "any thing", expected: "anything"),
            (previous: nil, current: "every thing", expected: "everything"),

            // Test with previous partial (should still work)
            (previous: "hello", current: "some thing", expected: "something"),
            (previous: "world", current: "any where", expected: "anywhere"),
        ]

        for (previous, current, expected) in testCases {
            let result = smartWordMerger.mergePartialResults(
                previousPartial: previous,
                currentPartial: current,
                language: .english
            )
            XCTAssertEqual(result, expected,
                          "Failed to merge '\(previous ?? "nil")' + '\(current)' → expected '\(expected)', got '\(result)'")
        }
    }

    func testRealWorldServerLogScenarios() {
        // Test cases based on actual server logs from speech recognition
        // These test intra-partial merging (within current partial) which is what our new algorithm does
        let realWorldTestCases: [(previous: String?, current: String, expected: String)] = [
            // Simulated scenarios based on log patterns - testing within current partial
            (previous: nil, current: "some body who is", expected: "somebody who is"),
            (previous: nil, current: "every dindinner par", expected: "every dindinner par"), // No valid merge
            (previous: nil, current: "any thing you want", expected: "anything you want"),
            (previous: nil, current: "some where in the world", expected: "somewhere in the world"),
            (previous: nil, current: "every one should know", expected: "everyone should know"),

            // Multiple merges in one text
            (previous: nil, current: "some thing any where", expected: "something anywhere"),
            (previous: nil, current: "every one some how", expected: "everyone somehow"),

            // Real patterns that might occur in speech recognition
            (previous: nil, current: "in to the house", expected: "into the house"),
            (previous: nil, current: "up stairs to bed", expected: "upstairs to bed"),
            (previous: nil, current: "some times I think", expected: "sometimes I think"),
            (previous: nil, current: "any way let's go", expected: "anyway let's go"),

            // Test that non-mergeable words are left alone
            (previous: nil, current: "hello world", expected: "hello world"),
            (previous: nil, current: "good morning", expected: "good morning"),
            (previous: nil, current: "thank you", expected: "thank you"),
        ]

        for (previous, current, expected) in realWorldTestCases {
            let result = smartWordMerger.mergePartialResults(
                previousPartial: previous,
                currentPartial: current,
                language: .english
            )
            XCTAssertEqual(result, expected,
                          "Real-world scenario failed: '\(previous ?? "nil")' + '\(current)' → expected '\(expected)', got '\(result)'")
        }
    }

    func testNewCrossPartialMerging() {
        // Test the new approach: merge last token of previous + first token of current
        smartWordMerger.resetState() // Start fresh

        let testCases: [(previous: String?, current: String, expected: String)] = [
            // Test cases where last + first should merge (FULL SENTENCE RECONSTRUCTION)
            (previous: "I want some", current: "thing else", expected: "I want something else"), // "some" + "thing" = "something"
            (previous: "any", current: "where you go", expected: "anywhere you go"), // "any" + "where" = "anywhere"
            (previous: "every", current: "one knows", expected: "everyone knows"), // "every" + "one" = "everyone"
            (previous: "i buil", current: "d mobile consum", expected: "i build mobile consum"), // "buil" + "d" = "build"

            // Test cases where last + first should NOT merge (add space between full sentences)
            (previous: "well million", current: "dollars every", expected: "well million dollars every"), // "million" + "dollars" ≠ valid word
            (previous: "hello", current: "world today", expected: "hello world today"), // "hello" + "world" ≠ valid word
            (previous: "I am", current: "happy now", expected: "I am happy now"), // "am" + "happy" ≠ valid word

            // Edge cases
            (previous: nil, current: "something", expected: "something"), // No previous
            (previous: "", current: "anything", expected: "anything"), // Empty previous
            (previous: "hello", current: "", expected: "hello"), // Empty current
        ]

        for (previous, current, expected) in testCases {
            smartWordMerger.resetState() // Reset for each test
            let result = smartWordMerger.mergePartialResults(
                previousPartial: previous,
                currentPartial: current,
                language: .english
            )
            XCTAssertEqual(result, expected,
                          "Cross-partial merging failed: '\(previous ?? "nil")' + '\(current)' → expected '\(expected)', got '\(result)'")
        }
    }

    func testRealWorldServerLogScenario() {
        // Test the exact scenarios from the server logs with corrected expectations
        smartWordMerger.resetState()

        // Scenario 1: "well million" + "dollars every mon"
        // Expected: "well million dollars every mon" (no merge, space added between full sentences)
        let result1 = smartWordMerger.mergePartialResults(
            previousPartial: "well million",
            currentPartial: "dollars every mon",
            language: .english
        )
        XCTAssertEqual(result1, "well million dollars every mon",
                      "Real server log scenario 1 failed: expected 'well million dollars every mon', got '\(result1)'")

        // Scenario 2: "i buil" + "d mobile consum"
        // Expected: "i build mobile consum" (merge successful: "buil" + "d" = "build")
        let result2 = smartWordMerger.mergePartialResults(
            previousPartial: "i buil",
            currentPartial: "d mobile consum",
            language: .english
        )
        XCTAssertEqual(result2, "i build mobile consum",
                      "Real server log scenario 2 failed: expected 'i build mobile consum', got '\(result2)'")

        // Scenario 3: "i build mobile consum" + "ers specifically"
        // Expected: "i build mobile consumers specifically" (merge successful: "consum" + "ers" = "consumers")
        let result3 = smartWordMerger.mergePartialResults(
            previousPartial: "i build mobile consum",
            currentPartial: "ers specifically",
            language: .english
        )
        XCTAssertEqual(result3, "i build mobile consumers specifically",
                      "Real server log scenario 3 failed: expected 'i build mobile consumers specifically', got '\(result3)'")

        // Scenario 4: "take advice from some" + "body who is"
        // Expected: "take advice from somebody who is" (merge successful: "some" + "body" = "somebody")
        let result4 = smartWordMerger.mergePartialResults(
            previousPartial: "take advice from some",
            currentPartial: "body who is",
            language: .english
        )
        XCTAssertEqual(result4, "take advice from somebody who is",
                      "Real server log scenario 4 failed: expected 'take advice from somebody who is', got '\(result4)'")
    }

    func testNonEnglishLanguageAppending() {
        // Test that non-English languages (Cantonese/Mandarin) don't add spaces
        smartWordMerger.resetState()

        let testCases: [(previous: String?, current: String, language: RecognizedLanguage, expected: String)] = [
            // Cantonese - no spaces should be added
            (previous: "我想要", current: "一些東西", language: .cantonese, expected: "我想要一些東西"),
            (previous: "今天天氣", current: "很好", language: .cantonese, expected: "今天天氣很好"),

            // Chinese (Mandarin) - no spaces should be added
            (previous: "我正在", current: "学习中文", language: .chinese, expected: "我正在学习中文"),
            (previous: "这是", current: "一个测试", language: .chinese, expected: "这是一个测试"),

            // Edge cases
            (previous: nil, current: "测试", language: .cantonese, expected: "测试"),
            (previous: "测试", current: "", language: .chinese, expected: "测试"),
        ]

        for (previous, current, language, expected) in testCases {
            smartWordMerger.resetState()
            let result = smartWordMerger.mergePartialResults(
                previousPartial: previous,
                currentPartial: current,
                language: language
            )
            XCTAssertEqual(result, expected,
                          "Non-English language appending failed for \(language): '\(previous ?? "nil")' + '\(current)' → expected '\(expected)', got '\(result)'")
        }
    }

    func testEnhancedDictionaryPatterns() {
        let testCases = [
            // Some- compounds
            (previous: "some", current: "some where", expected: "somewhere"),
            (previous: "some", current: "some time", expected: "sometime"),
            (previous: "some", current: "some times", expected: "sometimes"),
            (previous: "some", current: "some what", expected: "somewhat"),
            (previous: "some", current: "some day", expected: "someday"),
            (previous: "some", current: "some how", expected: "somehow"),

            // Any- compounds
            (previous: "any", current: "any body", expected: "anybody"),
            (previous: "any", current: "any time", expected: "anytime"),
            (previous: "any", current: "any way", expected: "anyway"),
            (previous: "any", current: "any place", expected: "anyplace"),
            (previous: "any", current: "any how", expected: "anyhow"),
            (previous: "any", current: "any more", expected: "anymore"),

            // Every- compounds
            (previous: "every", current: "every where", expected: "everywhere"),
            (previous: "every", current: "every day", expected: "everyday"),
            (previous: "every", current: "every time", expected: "everytime"),
            (previous: "every", current: "every place", expected: "everyplace"),

            // No- compounds
            (previous: "no", current: "no thing", expected: "nothing"),
            (previous: "no", current: "no body", expected: "nobody"),
            (previous: "no", current: "no where", expected: "nowhere"),
            (previous: "no", current: "no one", expected: "noone"),

            // Preposition compounds
            (previous: "in", current: "in to", expected: "into"),
            (previous: "on", current: "on to", expected: "onto"),
            (previous: "up", current: "up on", expected: "upon"),
            (previous: "with", current: "with in", expected: "within"),
            (previous: "with", current: "with out", expected: "without"),

            // Direction compounds
            (previous: "up", current: "up stairs", expected: "upstairs"),
            (previous: "down", current: "down stairs", expected: "downstairs"),
            (previous: "back", current: "back ground", expected: "background"),
            (previous: "back", current: "back yard", expected: "backyard"),
            (previous: "front", current: "front yard", expected: "frontyard"),

            // Way compounds
            (previous: "side", current: "side walk", expected: "sidewalk"),
            (previous: "high", current: "high way", expected: "highway"),
            (previous: "free", current: "free way", expected: "freeway"),
            (previous: "drive", current: "drive way", expected: "driveway"),
            (previous: "door", current: "door way", expected: "doorway"),
            (previous: "hall", current: "hall way", expected: "hallway")
        ]

        for (previous, current, expected) in testCases {
            let result = smartWordMerger.mergePartialResults(
                previousPartial: previous,
                currentPartial: current,
                language: .english
            )
            XCTAssertEqual(result, expected,
                          "Enhanced dictionary failed to merge '\(previous)' + '\(current)' → expected '\(expected)', got '\(result)'")
        }
    }
    
    func testNoMergingWhenInvalid() {
        let testCases = [
            (previous: "hello", current: "hello world", expected: "hello world"),
            (previous: "test", current: "test case", expected: "test case"),
            (previous: "random", current: "random text", expected: "random text")
        ]
        
        for (previous, current, expected) in testCases {
            let result = smartWordMerger.mergePartialResults(
                previousPartial: previous,
                currentPartial: current,
                language: .english
            )
            XCTAssertEqual(result, expected,
                          "Should not merge '\(previous)' + '\(current)' → expected '\(expected)', got '\(result)'")
        }
    }
    
    func testEdgeCases() {
        // Empty inputs
        XCTAssertEqual(
            smartWordMerger.mergePartialResults(previousPartial: nil, currentPartial: "hello", language: .english),
            "hello"
        )
        
        XCTAssertEqual(
            smartWordMerger.mergePartialResults(previousPartial: "", currentPartial: "hello", language: .english),
            "hello"
        )
        
        // Single word current
        XCTAssertEqual(
            smartWordMerger.mergePartialResults(previousPartial: "some", currentPartial: "thing", language: .english),
            "thing"
        )
        
        // Multiple words in result
        XCTAssertEqual(
            smartWordMerger.mergePartialResults(
                previousPartial: "some", 
                currentPartial: "some thing else", 
                language: .english
            ),
            "something else"
        )
    }
    
    // MARK: - Configuration Tests
    
    func testDisabledWordMerging() {
        let disabledConfig = MergingConfiguration(
            enableWordMerging: false,
            enableCaching: false,
            enableLanguageDetection: true,
            enablePerformanceLogging: false,
            enablePerformanceMonitoring: false,
            maxCacheSize: 100,
            dictionaryTimeout: 1.0,
            maxTokenLength: 50,
            performanceReportingInterval: 300.0,
            fallbackToSpacing: true,
            aggressiveMerging: false,
            preserveOriginalSpacing: false,
            enabledLanguages: [.english],
            chineseWordSegmentation: false,
            japaneseWordSegmentation: false,
            showPerformanceWarnings: false,
            enableDebugLogging: false
        )
        
        let disabledMerger = SmartWordMerger(configuration: disabledConfig)
        
        let result = disabledMerger.mergePartialResults(
            previousPartial: "some",
            currentPartial: "some thing",
            language: .english
        )
        
        XCTAssertEqual(result, "some thing", "Should not merge when word merging is disabled")
    }
    
    // MARK: - Language Support Tests
    
    func testNonEnglishLanguages() {
        // Test that non-space-based languages are handled correctly
        let result = smartWordMerger.mergePartialResults(
            previousPartial: "你好",
            currentPartial: "你好世界",
            language: .chinese
        )
        
        XCTAssertEqual(result, "你好世界", "Should return current partial for non-space-based languages")
    }
    
    // MARK: - Performance Tests
    
    func testPerformanceWithManyMerges() {
        measure {
            for i in 0..<100 {
                let _ = smartWordMerger.mergePartialResults(
                    previousPartial: "some",
                    currentPartial: "some thing \(i)",
                    language: .english
                )
            }
        }
    }
    
    // MARK: - Error Handling Tests
    
    func testErrorHandling() {
        // Test with very long strings
        let longString = String(repeating: "word ", count: 1000)
        let result = smartWordMerger.mergePartialResults(
            previousPartial: "some",
            currentPartial: longString,
            language: .english
        )
        
        XCTAssertNotNil(result, "Should handle long strings gracefully")
        
        // Test with special characters
        let specialResult = smartWordMerger.mergePartialResults(
            previousPartial: "test",
            currentPartial: "test@#$%",
            language: .english
        )
        
        XCTAssertEqual(specialResult, "test@#$%", "Should handle special characters")
    }
}
