//
//  FloatingHistoryAnimationTests.swift
//  SikTingTests
//
//  Created by Augment Agent on 2025/1/25.
//

import XCTest
import SwiftUI
import Combine
@testable import SikTing

final class FloatingHistoryAnimationTests: XCTestCase {
    
    var animationManager: FloatingHistoryAnimationManager!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        animationManager = FloatingHistoryAnimationManager()
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        cancellables.removeAll()
        animationManager = nil
        super.tearDown()
    }
    
    // MARK: - Animation Manager Tests
    
    func testAnimationManagerInitialization() {
        // Given & When
        let manager = FloatingHistoryAnimationManager()
        
        // Then
        XCTAssertEqual(manager.currentAnimationPhase, .idle)
        XCTAssertTrue(manager.staggeredAnimationStates.isEmpty)
        XCTAssertFalse(manager.isFilteringInProgress)
        XCTAssertEqual(manager.optimizationLevel, .balanced)
    }
    
    func testTabSwitchAnimation() {
        // Given
        let expectation = XCTestExpectation(description: "Tab switch animation should start")
        
        animationManager.$currentAnimationPhase
            .dropFirst()
            .sink { phase in
                if phase == .tabSwitching {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        animationManager.startTabSwitchAnimation()
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        XCTAssertEqual(animationManager.currentAnimationPhase, .tabSwitching)
    }
    
    func testFilteringAnimation() {
        // Given
        let expectation = XCTestExpectation(description: "Filtering animation should start")
        
        animationManager.$isFilteringInProgress
            .dropFirst()
            .sink { isFiltering in
                if isFiltering {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        animationManager.startFilteringAnimation()
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        XCTAssertTrue(animationManager.isFilteringInProgress)
        XCTAssertEqual(animationManager.currentAnimationPhase, .filtering)
    }
    
    func testStaggeredAnimationCreation() {
        // Given
        let sessionIds = ["session1", "session2", "session3"]
        
        // When
        animationManager.createStaggeredAnimations(for: sessionIds)
        
        // Then
        XCTAssertEqual(animationManager.staggeredAnimationStates.count, sessionIds.count)
        
        // Check that each session has an animation state
        for sessionId in sessionIds {
            XCTAssertNotNil(animationManager.staggeredAnimationStates[sessionId])
        }
        
        // Check that delays are staggered
        let delays = sessionIds.compactMap { animationManager.staggeredAnimationStates[$0]?.animationDelay }
        XCTAssertTrue(delays.sorted() == delays) // Should be in ascending order
    }
    
    func testAnimationStateRetrieval() {
        // Given
        let sessionId = "testSession"
        animationManager.createStaggeredAnimations(for: [sessionId])
        
        // When
        let animationState = animationManager.getAnimationState(for: sessionId)
        
        // Then
        XCTAssertNotNil(animationState)
        XCTAssertEqual(animationState?.scale, 1.0)
        XCTAssertEqual(animationState?.opacity, 1.0)
    }
    
    func testAnimationCompletion() {
        // Given
        animationManager.startTabSwitchAnimation()
        XCTAssertEqual(animationManager.currentAnimationPhase, .tabSwitching)
        
        let expectation = XCTestExpectation(description: "Animation should complete")
        
        animationManager.$currentAnimationPhase
            .dropFirst()
            .sink { phase in
                if phase == .idle {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        animationManager.completeCurrentAnimation()
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        XCTAssertEqual(animationManager.currentAnimationPhase, .idle)
    }
    
    // MARK: - Optimization Level Tests
    
    func testOptimizationLevelChange() {
        // Given
        let expectation = XCTestExpectation(description: "Optimization level should change")
        
        animationManager.$optimizationLevel
            .dropFirst()
            .sink { level in
                if level == .performance {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        animationManager.setOptimizationLevel(.performance)
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        XCTAssertEqual(animationManager.optimizationLevel, .performance)
    }
    
    func testOptimizedAnimationRetrieval() {
        // Given
        let baseAnimation = Animation.easeInOut(duration: 0.3)
        animationManager.setOptimizationLevel(.performance)
        
        // When
        let optimizedAnimation = animationManager.getOptimizedAnimation(baseAnimation)
        
        // Then
        XCTAssertNotNil(optimizedAnimation)
        // Performance mode should have faster animations (1.5x speed)
    }
    
    func testAnimationThrottling() {
        // Given
        animationManager.setOptimizationLevel(.performance)
        
        // When
        let shouldAllow1 = animationManager.shouldAllowAnimation()
        
        // Simulate many concurrent animations
        for _ in 0..<15 {
            _ = animationManager.shouldAllowAnimation()
        }
        
        let shouldAllow2 = animationManager.shouldAllowAnimation()
        
        // Then
        XCTAssertTrue(shouldAllow1) // First animation should be allowed
        // Performance mode allows up to 10 concurrent animations
    }
    
    // MARK: - Animation State Management Tests
    
    func testAnimationStateClear() {
        // Given
        animationManager.createStaggeredAnimations(for: ["session1", "session2"])
        XCTAssertFalse(animationManager.staggeredAnimationStates.isEmpty)
        
        // When
        animationManager.clearAnimationStates()
        
        // Then
        XCTAssertTrue(animationManager.staggeredAnimationStates.isEmpty)
    }
    
    func testAnimationStateUpdate() {
        // Given
        let sessionId = "testSession"
        animationManager.createStaggeredAnimations(for: [sessionId])
        
        var newState = FloatingHistoryAnimationManager.AnimationState()
        newState.scale = 0.8
        newState.opacity = 0.5
        
        // When
        animationManager.updateAnimationState(for: sessionId, state: newState)
        
        // Then
        let updatedState = animationManager.getAnimationState(for: sessionId)
        XCTAssertEqual(updatedState?.scale, 0.8)
        XCTAssertEqual(updatedState?.opacity, 0.5)
    }
    
    // MARK: - Performance Tests
    
    func testAnimationCreationPerformance() {
        // Given
        let sessionIds = (0..<1000).map { "session\($0)" }
        
        // When & Then
        measure {
            animationManager.createStaggeredAnimations(for: sessionIds)
            animationManager.clearAnimationStates()
        }
    }
    
    func testAnimationStateRetrievalPerformance() {
        // Given
        let sessionIds = (0..<1000).map { "session\($0)" }
        animationManager.createStaggeredAnimations(for: sessionIds)
        
        // When & Then
        measure {
            for sessionId in sessionIds {
                _ = animationManager.getAnimationState(for: sessionId)
            }
        }
    }
    
    // MARK: - Edge Cases
    
    func testAnimationStateForNonexistentSession() {
        // Given
        let nonexistentSessionId = "nonexistent"
        
        // When
        let animationState = animationManager.getAnimationState(for: nonexistentSessionId)
        
        // Then
        XCTAssertNil(animationState)
    }
    
    func testMultipleAnimationPhaseChanges() {
        // Given
        let expectation = XCTestExpectation(description: "Should handle multiple phase changes")
        expectation.expectedFulfillmentCount = 3
        
        animationManager.$currentAnimationPhase
            .dropFirst()
            .sink { _ in
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // When
        animationManager.startTabSwitchAnimation()
        animationManager.startFilteringAnimation()
        animationManager.completeCurrentAnimation()
        
        // Then
        wait(for: [expectation], timeout: 2.0)
    }
    
    func testConcurrentAnimationManagement() {
        // Given
        animationManager.setOptimizationLevel(.balanced) // 20 max concurrent
        
        // When
        var allowedCount = 0
        for _ in 0..<25 {
            if animationManager.shouldAllowAnimation() {
                allowedCount += 1
            }
        }
        
        // Then
        XCTAssertLessThanOrEqual(allowedCount, 20) // Should not exceed max
    }
    
    // MARK: - Memory Management Tests
    
    func testAnimationStateMemoryManagement() {
        // Given
        let sessionIds = (0..<100).map { "session\($0)" }
        animationManager.createStaggeredAnimations(for: sessionIds)
        
        // When
        animationManager.clearAnimationStates()
        
        // Then
        XCTAssertTrue(animationManager.staggeredAnimationStates.isEmpty)
        // Memory should be released
    }
    
    func testAnimationManagerDeallocation() {
        // Given
        var manager: FloatingHistoryAnimationManager? = FloatingHistoryAnimationManager()
        manager?.createStaggeredAnimations(for: ["session1", "session2"])
        
        // When
        manager = nil
        
        // Then
        XCTAssertNil(manager)
        // Should not crash or leak memory
    }
}
