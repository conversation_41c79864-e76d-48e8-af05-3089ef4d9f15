//
//  AuthenticationErrorHandlerTests.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import XCTest
import Combine
@testable import SikTing

@MainActor
final class AuthenticationErrorHandlerTests: XCTestCase {
    
    // MARK: - Properties
    
    var errorHandler: AuthenticationErrorHandler!
    var cancellables: Set<AnyCancellable>!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        errorHandler = AuthenticationErrorHandler()
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        cancellables.removeAll()
        errorHandler = nil
        super.tearDown()
    }
    
    // MARK: - Error Presentation Tests
    
    func testAuthenticationErrorHandling() {
        // Given
        let testError = SecureAuthenticationError.appleSignInFailed("Test error")
        let expectation = XCTestExpectation(description: "Error presented")
        
        errorHandler.$isShowingError
            .sink { isShowing in
                if isShowing {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        errorHandler.handleAuthenticationError(testError, context: "test")
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        
        XCTAssertTrue(errorHandler.isShowingError)
        XCTAssertNotNil(errorHandler.currentError)
        XCTAssertEqual(errorHandler.currentError?.title, "Sign In Failed")
        XCTAssertEqual(errorHandler.currentError?.severity, .error)
    }
    
    func testWebSocketErrorHandling() {
        // Given
        let testError = SecureWebSocketError.authenticationRequired
        let expectation = XCTestExpectation(description: "WebSocket error presented")
        
        errorHandler.$isShowingError
            .sink { isShowing in
                if isShowing {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        errorHandler.handleWebSocketError(testError, context: "websocket")
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        
        XCTAssertTrue(errorHandler.isShowingError)
        XCTAssertNotNil(errorHandler.currentError)
        XCTAssertEqual(errorHandler.currentError?.title, "Authentication Required")
        XCTAssertEqual(errorHandler.currentError?.severity, .warning)
    }
    
    func testNetworkErrorHandling() {
        // Given
        let testError = URLError(.notConnectedToInternet)
        let expectation = XCTestExpectation(description: "Network error presented")
        
        errorHandler.$isShowingError
            .sink { isShowing in
                if isShowing {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        errorHandler.handleNetworkError(testError, context: "network")
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        
        XCTAssertTrue(errorHandler.isShowingError)
        XCTAssertNotNil(errorHandler.currentError)
        XCTAssertEqual(errorHandler.currentError?.title, "Network Error")
        XCTAssertEqual(errorHandler.currentError?.severity, .warning)
    }
    
    // MARK: - Error Severity Tests
    
    func testErrorSeverityMapping() {
        // Test different error severities
        let criticalError = SecureAuthenticationError.invalidToken
        let warningError = SecureAuthenticationError.tokenExpired
        let networkError = SecureAuthenticationError.networkError(URLError(.notConnectedToInternet))
        
        // When
        errorHandler.handleAuthenticationError(criticalError, context: "critical")
        XCTAssertEqual(errorHandler.currentError?.severity, .error)
        
        errorHandler.clearError()
        
        errorHandler.handleAuthenticationError(warningError, context: "warning")
        XCTAssertEqual(errorHandler.currentError?.severity, .warning)
        
        errorHandler.clearError()
        
        errorHandler.handleAuthenticationError(networkError, context: "network")
        XCTAssertEqual(errorHandler.currentError?.severity, .warning)
    }
    
    // MARK: - Retry Logic Tests
    
    func testRetryActionPresence() {
        // Given
        let retryableError = SecureAuthenticationError.networkError(URLError(.notConnectedToInternet))
        var retryCallCount = 0
        
        // When
        errorHandler.handleAuthenticationError(retryableError, context: "retry") {
            retryCallCount += 1
        }
        
        // Then
        XCTAssertNotNil(errorHandler.currentError?.primaryAction)
        XCTAssertEqual(errorHandler.currentError?.primaryAction?.title, "Retry")
    }
    
    func testNoRetryActionForNonRetryableError() {
        // Given
        let nonRetryableError = SecureAuthenticationError.appleSignInCancelled
        
        // When
        errorHandler.handleAuthenticationError(nonRetryableError, context: "no-retry")
        
        // Then
        XCTAssertNil(errorHandler.currentError?.primaryAction)
    }
    
    func testAutoRetryBehavior() async {
        // Given
        let autoRetryError = SecureAuthenticationError.networkError(URLError(.notConnectedToInternet))
        var retryCallCount = 0
        let expectation = XCTestExpectation(description: "Auto retry executed")
        
        // When
        errorHandler.handleAuthenticationError(autoRetryError, context: "auto-retry") {
            retryCallCount += 1
            expectation.fulfill()
        }
        
        // Then
        await fulfillment(of: [expectation], timeout: 5.0)
        XCTAssertGreaterThan(retryCallCount, 0)
    }
    
    // MARK: - Error Clearing Tests
    
    func testErrorClearing() {
        // Given
        let testError = SecureAuthenticationError.appleSignInFailed("Test")
        errorHandler.handleAuthenticationError(testError, context: "clear")
        
        XCTAssertTrue(errorHandler.isShowingError)
        XCTAssertNotNil(errorHandler.currentError)
        
        // When
        errorHandler.clearError()
        
        // Then
        XCTAssertFalse(errorHandler.isShowingError)
        XCTAssertNil(errorHandler.currentError)
        XCTAssertFalse(errorHandler.isRetrying)
    }
    
    // MARK: - Retry Attempt Tracking Tests
    
    func testRetryAttemptTracking() {
        // Given
        let retryableError = SecureAuthenticationError.networkError(URLError(.notConnectedToInternet))
        let context = "retry-tracking"
        
        // When - First attempt
        errorHandler.handleAuthenticationError(retryableError, context: context)
        let firstAttemptShouldAutoRetry = errorHandler.currentError?.shouldAutoRetry ?? false
        
        errorHandler.clearError()
        
        // When - Second attempt (simulating retry)
        errorHandler.handleAuthenticationError(retryableError, context: context)
        let secondAttemptShouldAutoRetry = errorHandler.currentError?.shouldAutoRetry ?? false
        
        // Then
        XCTAssertTrue(firstAttemptShouldAutoRetry)
        // After multiple attempts, auto-retry might be disabled
        // This depends on the specific implementation
    }
    
    func testRetryAttemptReset() {
        // Given
        let retryableError = SecureAuthenticationError.networkError(URLError(.notConnectedToInternet))
        let context = "retry-reset"
        
        // When
        errorHandler.handleAuthenticationError(retryableError, context: context)
        errorHandler.resetRetryAttempts(for: context)
        errorHandler.clearError()
        
        // Then - Should behave like first attempt again
        errorHandler.handleAuthenticationError(retryableError, context: context)
        XCTAssertTrue(errorHandler.currentError?.shouldAutoRetry ?? false)
    }
    
    // MARK: - Error Message Tests
    
    func testUserFriendlyErrorMessages() {
        let testCases: [(SecureAuthenticationError, String)] = [
            (.appleSignInFailed("Technical error"), "Sign in with Apple failed. Please try again."),
            (.appleSignInCancelled, "Sign in was cancelled."),
            (.tokenExchangeFailed("Server error"), "Authentication failed. Please try signing in again."),
            (.networkError(URLError(.notConnectedToInternet)), "Please check your internet connection and try again."),
            (.tokenExpired, "Your session has expired. Please sign in again."),
            (.rateLimitExceeded, "Too many attempts. Please wait a moment and try again."),
            (.serverUnavailable, "Service is temporarily unavailable. Please try again later."),
            (.invalidResponse, "Authentication error. Please try signing in again."),
            (.userNotFound, "Account not found. Please sign up first.")
        ]
        
        for (error, expectedMessage) in testCases {
            // When
            errorHandler.handleAuthenticationError(error, context: "message-test")
            
            // Then
            XCTAssertEqual(errorHandler.currentError?.message, expectedMessage, "Error message mismatch for \(error)")
            
            errorHandler.clearError()
        }
    }
    
    // MARK: - Error Action Tests
    
    func testSecondaryActionAlwaysPresent() {
        // Given
        let testError = SecureAuthenticationError.appleSignInFailed("Test")
        
        // When
        errorHandler.handleAuthenticationError(testError, context: "secondary")
        
        // Then
        XCTAssertNotNil(errorHandler.currentError?.secondaryAction)
        XCTAssertEqual(errorHandler.currentError?.secondaryAction?.title, "Dismiss")
    }
    
    func testErrorActionExecution() async {
        // Given
        let testError = SecureAuthenticationError.appleSignInFailed("Test")
        errorHandler.handleAuthenticationError(testError, context: "action")
        
        // When
        if let secondaryAction = errorHandler.currentError?.secondaryAction {
            await secondaryAction.action()
        }
        
        // Then
        XCTAssertFalse(errorHandler.isShowingError)
        XCTAssertNil(errorHandler.currentError)
    }
}
