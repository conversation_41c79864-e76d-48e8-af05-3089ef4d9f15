//
//  HistoryStorageServiceTests.swift
//  SikTingTests
//
//  Created by Augment Agent on 2025-07-21.
//

import XCTest
import CoreData
@testable import SikTing

class HistoryStorageServiceTests: XCTestCase {
    
    // MARK: - Properties
    
    var storageService: HistoryStorageService!
    var testCoreDataStack: NSPersistentContainer!
    var testContext: NSManagedObjectContext!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        setupInMemoryCoreDataStack()
        storageService = HistoryStorageService(coreDataStack: createTestCoreDataStack())
    }
    
    override func tearDown() {
        clearTestData()
        storageService = nil
        testContext = nil
        testCoreDataStack = nil
        super.tearDown()
    }
    
    // MARK: - Helper Methods
    
    private func setupInMemoryCoreDataStack() {
        guard let modelURL = Bundle.main.url(forResource: "TranscriptionHistory", withExtension: "momd"),
              let model = NSManagedObjectModel(contentsOf: modelURL) else {
            XCTFail("Failed to load Core Data model")
            return
        }
        
        testCoreDataStack = NSPersistentContainer(name: "TranscriptionHistory", managedObjectModel: model)
        
        let description = NSPersistentStoreDescription()
        description.type = NSInMemoryStoreType
        description.shouldAddStoreAsynchronously = false
        
        testCoreDataStack.persistentStoreDescriptions = [description]
        
        testCoreDataStack.loadPersistentStores { _, error in
            XCTAssertNil(error)
        }
        
        testContext = testCoreDataStack.viewContext
        testContext.automaticallyMergesChangesFromParent = true
    }
    
    private func createTestCoreDataStack() -> CoreDataStack {
        // Create a mock CoreDataStack that uses our test container
        let mockStack = CoreDataStack()
        // We would need to modify CoreDataStack to accept a custom container for testing
        return mockStack
    }
    
    private func clearTestData() {
        let sessionRequest: NSFetchRequest<NSFetchRequestResult> = HistorySession.fetchRequest()
        let deleteSessionsRequest = NSBatchDeleteRequest(fetchRequest: sessionRequest)
        
        let entryRequest: NSFetchRequest<NSFetchRequestResult> = HistoryEntry.fetchRequest()
        let deleteEntriesRequest = NSBatchDeleteRequest(fetchRequest: entryRequest)
        
        do {
            try testContext.execute(deleteEntriesRequest)
            try testContext.execute(deleteSessionsRequest)
            try testContext.save()
        } catch {
            XCTFail("Failed to clear test data: \(error)")
        }
    }
    
    private func createTestSession(title: String = "Test Session") -> HistorySession {
        let session = HistorySession(context: testContext)
        session.id = UUID()
        session.title = title
        session.createdAt = Date()
        session.updatedAt = Date()
        session.language = "en-US"
        session.duration = 120.0
        session.isFavourite = false
        session.isSaved = false
        return session
    }
    
    private func createTestEntry(session: HistorySession, text: String = "Test text") -> HistoryEntry {
        let entry = HistoryEntry(context: testContext)
        entry.id = UUID()
        entry.sessionId = session.id
        entry.text = text
        entry.timestamp = Date()
        entry.confidence = 0.95
        entry.isPartial = false
        entry.language = session.language
        entry.session = session
        return entry
    }
    
    // MARK: - Session Save Tests
    
    func testSaveSession() throws {
        // Given
        let session = createTestSession(title: "Save Test Session")
        
        // When
        try storageService.saveSession(session)
        
        // Then
        let fetchRequest: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
        let savedSessions = try testContext.fetch(fetchRequest)
        
        XCTAssertEqual(savedSessions.count, 1)
        XCTAssertEqual(savedSessions.first?.title, "Save Test Session")
        XCTAssertEqual(savedSessions.first?.id, session.id)
    }
    
    func testSaveSessionWithEntries() throws {
        // Given
        let session = createTestSession(title: "Session with Entries")
        let entry1 = createTestEntry(session: session, text: "First entry")
        let entry2 = createTestEntry(session: session, text: "Second entry")
        
        session.addToEntries(entry1)
        session.addToEntries(entry2)
        
        // When
        try storageService.saveSession(session)
        
        // Then
        let fetchRequest: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
        fetchRequest.relationshipKeyPathsForPrefetching = ["entries"]
        let savedSessions = try testContext.fetch(fetchRequest)
        
        XCTAssertEqual(savedSessions.count, 1)
        XCTAssertEqual(savedSessions.first?.entries?.count, 2)
    }
    
    func testSaveSessionValidation() {
        // Given
        let invalidSession = HistorySession(context: testContext)
        // Missing required fields
        
        // When & Then
        XCTAssertThrowsError(try storageService.saveSession(invalidSession)) { error in
            XCTAssertTrue(error is HistoryError)
        }
    }
    
    // MARK: - Session Fetch Tests
    
    func testFetchAllSessions() throws {
        // Given
        let session1 = createTestSession(title: "Session 1")
        let session2 = createTestSession(title: "Session 2")
        let session3 = createTestSession(title: "Session 3")
        
        try storageService.saveSession(session1)
        try storageService.saveSession(session2)
        try storageService.saveSession(session3)
        
        // When
        let fetchedSessions = try storageService.fetchAllSessions()
        
        // Then
        XCTAssertEqual(fetchedSessions.count, 3)
        
        let titles = fetchedSessions.map { $0.title ?? "" }.sorted()
        XCTAssertEqual(titles, ["Session 1", "Session 2", "Session 3"])
    }
    
    func testFetchSessionsSortedByDate() throws {
        // Given
        let now = Date()
        let session1 = createTestSession(title: "Oldest")
        session1.createdAt = now.addingTimeInterval(-3600) // 1 hour ago
        
        let session2 = createTestSession(title: "Newest")
        session2.createdAt = now
        
        let session3 = createTestSession(title: "Middle")
        session3.createdAt = now.addingTimeInterval(-1800) // 30 minutes ago
        
        try storageService.saveSession(session1)
        try storageService.saveSession(session2)
        try storageService.saveSession(session3)
        
        // When
        let fetchedSessions = try storageService.fetchAllSessions()
        
        // Then
        XCTAssertEqual(fetchedSessions.count, 3)
        
        // Should be sorted by creation date (newest first)
        XCTAssertEqual(fetchedSessions[0].title, "Newest")
        XCTAssertEqual(fetchedSessions[1].title, "Middle")
        XCTAssertEqual(fetchedSessions[2].title, "Oldest")
    }
    
    func testFetchFavoriteSessions() throws {
        // Given
        let favoriteSession = createTestSession(title: "Favorite")
        favoriteSession.isFavourite = true
        
        let regularSession = createTestSession(title: "Regular")
        regularSession.isFavourite = false
        
        try storageService.saveSession(favoriteSession)
        try storageService.saveSession(regularSession)
        
        // When
        let favoriteSessions = try storageService.fetchFavoriteSessions()
        
        // Then
        XCTAssertEqual(favoriteSessions.count, 1)
        XCTAssertEqual(favoriteSessions.first?.title, "Favorite")
        XCTAssertTrue(favoriteSessions.first?.isFavourite ?? false)
    }
    
    func testFetchSavedSessions() throws {
        // Given
        let savedSession = createTestSession(title: "Saved")
        savedSession.isSaved = true
        
        let unsavedSession = createTestSession(title: "Unsaved")
        unsavedSession.isSaved = false
        
        try storageService.saveSession(savedSession)
        try storageService.saveSession(unsavedSession)
        
        // When
        let savedSessions = try storageService.fetchSavedSessions()
        
        // Then
        XCTAssertEqual(savedSessions.count, 1)
        XCTAssertEqual(savedSessions.first?.title, "Saved")
        XCTAssertTrue(savedSessions.first?.isSaved ?? false)
    }
    
    // MARK: - Session Update Tests
    
    func testUpdateSession() throws {
        // Given
        let session = createTestSession(title: "Original Title")
        try storageService.saveSession(session)
        
        // When
        session.title = "Updated Title"
        session.isFavourite = true
        try storageService.updateSession(session)
        
        // Then
        let fetchRequest: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %@", session.id! as CVarArg)
        
        let updatedSessions = try testContext.fetch(fetchRequest)
        XCTAssertEqual(updatedSessions.count, 1)
        XCTAssertEqual(updatedSessions.first?.title, "Updated Title")
        XCTAssertTrue(updatedSessions.first?.isFavourite ?? false)
    }
    
    func testToggleFavoriteSession() throws {
        // Given
        let session = createTestSession()
        session.isFavourite = false
        try storageService.saveSession(session)
        
        // When
        try storageService.toggleFavorite(for: session)
        
        // Then
        XCTAssertTrue(session.isFavourite)
        
        // When - toggle again
        try storageService.toggleFavorite(for: session)
        
        // Then
        XCTAssertFalse(session.isFavourite)
    }
    
    func testToggleSavedSession() throws {
        // Given
        let session = createTestSession()
        session.isSaved = false
        try storageService.saveSession(session)
        
        // When
        try storageService.toggleSaved(for: session)
        
        // Then
        XCTAssertTrue(session.isSaved)
        
        // When - toggle again
        try storageService.toggleSaved(for: session)
        
        // Then
        XCTAssertFalse(session.isSaved)
    }
    
    // MARK: - Session Delete Tests
    
    func testDeleteSession() throws {
        // Given
        let session = createTestSession(title: "To Delete")
        try storageService.saveSession(session)
        
        let fetchRequest: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
        let initialCount = try testContext.count(for: fetchRequest)
        XCTAssertEqual(initialCount, 1)
        
        // When
        try storageService.deleteSession(session)
        
        // Then
        let finalCount = try testContext.count(for: fetchRequest)
        XCTAssertEqual(finalCount, 0)
    }
    
    func testDeleteSessionWithEntries() throws {
        // Given
        let session = createTestSession(title: "Session with Entries")
        let entry1 = createTestEntry(session: session)
        let entry2 = createTestEntry(session: session)
        
        session.addToEntries(entry1)
        session.addToEntries(entry2)
        try storageService.saveSession(session)
        
        let entryRequest: NSFetchRequest<HistoryEntry> = HistoryEntry.fetchRequest()
        let initialEntryCount = try testContext.count(for: entryRequest)
        XCTAssertEqual(initialEntryCount, 2)
        
        // When
        try storageService.deleteSession(session)
        
        // Then - Entries should be deleted due to cascade delete
        let finalEntryCount = try testContext.count(for: entryRequest)
        XCTAssertEqual(finalEntryCount, 0)
    }
    
    // MARK: - Entry Operations Tests
    
    func testSaveEntry() throws {
        // Given
        let session = createTestSession()
        try storageService.saveSession(session)
        
        let entry = createTestEntry(session: session, text: "Test entry text")
        
        // When
        try storageService.saveEntry(entry)
        
        // Then
        let fetchRequest: NSFetchRequest<HistoryEntry> = HistoryEntry.fetchRequest()
        let savedEntries = try testContext.fetch(fetchRequest)
        
        XCTAssertEqual(savedEntries.count, 1)
        XCTAssertEqual(savedEntries.first?.text, "Test entry text")
        XCTAssertEqual(savedEntries.first?.sessionId, session.id)
    }
    
    func testUpdateEntry() throws {
        // Given
        let session = createTestSession()
        let entry = createTestEntry(session: session, text: "Original text")
        try storageService.saveSession(session)
        try storageService.saveEntry(entry)
        
        // When
        entry.text = "Updated text"
        entry.confidence = 0.85
        try storageService.updateEntry(entry)
        
        // Then
        let fetchRequest: NSFetchRequest<HistoryEntry> = HistoryEntry.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %@", entry.id! as CVarArg)
        
        let updatedEntries = try testContext.fetch(fetchRequest)
        XCTAssertEqual(updatedEntries.count, 1)
        XCTAssertEqual(updatedEntries.first?.text, "Updated text")
        XCTAssertEqual(updatedEntries.first?.confidence, 0.85)
    }
    
    // MARK: - Search and Filter Tests
    
    func testSearchSessions() throws {
        // Given
        let session1 = createTestSession(title: "Meeting Notes")
        let session2 = createTestSession(title: "Interview Recording")
        let session3 = createTestSession(title: "Lecture Transcript")
        
        try storageService.saveSession(session1)
        try storageService.saveSession(session2)
        try storageService.saveSession(session3)
        
        // When
        let searchResults = try storageService.searchSessions(query: "record")
        
        // Then
        XCTAssertEqual(searchResults.count, 1)
        XCTAssertEqual(searchResults.first?.title, "Interview Recording")
    }
    
    func testSearchSessionsInContent() throws {
        // Given
        let session = createTestSession(title: "Test Session")
        let entry1 = createTestEntry(session: session, text: "This is about machine learning")
        let entry2 = createTestEntry(session: session, text: "Artificial intelligence discussion")
        
        session.addToEntries(entry1)
        session.addToEntries(entry2)
        try storageService.saveSession(session)
        
        // When
        let searchResults = try storageService.searchSessions(query: "machine")
        
        // Then
        XCTAssertEqual(searchResults.count, 1)
        XCTAssertEqual(searchResults.first?.title, "Test Session")
    }
    
    // MARK: - Performance Tests
    
    func testSavePerformance() {
        measure {
            for i in 0..<100 {
                let session = createTestSession(title: "Performance Test \(i)")
                do {
                    try storageService.saveSession(session)
                } catch {
                    XCTFail("Save failed: \(error)")
                }
            }
        }
    }
    
    func testFetchPerformance() throws {
        // Given - Create test data
        for i in 0..<100 {
            let session = createTestSession(title: "Fetch Test \(i)")
            try storageService.saveSession(session)
        }
        
        // When & Then
        measure {
            do {
                let sessions = try storageService.fetchAllSessions()
                XCTAssertEqual(sessions.count, 100)
            } catch {
                XCTFail("Fetch failed: \(error)")
            }
        }
    }
    
    // MARK: - Error Handling Tests

    func testSaveSessionError() {
        // Given
        let invalidSession = HistorySession(context: testContext)
        // Missing required fields like id, title, etc.

        // When & Then
        XCTAssertThrowsError(try storageService.saveSession(invalidSession)) { error in
            if let historyError = error as? HistoryError {
                XCTAssertEqual(historyError, .validationFailed)
            } else {
                XCTFail("Expected HistoryError.validationFailed")
            }
        }
    }

    func testDeleteNonExistentSession() {
        // Given
        let session = createTestSession()
        // Don't save it to the context

        // When & Then
        XCTAssertThrowsError(try storageService.deleteSession(session)) { error in
            XCTAssertTrue(error is HistoryError)
        }
    }

    // MARK: - Batch Operations Tests

    func testBatchDeleteSessions() throws {
        // Given
        let sessions = (0..<10).map { createTestSession(title: "Batch Test \($0)") }
        for session in sessions {
            try storageService.saveSession(session)
        }

        let initialCount = try storageService.fetchAllSessions().count
        XCTAssertEqual(initialCount, 10)

        // When
        let sessionsToDelete = Array(sessions.prefix(5))
        try storageService.batchDeleteSessions(sessionsToDelete)

        // Then
        let finalCount = try storageService.fetchAllSessions().count
        XCTAssertEqual(finalCount, 5)
    }

    func testBatchUpdateSessions() throws {
        // Given
        let sessions = (0..<5).map { createTestSession(title: "Update Test \($0)") }
        for session in sessions {
            try storageService.saveSession(session)
        }

        // When
        for session in sessions {
            session.isFavourite = true
        }
        try storageService.batchUpdateSessions(sessions)

        // Then
        let favoriteSessions = try storageService.fetchFavoriteSessions()
        XCTAssertEqual(favoriteSessions.count, 5)
    }

    // MARK: - Language Filter Tests

    func testFetchSessionsByLanguage() throws {
        // Given
        let englishSession = createTestSession(title: "English Session")
        englishSession.language = "en-US"

        let spanishSession = createTestSession(title: "Spanish Session")
        spanishSession.language = "es-ES"

        let frenchSession = createTestSession(title: "French Session")
        frenchSession.language = "fr-FR"

        try storageService.saveSession(englishSession)
        try storageService.saveSession(spanishSession)
        try storageService.saveSession(frenchSession)

        // When
        let englishSessions = try storageService.fetchSessions(byLanguage: "en-US")

        // Then
        XCTAssertEqual(englishSessions.count, 1)
        XCTAssertEqual(englishSessions.first?.title, "English Session")
        XCTAssertEqual(englishSessions.first?.language, "en-US")
    }

    // MARK: - Date Range Filter Tests

    func testFetchSessionsByDateRange() throws {
        // Given
        let now = Date()
        let yesterday = now.addingTimeInterval(-86400)
        let tomorrow = now.addingTimeInterval(86400)

        let oldSession = createTestSession(title: "Old Session")
        oldSession.createdAt = yesterday.addingTimeInterval(-3600) // Before yesterday

        let recentSession = createTestSession(title: "Recent Session")
        recentSession.createdAt = now

        let futureSession = createTestSession(title: "Future Session")
        futureSession.createdAt = tomorrow

        try storageService.saveSession(oldSession)
        try storageService.saveSession(recentSession)
        try storageService.saveSession(futureSession)

        // When
        let sessionsInRange = try storageService.fetchSessions(from: yesterday, to: tomorrow)

        // Then
        XCTAssertEqual(sessionsInRange.count, 2)
        let titles = sessionsInRange.map { $0.title ?? "" }.sorted()
        XCTAssertTrue(titles.contains("Recent Session"))
        XCTAssertTrue(titles.contains("Future Session"))
        XCTAssertFalse(titles.contains("Old Session"))
    }

    // MARK: - Pagination Tests

    func testFetchSessionsWithPagination() throws {
        // Given
        let sessions = (0..<25).map { createTestSession(title: "Page Test \($0)") }
        for session in sessions {
            try storageService.saveSession(session)
        }

        // When - First page
        let firstPage = try storageService.fetchSessions(limit: 10, offset: 0)

        // Then
        XCTAssertEqual(firstPage.count, 10)

        // When - Second page
        let secondPage = try storageService.fetchSessions(limit: 10, offset: 10)

        // Then
        XCTAssertEqual(secondPage.count, 10)

        // When - Third page
        let thirdPage = try storageService.fetchSessions(limit: 10, offset: 20)

        // Then
        XCTAssertEqual(thirdPage.count, 5) // Remaining sessions

        // Verify no overlap
        let firstPageIds = Set(firstPage.compactMap { $0.id })
        let secondPageIds = Set(secondPage.compactMap { $0.id })
        let thirdPageIds = Set(thirdPage.compactMap { $0.id })

        XCTAssertTrue(firstPageIds.isDisjoint(with: secondPageIds))
        XCTAssertTrue(secondPageIds.isDisjoint(with: thirdPageIds))
        XCTAssertTrue(firstPageIds.isDisjoint(with: thirdPageIds))
    }

    // MARK: - Statistics Tests

    func testGetSessionStatistics() throws {
        // Given
        let sessions = (0..<10).map { i in
            let session = createTestSession(title: "Stats Test \(i)")
            session.duration = Double(i * 60) // 0, 60, 120, ... seconds
            session.isFavourite = i % 2 == 0 // Every other session is favorite
            return session
        }

        for session in sessions {
            try storageService.saveSession(session)
        }

        // When
        let stats = try storageService.getSessionStatistics()

        // Then
        XCTAssertEqual(stats.totalSessions, 10)
        XCTAssertEqual(stats.favoriteSessions, 5)
        XCTAssertEqual(stats.totalDuration, 2700.0) // Sum of 0+60+120+...+540
        XCTAssertEqual(stats.averageDuration, 270.0) // 2700/10
    }

    // MARK: - Concurrent Access Tests

    func testConcurrentSaveOperations() throws {
        let expectation = XCTestExpectation(description: "Concurrent saves")
        expectation.expectedFulfillmentCount = 10

        let queue = DispatchQueue.global(qos: .userInitiated)

        // When
        for i in 0..<10 {
            queue.async {
                let session = self.createTestSession(title: "Concurrent \(i)")
                do {
                    try self.storageService.saveSession(session)
                    expectation.fulfill()
                } catch {
                    XCTFail("Concurrent save failed: \(error)")
                }
            }
        }

        wait(for: [expectation], timeout: 5.0)

        // Then
        let allSessions = try storageService.fetchAllSessions()
        XCTAssertEqual(allSessions.count, 10)
    }

    // MARK: - Memory Management Tests

    func testLargeDatasetMemoryUsage() throws {
        // Given
        let largeDatasetSize = 1000

        // When
        for i in 0..<largeDatasetSize {
            let session = createTestSession(title: "Memory Test \(i)")

            // Add multiple entries to each session
            for j in 0..<5 {
                let entry = createTestEntry(session: session, text: "Entry \(j) for session \(i)")
                session.addToEntries(entry)
            }

            try storageService.saveSession(session)
        }

        // Then
        let allSessions = try storageService.fetchAllSessions()
        XCTAssertEqual(allSessions.count, largeDatasetSize)

        // Verify memory usage is reasonable (this is more of a smoke test)
        let totalEntries = allSessions.reduce(0) { $0 + ($1.entries?.count ?? 0) }
        XCTAssertEqual(totalEntries, largeDatasetSize * 5)
    }

    // MARK: - Data Integrity Tests

    func testDataIntegrityAfterMultipleOperations() throws {
        // Given
        let session = createTestSession(title: "Integrity Test")
        try storageService.saveSession(session)

        // When - Multiple operations
        session.title = "Updated Title"
        try storageService.updateSession(session)

        try storageService.toggleFavorite(for: session)
        try storageService.toggleSaved(for: session)

        let entry = createTestEntry(session: session, text: "Test entry")
        session.addToEntries(entry)
        try storageService.saveEntry(entry)

        // Then - Verify data integrity
        let fetchedSessions = try storageService.fetchAllSessions()
        XCTAssertEqual(fetchedSessions.count, 1)

        let fetchedSession = fetchedSessions.first!
        XCTAssertEqual(fetchedSession.title, "Updated Title")
        XCTAssertTrue(fetchedSession.isFavourite)
        XCTAssertTrue(fetchedSession.isSaved)
        XCTAssertEqual(fetchedSession.entries?.count, 1)

        let fetchedEntry = fetchedSession.entries?.allObjects.first as? HistoryEntry
        XCTAssertEqual(fetchedEntry?.text, "Test entry")
        XCTAssertEqual(fetchedEntry?.sessionId, session.id)
    }
}
