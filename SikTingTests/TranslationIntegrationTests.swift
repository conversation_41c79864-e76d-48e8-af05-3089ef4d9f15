//
//  TranslationIntegrationTests.swift
//  SikTingTests
//
//  Created by Augment Agent on 2025/7/21.
//

import XCTest
import Combine
@testable import SikTing

@MainActor
final class TranslationIntegrationTests: XCTestCase {
    
    var viewModel: SpeechRecognitionViewModel!
    var mockTranslationService: MockTranslationService!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        viewModel = SpeechRecognitionViewModel()
        mockTranslationService = MockTranslationService()
        
        // Replace the real translation service with mock for testing
        viewModel.translationService = mockTranslationService
        
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        cancellables = nil
        mockTranslationService = nil
        viewModel = nil
        super.tearDown()
    }
    
    // MARK: - Translation Entry Tests
    
    func testTranscriptionEntryTranslationProperties() {
        let entry = TranscriptionEntry(
            timestamp: Date(),
            text: "Hello world",
            parsedContent: ParsedContent(language: .english, emotion: .neutral),
            isWaiting: false,
            isFinal: true
        )
        
        // Test initial state
        XCTAssertNil(entry.translatedText)
        XCTAssertEqual(entry.translationState, .idle)
        XCTAssertNil(entry.targetLanguage)
        
        // Test setting translation properties
        entry.translatedText = "Hola mundo"
        entry.translationState = .completed("Hola mundo")
        entry.targetLanguage = .spanish
        
        XCTAssertEqual(entry.translatedText, "Hola mundo")
        XCTAssertEqual(entry.translationState, .completed("Hola mundo"))
        XCTAssertEqual(entry.targetLanguage, .spanish)
    }
    
    func testTranscriptionEntryEquality() {
        let entry1 = TranscriptionEntry(
            timestamp: Date(),
            text: "Hello",
            parsedContent: nil,
            translatedText: "Hola"
        )
        
        let entry2 = TranscriptionEntry(
            timestamp: Date(),
            text: "Hello",
            parsedContent: nil,
            translatedText: "Hola"
        )
        
        // Entries should be equal based on their ID, not content
        XCTAssertNotEqual(entry1, entry2, "Different entries should not be equal even with same content")
        XCTAssertEqual(entry1, entry1, "Entry should be equal to itself")
    }
    
    // MARK: - Translation Workflow Tests
    
    func testRetryTranslationForEntry() async {
        // Create a test entry
        let entry = TranscriptionEntry(
            timestamp: Date(),
            text: "Hello world",
            parsedContent: ParsedContent(language: .english, emotion: .neutral),
            isFinal: true
        )
        
        // Set up mock translation
        mockTranslationService.mockTranslation = "Hola mundo"
        mockTranslationService.shouldFail = false
        
        // Add entry to viewModel
        viewModel.transcriptionEntries.append(entry)
        
        // Test retry translation
        viewModel.retryTranslation(for: entry)
        
        // Wait for async translation to complete
        let expectation = XCTestExpectation(description: "Translation completed")
        
        entry.$translationState
            .sink { state in
                if case .completed = state {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        await fulfillment(of: [expectation], timeout: 2.0)
        
        // Verify translation was successful
        XCTAssertEqual(entry.translatedText, "Hola mundo")
        XCTAssertEqual(entry.targetLanguage, mockTranslationService.selectedTargetLanguage)
    }
    
    func testTranslationFailureHandling() async {
        // Create a test entry
        let entry = TranscriptionEntry(
            timestamp: Date(),
            text: "Hello world",
            parsedContent: ParsedContent(language: .english, emotion: .neutral),
            isFinal: true
        )
        
        // Set up mock to fail
        mockTranslationService.shouldFail = true
        
        // Add entry to viewModel
        viewModel.transcriptionEntries.append(entry)
        
        // Test retry translation
        viewModel.retryTranslation(for: entry)
        
        // Wait for async translation to fail
        let expectation = XCTestExpectation(description: "Translation failed")
        
        entry.$translationState
            .sink { state in
                if case .failed = state {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        await fulfillment(of: [expectation], timeout: 2.0)
        
        // Verify translation failed appropriately
        XCTAssertNil(entry.translatedText)
        if case .failed(let error) = entry.translationState {
            XCTAssertTrue(error is TranslationError)
        } else {
            XCTFail("Expected translation state to be failed")
        }
    }
    
    func testTranslationWithDisabledService() async {
        // Disable translation
        mockTranslationService.isTranslationEnabled = false
        
        // Create a test entry
        let entry = TranscriptionEntry(
            timestamp: Date(),
            text: "Hello world",
            parsedContent: ParsedContent(language: .english, emotion: .neutral),
            isFinal: true
        )
        
        // Add entry to viewModel
        viewModel.transcriptionEntries.append(entry)
        
        // Test retry translation (should be skipped)
        viewModel.retryTranslation(for: entry)
        
        // Wait a bit to ensure no translation occurs
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        // Verify no translation occurred
        XCTAssertNil(entry.translatedText)
        XCTAssertEqual(entry.translationState, .idle)
    }
    
    func testTranslationWithEmptyText() async {
        // Create a test entry with empty text
        let entry = TranscriptionEntry(
            timestamp: Date(),
            text: "",
            parsedContent: ParsedContent(language: .english, emotion: .neutral),
            isFinal: true
        )
        
        // Add entry to viewModel
        viewModel.transcriptionEntries.append(entry)
        
        // Test retry translation (should be skipped)
        viewModel.retryTranslation(for: entry)
        
        // Wait a bit to ensure no translation occurs
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        // Verify no translation occurred
        XCTAssertNil(entry.translatedText)
        XCTAssertEqual(entry.translationState, .idle)
    }
    
    // MARK: - Language Detection Integration Tests
    
    func testLanguageDetectionIntegration() async {
        // Create entry without recognized language
        let entry = TranscriptionEntry(
            timestamp: Date(),
            text: "Hello world",
            parsedContent: nil, // No language detected
            isFinal: true
        )
        
        // Set up mock language detection
        mockTranslationService.mockLanguage = .english
        mockTranslationService.mockConfidence = 0.95
        mockTranslationService.mockTranslation = "Hola mundo"
        
        // Add entry to viewModel
        viewModel.transcriptionEntries.append(entry)
        
        // Test retry translation (should trigger language detection)
        viewModel.retryTranslation(for: entry)
        
        // Wait for async translation to complete
        let expectation = XCTestExpectation(description: "Translation with language detection completed")
        
        entry.$translationState
            .sink { state in
                if case .completed = state {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        await fulfillment(of: [expectation], timeout: 3.0)
        
        // Verify translation was successful
        XCTAssertEqual(entry.translatedText, "Hola mundo")
    }
    
    // MARK: - Performance Tests
    
    func testTranslationPerformance() {
        // Test that translation request setup is fast
        measure {
            let entry = TranscriptionEntry(
                timestamp: Date(),
                text: "Hello world",
                parsedContent: ParsedContent(language: .english, emotion: .neutral),
                isFinal: true
            )
            
            viewModel.retryTranslation(for: entry)
        }
    }
    
    func testMultipleTranslationRequests() async {
        // Test handling multiple translation requests
        let entries = (0..<5).map { index in
            TranscriptionEntry(
                timestamp: Date(),
                text: "Hello world \(index)",
                parsedContent: ParsedContent(language: .english, emotion: .neutral),
                isFinal: true
            )
        }
        
        // Set up mock translation
        mockTranslationService.mockTranslation = "Hola mundo"
        
        // Add entries to viewModel
        viewModel.transcriptionEntries.append(contentsOf: entries)
        
        // Trigger translations for all entries
        for entry in entries {
            viewModel.retryTranslation(for: entry)
        }
        
        // Wait for all translations to complete
        let expectation = XCTestExpectation(description: "All translations completed")
        expectation.expectedFulfillmentCount = entries.count
        
        for entry in entries {
            entry.$translationState
                .sink { state in
                    if case .completed = state {
                        expectation.fulfill()
                    }
                }
                .store(in: &cancellables)
        }
        
        await fulfillment(of: [expectation], timeout: 5.0)
        
        // Verify all translations completed
        for entry in entries {
            XCTAssertEqual(entry.translatedText, "Hola mundo")
        }
    }
}
