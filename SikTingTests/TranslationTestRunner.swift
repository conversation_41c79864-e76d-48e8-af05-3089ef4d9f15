//
//  TranslationTestRunner.swift
//  SikTingTests
//
//  Created by Augment Agent on 2025/7/26.
//

import XCTest
@testable import SikTing

/// Manual test runner for translation services
/// Use this to manually test translation functionality during development
@MainActor
class TranslationTestRunner {
    
    static func runAllTests() async {
        print("🚀 Starting Translation Service Tests...")
        print("=" * 50)
        
        await testGoogleTranslateBasic()
        await testCantoneseTranslation()
        await testNLLBIfAvailable()
        await testHybridServiceRouting()
        await testLanguageDetection()
        
        print("=" * 50)
        print("✅ All tests completed!")
    }
    
    // MARK: - Individual Test Methods
    
    static func testGoogleTranslateBasic() async {
        print("\n📝 Testing Google Translate Basic Functionality...")
        
        let service = HybridTranslationService()
        
        let testCases = [
            ("Hello world", TranslationLanguage.english, TranslationLanguage.spanish),
            ("Good morning", TranslationLanguage.english, TranslationLanguage.french),
            ("Thank you", TranslationLanguage.english, TranslationLanguage.german),
            ("How are you?", TranslationLanguage.english, TranslationLanguage.italian)
        ]
        
        for (text, from, to) in testCases {
            do {
                let result = try await service.translate(text: text, from: from, to: to)
                print("✅ \(from.flag) → \(to.flag): '\(text)' → '\(result)'")
            } catch {
                print("❌ \(from.flag) → \(to.flag): '\(text)' failed: \(error)")
            }
        }
    }
    
    static func testCantoneseTranslation() async {
        print("\n🇭🇰 Testing Cantonese Translation (User's Example)...")
        
        let service = HybridTranslationService()
        let cantoneseText = "粤语写嘢喺明末清初嗰阵已经有一九二零年代到一九三零年代初亦风行一时由二战时期嘅限于传统左派"
        
        print("Original Cantonese: \(cantoneseText)")
        
        // Test Cantonese to Simplified Chinese
        do {
            let result = try await service.translate(
                text: cantoneseText,
                from: .cantonese,
                to: .chineseSimplified
            )
            print("✅ Cantonese → Simplified Chinese: \(result)")
        } catch {
            print("❌ Cantonese → Simplified Chinese failed: \(error)")
        }
        
        // Test Cantonese to English
        do {
            let result = try await service.translate(
                text: cantoneseText,
                from: .cantonese,
                to: .english
            )
            print("✅ Cantonese → English: \(result)")
        } catch {
            print("❌ Cantonese → English failed: \(error)")
        }
    }
    
    static func testNLLBIfAvailable() async {
        print("\n🤖 Testing NLLB Service (if available)...")
        
        let service = HybridTranslationService()
        
        // Test with NLLB-supported languages
        let testCases = [
            ("Hello", TranslationLanguage.english, TranslationLanguage.spanish),
            ("你好", TranslationLanguage.chineseSimplified, TranslationLanguage.english),
            ("こんにちは", TranslationLanguage.japanese, TranslationLanguage.english)
        ]
        
        for (text, from, to) in testCases {
            // Check if both languages are supported by NLLB
            if NLLBLanguageMapper.isNLLBSupported(from.rawValue) && 
               NLLBLanguageMapper.isNLLBSupported(to.rawValue) {
                
                do {
                    let result = try await service.translate(text: text, from: from, to: to)
                    print("✅ NLLB \(from.flag) → \(to.flag): '\(text)' → '\(result)'")
                } catch TranslationError.serverError(let code) {
                    print("⚠️ NLLB Server not available (HTTP \(code))")
                    break
                } catch {
                    print("❌ NLLB \(from.flag) → \(to.flag): '\(text)' failed: \(error)")
                }
            } else {
                print("ℹ️ Language pair \(from.displayName) → \(to.displayName) not supported by NLLB")
            }
        }
    }
    
    static func testHybridServiceRouting() async {
        print("\n🔀 Testing Hybrid Service Language Routing...")
        
        let optimalConfig = TranslationServiceConfig.optimalHybrid
        
        // Test language routing logic
        let asianLanguages: [TranslationLanguage] = [.japanese, .korean, .chineseSimplified, .cantonese]
        let europeanLanguages: [TranslationLanguage] = [.english, .spanish, .french, .german]
        
        print("Asian languages should use NLLB:")
        for lang in asianLanguages {
            let service = optimalConfig.serviceForLanguage(lang)
            print("  \(lang.flag) \(lang.displayName): \(service.displayName)")
        }
        
        print("European languages should use Google:")
        for lang in europeanLanguages {
            let service = optimalConfig.serviceForLanguage(lang)
            print("  \(lang.flag) \(lang.displayName): \(service.displayName)")
        }
    }
    
    static func testLanguageDetection() async {
        print("\n🔍 Testing Language Detection...")
        
        let service = HybridTranslationService()
        
        let testTexts = [
            ("Hello world", "English"),
            ("Bonjour le monde", "French"),
            ("Hola mundo", "Spanish"),
            ("你好世界", "Chinese"),
            ("こんにちは世界", "Japanese"),
            ("안녕하세요 세계", "Korean"),
            ("粤语写嘢", "Cantonese/Chinese")
        ]
        
        for (text, expectedLanguage) in testTexts {
            do {
                let (detectedLanguage, confidence) = try await service.detectLanguage(text: text)
                print("✅ '\(text)' → \(detectedLanguage.flag) \(detectedLanguage.displayName) (confidence: \(String(format: "%.2f", confidence)), expected: \(expectedLanguage))")
            } catch {
                print("❌ Language detection failed for '\(text)': \(error)")
            }
        }
    }
    
    // MARK: - Helper Methods
    
    static func printSeparator() {
        print("-" * 30)
    }
}

// MARK: - String Extension for Repeating Characters

extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}

// MARK: - Manual Test Execution

/// Uncomment this to run tests manually in a playground or test environment
/*
Task {
    await TranslationTestRunner.runAllTests()
}
*/
