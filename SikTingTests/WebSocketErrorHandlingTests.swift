//
//  WebSocketErrorHandlingTests.swift
//  SikTingTests
//
//  Created by <PERSON><PERSON> on 2025/7/18.
//

import Testing
import Foundation
@testable import SikTing

struct WebSocketErrorHandlingTests {
    
    // MARK: - DisconnectionReason Classification Tests
    
    @Test func testNetworkErrorClassification() async throws {
        let manager = WebSocketManager()
        
        // Test URLError network-related errors
        let networkLostError = URLError(.networkConnectionLost)
        let notConnectedError = URLError(.notConnectedToInternet)
        let dataNotAllowedError = URLError(.dataNotAllowed)
        
        #expect(manager.classifyError(networkLostError) == .networkError)
        #expect(manager.classifyError(notConnectedError) == .networkError)
        #expect(manager.classifyError(dataNotAllowedError) == .networkError)
    }
    
    @Test func testTimeoutErrorClassification() async throws {
        let manager = WebSocketManager()
        
        let timeoutError = URLError(.timedOut)
        #expect(manager.classifyError(timeoutError) == .connectionTimeout)
    }
    
    @Test func testServerErrorClassification() async throws {
        let manager = WebSocketManager()
        
        let hostNotFoundError = URLError(.cannotFindHost)
        let cannotConnectError = URLError(.cannotConnectToHost)
        let dnsError = URLError(.dnsLookupFailed)
        
        #expect(manager.classifyError(hostNotFoundError) == .serverError)
        #expect(manager.classifyError(cannotConnectError) == .serverError)
        #expect(manager.classifyError(dnsError) == .serverError)
    }
    
    @Test func testProtocolErrorClassification() async throws {
        let manager = WebSocketManager()
        
        let badServerResponseError = URLError(.badServerResponse)
        let badURLError = URLError(.badURL)
        
        #expect(manager.classifyError(badServerResponseError) == .protocolError)
        #expect(manager.classifyError(badURLError) == .protocolError)
    }
    
    @Test func testGenericErrorClassification() async throws {
        let manager = WebSocketManager()
        
        // Test generic NSError
        let genericError = NSError(domain: "TestDomain", code: 999, userInfo: [NSLocalizedDescriptionKey: "Generic error"])
        #expect(manager.classifyError(genericError) == .networkError)

        // Test error with network description
        let networkDescError = NSError(domain: "TestDomain", code: 999, userInfo: [NSLocalizedDescriptionKey: "Network connection failed"])
        #expect(manager.classifyError(networkDescError) == .networkError)

        // Test error with timeout description
        let timeoutDescError = NSError(domain: "TestDomain", code: 999, userInfo: [NSLocalizedDescriptionKey: "Request timeout occurred"])
        #expect(manager.classifyError(timeoutDescError) == .connectionTimeout)
    }
    
    // MARK: - Reconnection Logic Tests
    
    @Test func testShouldAttemptReconnectionForUserInitiated() async throws {
        let manager = WebSocketManager()
        
        #expect(manager.shouldAttemptReconnection(for: .userInitiated) == false)
    }
    
    @Test func testShouldAttemptReconnectionForSystemErrors() async throws {
        let manager = WebSocketManager()
        
        #expect(manager.shouldAttemptReconnection(for: .networkError) == true)
        #expect(manager.shouldAttemptReconnection(for: .serverError) == true)
        #expect(manager.shouldAttemptReconnection(for: .connectionTimeout) == true)
        #expect(manager.shouldAttemptReconnection(for: .protocolError) == true)
    }
    
    @Test func testShouldAttemptReconnectionForUnknown() async throws {
        let manager = WebSocketManager()
        
        #expect(manager.shouldAttemptReconnection(for: .unknown) == false)
    }
    
    // MARK: - Reconnection Delay Calculation Tests
    
    @Test func testReconnectionDelayForNetworkErrors() async throws {
        let manager = WebSocketManager()
        
        // Test exponential backoff for network errors
        let delay1 = manager.calculateReconnectionDelay(for: .networkError, attempt: 1)
        let delay2 = manager.calculateReconnectionDelay(for: .networkError, attempt: 2)
        let delay3 = manager.calculateReconnectionDelay(for: .networkError, attempt: 3)
        
        #expect(delay1 == 2.0) // 2s * 2^0
        #expect(delay2 == 4.0) // 2s * 2^1
        #expect(delay3 == 8.0) // 2s * 2^2
    }
    
    @Test func testReconnectionDelayForServerErrors() async throws {
        let manager = WebSocketManager()
        
        // Test linear backoff for server errors
        let delay1 = manager.calculateReconnectionDelay(for: .serverError, attempt: 1)
        let delay2 = manager.calculateReconnectionDelay(for: .serverError, attempt: 2)
        let delay3 = manager.calculateReconnectionDelay(for: .serverError, attempt: 3)
        
        #expect(delay1 == 5.0) // 5s * 1
        #expect(delay2 == 10.0) // 5s * 2
        #expect(delay3 == 15.0) // 5s * 3
    }
    
    @Test func testReconnectionDelayForProtocolErrors() async throws {
        let manager = WebSocketManager()
        
        // Test fixed delay for protocol errors
        let delay1 = manager.calculateReconnectionDelay(for: .protocolError, attempt: 1)
        let delay2 = manager.calculateReconnectionDelay(for: .protocolError, attempt: 2)
        
        #expect(delay1 == 3.0)
        #expect(delay2 == 3.0)
    }
    
    // MARK: - WebSocketError Message Tests
    
    @Test func testWebSocketErrorMessages() async throws {
        let invalidURLError = WebSocketError.invalidURL
        let connectionFailedError = WebSocketError.connectionFailed(reason: "Test reason")
        let timeoutError = WebSocketError.connectionTimeout
        let networkUnavailableError = WebSocketError.networkUnavailable
        let serverUnreachableError = WebSocketError.serverUnreachable
        let protocolError = WebSocketError.protocolError("Test protocol error")
        let maxAttemptsError = WebSocketError.maxReconnectAttemptsReached
        let userCancelledError = WebSocketError.userCancelled
        
        #expect(invalidURLError.errorDescription == "Invalid server URL. Please check your connection settings.")
        #expect(connectionFailedError.errorDescription == "Connection failed: Test reason")
        #expect(timeoutError.errorDescription == "Connection timeout. Please check your network and try again.")
        #expect(networkUnavailableError.errorDescription == "Network unavailable. Please check your internet connection.")
        #expect(serverUnreachableError.errorDescription == "Server unreachable. Please try again later.")
        #expect(protocolError.errorDescription == "Communication error: Test protocol error. Please restart the connection.")
        #expect(maxAttemptsError.errorDescription == "Unable to reconnect after multiple attempts. Please check your connection and try again.")
        #expect(userCancelledError.errorDescription == nil)
    }
    
    @Test func testWebSocketErrorUserFriendlyMessages() async throws {
        let invalidURLError = WebSocketError.invalidURL
        let connectionFailedError = WebSocketError.connectionFailed(reason: "Test reason")
        let timeoutError = WebSocketError.connectionTimeout
        let networkUnavailableError = WebSocketError.networkUnavailable
        let serverUnreachableError = WebSocketError.serverUnreachable
        let protocolError = WebSocketError.protocolError("Test protocol error")
        let maxAttemptsError = WebSocketError.maxReconnectAttemptsReached
        let userCancelledError = WebSocketError.userCancelled
        
        #expect(invalidURLError.userFriendlyMessage == "Invalid server URL")
        #expect(connectionFailedError.userFriendlyMessage == "Connection failed")
        #expect(timeoutError.userFriendlyMessage == "Connection timeout")
        #expect(networkUnavailableError.userFriendlyMessage == "Network unavailable")
        #expect(serverUnreachableError.userFriendlyMessage == "Server unavailable")
        #expect(protocolError.userFriendlyMessage == "Communication error")
        #expect(maxAttemptsError.userFriendlyMessage == "Unable to reconnect")
        #expect(userCancelledError.userFriendlyMessage == "Connection cancelled")
    }
    
    // MARK: - Connection State Tests
    
    @Test func testConnectionStateShouldShowError() async throws {
        let userDisconnected = WebSocketConnectionState.disconnected(reason: .userInitiated)
        let networkDisconnected = WebSocketConnectionState.disconnected(reason: .networkError)
        let unknownDisconnected = WebSocketConnectionState.disconnected(reason: nil)
        let connecting = WebSocketConnectionState.connecting
        let connected = WebSocketConnectionState.connected
        let reconnecting = WebSocketConnectionState.reconnecting(attempt: 1, maxAttempts: 5)
        
        #expect(userDisconnected.shouldShowError == false)
        #expect(networkDisconnected.shouldShowError == true)
        #expect(unknownDisconnected.shouldShowError == false)
        #expect(connecting.shouldShowError == false)
        #expect(connected.shouldShowError == false)
        #expect(reconnecting.shouldShowError == false)
    }
    
    @Test func testConnectionStateIsReconnecting() async throws {
        let disconnected = WebSocketConnectionState.disconnected(reason: .networkError)
        let connecting = WebSocketConnectionState.connecting
        let connected = WebSocketConnectionState.connected
        let reconnecting = WebSocketConnectionState.reconnecting(attempt: 1, maxAttempts: 5)
        
        #expect(disconnected.isReconnecting() == false)
        #expect(connecting.isReconnecting() == false)
        #expect(connected.isReconnecting() == false)
        #expect(reconnecting.isReconnecting() == true)
    }
}
