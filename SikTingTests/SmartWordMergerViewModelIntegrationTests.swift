//
//  SmartWordMergerViewModelIntegrationTests.swift
//  SikTingTests
//
//  Created by <PERSON><PERSON> on 2025/7/19.
//

import XCTest
@testable import SikTing

/// Integration tests for SmartWordMerger with ViewModel transcription processing
/// Tests Requirements: 2.1, 6.1, 6.2, 6.3, 6.4, 6.5
class SmartWordMergerViewModelIntegrationTests: XCTestCase {
    
    var viewModel: SpeechRecognitionViewModel!
    
    @MainActor
    override func setUp() {
        super.setUp()
        viewModel = SpeechRecognitionViewModel()
    }
    
    override func tearDown() {
        viewModel = nil
        super.tearDown()
    }
    
    // MARK: - End-to-End Partial Result Processing Tests
    
    @MainActor
    func testEndToEnd_PartialResultProcessing_WithWordMerging() {
        // Test progressive partial results that should trigger word merging
        let partialResults = [
            "some",
            "some thing",
            "some thing else",
            "some thing else here"
        ]
        
        for (index, text) in partialResults.enumerated() {
            let response = TranscriptionResponse(
                text: text,
                timestamp: Date().timeIntervalSince1970 + Double(index),
                mode: "online",
                isFinal: false
            )
            
            viewModel.processTranscriptionForTesting(response)
        }
        
        // Verify that partial results are being processed
        XCTAssertGreaterThan(viewModel.transcriptionEntries.count, 0, "Should have transcription entries")
        
        // Check that the latest entry contains the merged text
        if let lastEntry = viewModel.transcriptionEntries.last {
            // The exact merging behavior depends on dictionary availability
            // We verify that the text is processed without errors
            XCTAssertFalse(lastEntry.text.isEmpty, "Last entry should not be empty")
            XCTAssertTrue(lastEntry.text.contains("thing"), "Should contain expected words")
        }
    }
    
    @MainActor
    func testEndToEnd_FinalResultProcessing_WithWordMerging() {
        // Test final result processing
        let finalResponse = TranscriptionResponse(
            text: "some thing important here",
            timestamp: Date().timeIntervalSince1970,
            mode: "online",
            isFinal: true
        )
        
        viewModel.processTranscriptionForTesting(finalResponse)
        
        // Verify final result is processed
        XCTAssertEqual(viewModel.transcriptionEntries.count, 1, "Should have one final entry")
        
        if let entry = viewModel.transcriptionEntries.first {
            XCTAssertTrue(entry.isFinal, "Entry should be marked as final")
            XCTAssertFalse(entry.text.isEmpty, "Final entry should not be empty")
        }
    }
    
    // MARK: - Online vs Offline Mode Behavior Tests
    
    @MainActor
    func testOnlineMode_PartialResultBehavior() {
        // Test online mode partial results
        let onlineResponse = TranscriptionResponse(
            text: "test online mode",
            timestamp: Date().timeIntervalSince1970,
            mode: "online",
            isFinal: false
        )
        
        viewModel.processTranscriptionForTesting(onlineResponse)
        
        // Verify online mode processing
        XCTAssertGreaterThan(viewModel.transcriptionEntries.count, 0, "Should process online partial results")
        
        if let entry = viewModel.transcriptionEntries.first {
            XCTAssertFalse(entry.isFinal, "Partial result should not be final")
            // Note: mode is preserved in the response, not directly in TranscriptionEntry
        }
    }
    
    @MainActor
    func testOfflineMode_PartialResultBehavior() {
        // Test offline mode partial results
        let offlineResponse = TranscriptionResponse(
            text: "test offline mode",
            timestamp: Date().timeIntervalSince1970,
            mode: "offline",
            isFinal: false
        )
        
        viewModel.processTranscriptionForTesting(offlineResponse)
        
        // Verify offline mode processing
        XCTAssertGreaterThan(viewModel.transcriptionEntries.count, 0, "Should process offline partial results")
        
        if let entry = viewModel.transcriptionEntries.first {
            XCTAssertFalse(entry.isFinal, "Partial result should not be final")
            // Note: mode is preserved in the response, not directly in TranscriptionEntry
        }
    }
    
    @MainActor
    func testModeTransition_OnlineToOffline() {
        // Test transition from online to offline mode
        let onlineResponse = TranscriptionResponse(
            text: "online text",
            timestamp: Date().timeIntervalSince1970,
            mode: "online",
            isFinal: false
        )
        
        let offlineResponse = TranscriptionResponse(
            text: "offline text",
            timestamp: Date().timeIntervalSince1970 + 1,
            mode: "offline",
            isFinal: false
        )
        
        viewModel.processTranscriptionForTesting(onlineResponse)
        viewModel.processTranscriptionForTesting(offlineResponse)
        
        // Verify both modes are handled correctly
        XCTAssertGreaterThanOrEqual(viewModel.transcriptionEntries.count, 1, "Should handle mode transitions")
        
        // Check that entries are created for different modes
        // Note: mode information is not stored directly in TranscriptionEntry
        XCTAssertTrue(viewModel.transcriptionEntries.count >= 1, "Should handle different modes")
    }
    
    // MARK: - Language-Aware Merging Tests
    
    @MainActor
    func testLanguageAware_EnglishWordMerging() {
        // Test English word combinations
        let englishResponse = TranscriptionResponse(
            text: "some thing good",
            timestamp: Date().timeIntervalSince1970,
            mode: "online",
            isFinal: false
        )
        
        viewModel.processTranscriptionForTesting(englishResponse)
        
        // Verify English processing
        XCTAssertGreaterThan(viewModel.transcriptionEntries.count, 0, "Should process English text")
        
        if let entry = viewModel.transcriptionEntries.first {
            XCTAssertTrue(entry.text.contains("thing"), "Should contain English words")
        }
    }
    
    @MainActor
    func testLanguageAware_ChineseWordMerging() {
        // Test Chinese text (should not be merged as it doesn't use space-separated words)
        let chineseResponse = TranscriptionResponse(
            text: "这是中文测试",
            timestamp: Date().timeIntervalSince1970,
            mode: "online",
            isFinal: false
        )
        
        viewModel.processTranscriptionForTesting(chineseResponse)
        
        // Verify Chinese processing
        XCTAssertGreaterThan(viewModel.transcriptionEntries.count, 0, "Should process Chinese text")
        
        if let entry = viewModel.transcriptionEntries.first {
            XCTAssertEqual(entry.text, "这是中文测试", "Chinese text should remain unchanged")
        }
    }
    
    @MainActor
    func testLanguageAware_MixedLanguageInput() {
        // Test mixed language input
        let mixedResponse = TranscriptionResponse(
            text: "hello 世界 some thing",
            timestamp: Date().timeIntervalSince1970,
            mode: "online",
            isFinal: false
        )
        
        viewModel.processTranscriptionForTesting(mixedResponse)
        
        // Verify mixed language processing
        XCTAssertGreaterThan(viewModel.transcriptionEntries.count, 0, "Should process mixed language text")
        
        if let entry = viewModel.transcriptionEntries.first {
            XCTAssertTrue(entry.text.contains("hello"), "Should contain English part")
            XCTAssertTrue(entry.text.contains("世界"), "Should contain Chinese part")
        }
    }
    
    // MARK: - Existing Functionality Preservation Tests
    
    @MainActor
    func testExistingFunctionality_TranscriptionEntryCreation() {
        // Test that basic transcription entry creation still works
        let response = TranscriptionResponse(
            text: "basic transcription test",
            timestamp: Date().timeIntervalSince1970,
            mode: "online",
            isFinal: true
        )
        
        viewModel.processTranscriptionForTesting(response)
        
        // Verify basic functionality
        XCTAssertEqual(viewModel.transcriptionEntries.count, 1, "Should create one entry")
        
        if let entry = viewModel.transcriptionEntries.first {
            XCTAssertEqual(entry.text, "basic transcription test", "Should preserve original text for basic cases")
            XCTAssertTrue(entry.isFinal, "Should preserve final status")
            // Note: mode is preserved in the response, not directly in TranscriptionEntry
        }
    }
    
    @MainActor
    func testExistingFunctionality_TimestampPreservation() {
        // Test that timestamps are preserved correctly
        let timestamp = Date().timeIntervalSince1970
        let response = TranscriptionResponse(
            text: "timestamp test",
            timestamp: timestamp,
            mode: "online",
            isFinal: false
        )
        
        viewModel.processTranscriptionForTesting(response)
        
        // Verify timestamp preservation
        XCTAssertGreaterThan(viewModel.transcriptionEntries.count, 0, "Should create entry")
        
        if let entry = viewModel.transcriptionEntries.first {
            XCTAssertEqual(entry.timestamp.timeIntervalSince1970, timestamp, accuracy: 0.001, "Should preserve timestamp")
        }
    }
    
    @MainActor
    func testExistingFunctionality_EmotionDetection() {
        // Test that emotion detection still works with word merging
        let emotionalResponse = TranscriptionResponse(
            text: "I am very happy today",
            timestamp: Date().timeIntervalSince1970,
            mode: "online",
            isFinal: true
        )
        
        viewModel.processTranscriptionForTesting(emotionalResponse)
        
        // Verify emotion detection functionality
        XCTAssertGreaterThan(viewModel.transcriptionEntries.count, 0, "Should create entry")
        
        if let entry = viewModel.transcriptionEntries.first {
            // Emotion detection should still work regardless of word merging
            XCTAssertNotNil(entry.parsedContent?.emotion, "Should detect emotion")
        }
    }
    
    // MARK: - Performance and Stability Tests
    
    @MainActor
    func testPerformance_MultiplePartialResults() {
        // Test performance with multiple rapid partial results
        let startTime = Date()
        
        for i in 0..<50 {
            let response = TranscriptionResponse(
                text: "partial result number \(i) some thing",
                timestamp: Date().timeIntervalSince1970 + Double(i) * 0.1,
                mode: "online",
                isFinal: false
            )
            
            viewModel.processTranscriptionForTesting(response)
        }
        
        let endTime = Date()
        let processingTime = endTime.timeIntervalSince(startTime)
        
        // Verify performance (should process 50 results in reasonable time)
        XCTAssertLessThan(processingTime, 5.0, "Should process multiple results efficiently")
        XCTAssertGreaterThan(viewModel.transcriptionEntries.count, 0, "Should create entries")
    }
}
