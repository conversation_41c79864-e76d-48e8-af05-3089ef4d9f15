import Foundation
import AVFoundation
import Combine

/// Service for handling audio recording and processing
@MainActor
class AudioService: NSObject, ObservableObject {
    @Published var recordingState: AudioRecordingState = .idle
    @Published var audioLevel: Float = 0.0
    @Published var isRecording: Bool = false
    
    private var audioEngine: AVAudioEngine?
    private var inputNode: AVAudioInputNode?
    private var audioFormat: AVAudioFormat?
    private var audioBuffer: AVAudioPCMBuffer?
    private var audioConverter: AVAudioConverter?
    
    private let webSocketService: WebSocketService
    private var levelTimer: Timer?
    
    // Audio configuration
    private let sampleRate: Double = BackendConfig.audioSampleRate
    private let channels: UInt32 = UInt32(BackendConfig.audioChannels)
    private let bufferSize: AVAudioFrameCount = 1024
    
    init(webSocketService: WebSocketService) {
        self.webSocketService = webSocketService
        super.init()
        setupAudioSession()
    }
    
    deinit {
        stopRecording()
    }
    
    // MARK: - Audio Session Setup
    
    /// Setup audio session for recording
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .measurement, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        } catch {
            recordingState = .error(RockerSTTError.audioError("Failed to setup audio session: \(error.localizedDescription)"))
        }
    }
    
    // MARK: - Recording Control
    
    /// Start audio recording
    func startRecording() {
        guard recordingState == .idle else { return }
        
        do {
            try setupAudioEngine()
            try audioEngine?.start()
            recordingState = .recording
            isRecording = true
            startLevelMonitoring()
        } catch {
            recordingState = .error(RockerSTTError.audioError("Failed to start recording: \(error.localizedDescription)"))
        }
    }
    
    /// Stop audio recording
    func stopRecording() {
        audioEngine?.stop()
        audioEngine?.inputNode.removeTap(onBus: 0)
        audioEngine = nil
        recordingState = .idle
        isRecording = false
        stopLevelMonitoring()
    }
    
    /// Pause recording (keeps engine running but stops sending data)
    func pauseRecording() {
        guard recordingState == .recording else { return }
        recordingState = .processing
        isRecording = false
    }
    
    /// Resume recording
    func resumeRecording() {
        guard recordingState == .processing else { return }
        recordingState = .recording
        isRecording = true
    }
    
    // MARK: - Audio Engine Setup
    
    /// Setup audio engine for recording
    private func setupAudioEngine() throws {
        audioEngine = AVAudioEngine()
        
        guard let audioEngine = audioEngine else {
            throw RockerSTTError.audioError("Failed to create audio engine")
        }
        
        inputNode = audioEngine.inputNode
        
        guard let inputNode = inputNode else {
            throw RockerSTTError.audioError("Failed to get input node")
        }
        
        // Get input format
        let inputFormat = inputNode.outputFormat(forBus: 0)
        
        // Create desired format for WebSocket transmission
        guard let desiredFormat = AVAudioFormat(
            commonFormat: .pcmFormatInt16,
            sampleRate: sampleRate,
            channels: channels,
            interleaved: true
        ) else {
            throw RockerSTTError.audioError("Failed to create audio format")
        }
        
        audioFormat = desiredFormat
        
        // Create converter if needed
        if inputFormat != desiredFormat {
            audioConverter = AVAudioConverter(from: inputFormat, to: desiredFormat)
        }
        
        // Install tap on input node
        inputNode.installTap(onBus: 0, bufferSize: bufferSize, format: inputFormat) { [weak self] buffer, time in
            Task { @MainActor in
                self?.processAudioBuffer(buffer, time: time)
            }
        }
    }
    
    // MARK: - Audio Processing
    
    /// Process audio buffer and send to WebSocket
    private func processAudioBuffer(_ buffer: AVAudioPCMBuffer, time: AVAudioTime) {
        guard recordingState == .recording,
              webSocketService.isConnected else { return }
        
        do {
            let processedBuffer = try convertAudioBuffer(buffer)
            let audioData = try convertBufferToData(processedBuffer)
            
            // Update audio level for UI
            updateAudioLevel(from: processedBuffer)
            
            // Send to WebSocket
            webSocketService.sendAudioData(audioData)
            
        } catch {
            recordingState = .error(RockerSTTError.audioError("Failed to process audio: \(error.localizedDescription)"))
        }
    }
    
    /// Convert audio buffer to desired format
    private func convertAudioBuffer(_ inputBuffer: AVAudioPCMBuffer) throws -> AVAudioPCMBuffer {
        guard let audioFormat = audioFormat else {
            throw RockerSTTError.audioError("Audio format not set")
        }
        
        // If no conversion needed, return original buffer
        guard let converter = audioConverter else {
            return inputBuffer
        }
        
        // Create output buffer
        guard let outputBuffer = AVAudioPCMBuffer(
            pcmFormat: audioFormat,
            frameCapacity: inputBuffer.frameLength
        ) else {
            throw RockerSTTError.audioError("Failed to create output buffer")
        }
        
        // Convert audio
        var error: NSError?
        let status = converter.convert(to: outputBuffer, error: &error) { _, outStatus in
            outStatus.pointee = .haveData
            return inputBuffer
        }
        
        if status == .error {
            throw RockerSTTError.audioError("Audio conversion failed: \(error?.localizedDescription ?? "Unknown error")")
        }
        
        return outputBuffer
    }
    
    /// Convert audio buffer to Data for WebSocket transmission
    private func convertBufferToData(_ buffer: AVAudioPCMBuffer) throws -> Data {
        guard let channelData = buffer.int16ChannelData else {
            throw RockerSTTError.audioError("Failed to get channel data")
        }
        
        let frameLength = Int(buffer.frameLength)
        let channelCount = Int(buffer.format.channelCount)
        
        var data = Data()
        
        for frame in 0..<frameLength {
            for channel in 0..<channelCount {
                let sample = channelData[channel][frame]
                data.append(contentsOf: withUnsafeBytes(of: sample.littleEndian) { Array($0) })
            }
        }
        
        return data
    }
    
    /// Update audio level for UI visualization
    private func updateAudioLevel(from buffer: AVAudioPCMBuffer) {
        guard let channelData = buffer.floatChannelData else { return }
        
        let frameLength = Int(buffer.frameLength)
        let channelCount = Int(buffer.format.channelCount)
        
        var sum: Float = 0.0
        
        for channel in 0..<channelCount {
            for frame in 0..<frameLength {
                let sample = channelData[channel][frame]
                sum += sample * sample
            }
        }
        
        let rms = sqrt(sum / Float(frameLength * channelCount))
        let db = 20 * log10(rms)
        
        // Normalize to 0-1 range (assuming -60dB to 0dB range)
        audioLevel = max(0, min(1, (db + 60) / 60))
    }
    
    // MARK: - Audio Level Monitoring
    
    /// Start monitoring audio levels for UI
    private func startLevelMonitoring() {
        levelTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            // Audio level is updated in processAudioBuffer
            // This timer ensures UI updates even if no audio is being processed
        }
    }
    
    /// Stop monitoring audio levels
    private func stopLevelMonitoring() {
        levelTimer?.invalidate()
        levelTimer = nil
        audioLevel = 0.0
    }
    
    // MARK: - Permission Handling
    
    /// Request microphone permission
    func requestMicrophonePermission() async -> Bool {
        switch AVAudioSession.sharedInstance().recordPermission {
        case .granted:
            return true
        case .denied:
            return false
        case .undetermined:
            return await withCheckedContinuation { continuation in
                AVAudioSession.sharedInstance().requestRecordPermission { granted in
                    continuation.resume(returning: granted)
                }
            }
        @unknown default:
            return false
        }
    }
    
    /// Check if microphone permission is granted
    var hasMicrophonePermission: Bool {
        return AVAudioSession.sharedInstance().recordPermission == .granted
    }
}
