import Foundation

// MARK: - API Models

/// Request model for token exchange
struct TokenRequest: Codable {
    let identityToken: String
}

/// Response model for successful token exchange
struct TokenResponse: Codable {
    let accessToken: String
    let expiresIn: Int
}

/// Error response model
struct APIError: Codable, Error {
    let error: String
    let message: String
    let code: Int
}

// MARK: - WebSocket Models

/// WebSocket message types
enum WebSocketMessageType: String, Codable {
    case audio = "audio"
    case transcription = "transcription"
    case error = "error"
    case ping = "ping"
    case pong = "pong"
}

/// Audio message sent to server
struct AudioMessage: Codable {
    let type: String = "audio"
    let data: String // Base64 encoded audio
    let format: String = "wav"
    let sampleRate: Int = 16000
    let channels: Int = 1
}

/// Transcription result from server
struct TranscriptionMessage: Codable {
    let type: String
    let text: String
    let confidence: Double
    let isFinal: Bool
    let timestamp: String
}

/// Error message from server
struct ErrorMessage: Codable {
    let type: String
    let error: String
    let message: String
    let code: Int
}

/// Generic WebSocket message wrapper
struct WebSocketMessage: Codable {
    let type: String
    let data: Data?
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        type = try container.decode(String.self, forKey: .type)
        
        // Store raw data for type-specific parsing
        data = try? JSONEncoder().encode(container)
    }
    
    private enum CodingKeys: String, CodingKey {
        case type
    }
}

// MARK: - Connection States

/// Authentication state
enum AuthenticationState {
    case notAuthenticated
    case authenticating
    case authenticated(accessToken: String, expiresAt: Date)
    case failed(Error)
}

/// WebSocket connection state
enum WebSocketState {
    case disconnected
    case connecting
    case connected
    case reconnecting
    case failed(Error)
}

/// Audio recording state
enum AudioRecordingState {
    case idle
    case recording
    case processing
    case error(Error)
}

// MARK: - Configuration

/// Backend configuration
struct BackendConfig {
    static let baseURL = "https://your-domain.com"
    static let tokenEndpoint = "/api/token"
    static let webSocketEndpoint = "/ws/asr"
    static let appleBundleID = "com.superspacex.asr"
    
    // Audio configuration
    static let audioSampleRate: Double = 16000
    static let audioChannels: Int = 1
    static let audioFormat = "wav"
    
    // Connection timeouts
    static let connectionTimeout: TimeInterval = 30
    static let tokenRefreshBuffer: TimeInterval = 60 // Refresh 1 minute before expiry
}

// MARK: - User Session

/// User session information
struct UserSession {
    let userID: String
    let email: String?
    let accessToken: String
    let expiresAt: Date
    let sessionID: String?
    
    var isValid: Bool {
        return Date() < expiresAt.addingTimeInterval(-BackendConfig.tokenRefreshBuffer)
    }
    
    var needsRefresh: Bool {
        return Date() > expiresAt.addingTimeInterval(-BackendConfig.tokenRefreshBuffer)
    }
}

// MARK: - Transcription Result

/// Complete transcription result
struct TranscriptionResult {
    let id: UUID = UUID()
    let text: String
    let confidence: Double
    let isFinal: Bool
    let timestamp: Date
    let duration: TimeInterval?
}

// MARK: - Error Types

/// Custom error types for the app
enum RockerSTTError: Error, LocalizedError {
    case authenticationFailed(String)
    case networkError(Error)
    case webSocketError(String)
    case audioError(String)
    case tokenExpired
    case rateLimitExceeded
    case serverUnavailable
    case invalidResponse
    
    var errorDescription: String? {
        switch self {
        case .authenticationFailed(let message):
            return "Authentication failed: \(message)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .webSocketError(let message):
            return "WebSocket error: \(message)"
        case .audioError(let message):
            return "Audio error: \(message)"
        case .tokenExpired:
            return "Access token has expired"
        case .rateLimitExceeded:
            return "Rate limit exceeded. Please try again later."
        case .serverUnavailable:
            return "Server is temporarily unavailable"
        case .invalidResponse:
            return "Invalid response from server"
        }
    }
}

// MARK: - Extensions

extension Date {
    /// Create date from ISO 8601 string
    init?(iso8601String: String) {
        let formatter = ISO8601DateFormatter()
        guard let date = formatter.date(from: iso8601String) else {
            return nil
        }
        self = date
    }
    
    /// Convert to ISO 8601 string
    var iso8601String: String {
        return ISO8601DateFormatter().string(from: self)
    }
}

extension Data {
    /// Convert to base64 string for WebSocket transmission
    var base64String: String {
        return self.base64EncodedString()
    }
}

extension String {
    /// Convert base64 string to Data
    var base64Data: Data? {
        return Data(base64Encoded: self)
    }
}
