import SwiftUI
import AuthenticationServices

/// Main content view for the RockerSTT app
struct ContentView: View {
    @StateObject private var authService = AuthenticationService()
    @StateObject private var webSocketService = WebSocketService()
    @StateObject private var audioService: AudioService
    
    @State private var showingPermissionAlert = false
    @State private var showingErrorAlert = false
    @State private var errorMessage = ""
    
    init() {
        let webSocketService = WebSocketService()
        let audioService = AudioService(webSocketService: webSocketService)
        
        self._webSocketService = StateObject(wrappedValue: webSocketService)
        self._audioService = StateObject(wrappedValue: audioService)
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                headerView
                
                switch authService.authState {
                case .notAuthenticated:
                    signInView
                case .authenticating:
                    authenticatingView
                case .authenticated:
                    mainContentView
                case .failed(let error):
                    errorView(error)
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("RockerSTT")
            .alert("Microphone Permission Required", isPresented: $showingPermissionAlert) {
                Button("Settings") {
                    openAppSettings()
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                Text("Please enable microphone access in Settings to use speech recognition.")
            }
            .alert("Error", isPresented: $showingErrorAlert) {
                Button("OK") { }
            } message: {
                Text(errorMessage)
            }
        }
        .onAppear {
            authService.checkExistingAuthentication()
        }
        .onChange(of: authService.authState) { state in
            handleAuthStateChange(state)
        }
        .onChange(of: webSocketService.connectionState) { state in
            handleWebSocketStateChange(state)
        }
    }
    
    // MARK: - Header View
    
    private var headerView: some View {
        VStack(spacing: 8) {
            Image(systemName: "waveform.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.blue)
            
            Text("RockerSTT")
                .font(.largeTitle)
                .fontWeight(.bold)
            
            Text("Real-time Speech Recognition")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Sign In View
    
    private var signInView: some View {
        VStack(spacing: 20) {
            Text("Sign in to start using speech recognition")
                .font(.headline)
                .multilineTextAlignment(.center)
            
            SignInWithAppleButton(
                onRequest: { request in
                    request.requestedScopes = [.fullName, .email]
                },
                onCompletion: { _ in
                    // Handled by AuthenticationService
                }
            )
            .signInWithAppleButtonStyle(.black)
            .frame(height: 50)
            .cornerRadius(8)
        }
        .padding()
    }
    
    // MARK: - Authenticating View
    
    private var authenticatingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("Authenticating...")
                .font(.headline)
        }
    }
    
    // MARK: - Main Content View
    
    private var mainContentView: some View {
        VStack(spacing: 20) {
            connectionStatusView
            audioVisualizationView
            recordingControlsView
            transcriptionView
            
            Button("Sign Out") {
                signOut()
            }
            .foregroundColor(.red)
        }
    }
    
    // MARK: - Connection Status View
    
    private var connectionStatusView: some View {
        HStack {
            Circle()
                .fill(connectionStatusColor)
                .frame(width: 12, height: 12)
            
            Text(connectionStatusText)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
        }
        .padding(.horizontal)
    }
    
    private var connectionStatusColor: Color {
        switch webSocketService.connectionState {
        case .connected:
            return .green
        case .connecting, .reconnecting:
            return .orange
        case .disconnected:
            return .gray
        case .failed:
            return .red
        }
    }
    
    private var connectionStatusText: String {
        switch webSocketService.connectionState {
        case .connected:
            return "Connected"
        case .connecting:
            return "Connecting..."
        case .reconnecting:
            return "Reconnecting..."
        case .disconnected:
            return "Disconnected"
        case .failed(let error):
            return "Connection failed: \(error.localizedDescription)"
        }
    }
    
    // MARK: - Audio Visualization View
    
    private var audioVisualizationView: some View {
        VStack {
            Text("Audio Level")
                .font(.caption)
                .foregroundColor(.secondary)
            
            ProgressView(value: audioService.audioLevel)
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                .frame(height: 8)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - Recording Controls View
    
    private var recordingControlsView: some View {
        HStack(spacing: 20) {
            Button(action: toggleRecording) {
                Image(systemName: audioService.isRecording ? "stop.circle.fill" : "mic.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(audioService.isRecording ? .red : .blue)
            }
            .disabled(!webSocketService.isConnected)
            
            if audioService.isRecording {
                Button(action: pauseRecording) {
                    Image(systemName: "pause.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(.orange)
                }
            }
        }
    }
    
    // MARK: - Transcription View
    
    private var transcriptionView: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Transcription")
                    .font(.headline)
                
                Spacer()
                
                if !webSocketService.transcriptionResults.isEmpty {
                    Button("Clear") {
                        webSocketService.clearResults()
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
            
            ScrollView {
                VStack(alignment: .leading, spacing: 8) {
                    // Current (live) transcription
                    if !webSocketService.currentTranscription.isEmpty {
                        Text(webSocketService.currentTranscription)
                            .foregroundColor(.secondary)
                            .italic()
                    }
                    
                    // Final transcription results
                    ForEach(webSocketService.transcriptionResults, id: \.id) { result in
                        TranscriptionResultView(result: result)
                    }
                }
            }
            .frame(maxHeight: 200)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - Error View
    
    private func errorView(_ error: Error) -> some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 50))
                .foregroundColor(.red)
            
            Text("Authentication Failed")
                .font(.headline)
            
            Text(error.localizedDescription)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Try Again") {
                authService.signInWithApple()
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
    }
    
    // MARK: - Actions
    
    private func toggleRecording() {
        Task {
            let hasPermission = await audioService.requestMicrophonePermission()
            
            if hasPermission {
                if audioService.isRecording {
                    audioService.stopRecording()
                } else {
                    audioService.startRecording()
                }
            } else {
                showingPermissionAlert = true
            }
        }
    }
    
    private func pauseRecording() {
        if audioService.recordingState == .recording {
            audioService.pauseRecording()
        } else {
            audioService.resumeRecording()
        }
    }
    
    private func signOut() {
        audioService.stopRecording()
        webSocketService.disconnect()
        authService.signOut()
    }
    
    private func openAppSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
    
    // MARK: - State Change Handlers
    
    private func handleAuthStateChange(_ state: AuthenticationState) {
        switch state {
        case .authenticated(let accessToken, _):
            webSocketService.connect(accessToken: accessToken)
        case .notAuthenticated, .failed:
            webSocketService.disconnect()
            audioService.stopRecording()
        default:
            break
        }
    }
    
    private func handleWebSocketStateChange(_ state: WebSocketState) {
        switch state {
        case .failed(let error):
            errorMessage = error.localizedDescription
            showingErrorAlert = true
            audioService.stopRecording()
        default:
            break
        }
    }
}

// MARK: - Transcription Result View

struct TranscriptionResultView: View {
    let result: TranscriptionResult
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(result.text)
                    .font(.body)
                
                Spacer()
                
                Text("\(Int(result.confidence * 100))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Text(result.timestamp.formatted(date: .omitted, time: .shortened))
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Preview

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
    }
}
