# iOS Project Setup Guide for RockerSTT

## 📱 Xcode Project Configuration

### 1. Create New iOS Project

1. Open Xcode and create a new iOS project
2. Choose "App" template
3. Set the following:
   - **Product Name:** RockerSTT (or your preferred name)
   - **Bundle Identifier:** `com.superspacex.asr`
   - **Language:** Swift
   - **Interface:** SwiftUI
   - **Use Core Data:** No (we use PostgreSQL backend)

### 2. Enable Required Capabilities

In your project settings, go to "Signing & Capabilities" and add:

1. **Sign In with Apple**
   - Click "+ Capability"
   - Search for "Sign In with Apple"
   - Add the capability

2. **Background Modes** (Optional, for background audio)
   - Add "Background Modes" capability
   - Enable "Audio, AirPlay, and Picture in Picture"

### 3. Configure Info.plist

Add the following keys to your `Info.plist`:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- Existing keys... -->
    
    <!-- Microphone Permission -->
    <key>NSMicrophoneUsageDescription</key>
    <string>This app needs microphone access to provide real-time speech recognition services.</string>
    
    <!-- Network Security (for development with self-signed certificates) -->
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <false/>
        <key>NSExceptionDomains</key>
        <dict>
            <key>your-development-domain.com</key>
            <dict>
                <key>NSExceptionAllowsInsecureHTTPLoads</key>
                <true/>
                <key>NSExceptionMinimumTLSVersion</key>
                <string>TLSv1.0</string>
                <key>NSExceptionRequiresForwardSecrecy</key>
                <false/>
            </dict>
        </dict>
    </dict>
    
    <!-- Background Audio (Optional) -->
    <key>UIBackgroundModes</key>
    <array>
        <string>audio</string>
    </array>
</dict>
</plist>
```

### 4. Add Required Frameworks

In your project settings, go to "Build Phases" → "Link Binary With Libraries" and add:

- `AuthenticationServices.framework`
- `AVFoundation.framework`
- `Combine.framework` (should be included by default)

## 🔧 Apple Developer Account Setup

### 1. Configure App ID

1. Go to [Apple Developer Portal](https://developer.apple.com/account/)
2. Navigate to "Certificates, Identifiers & Profiles"
3. Click "Identifiers" → "App IDs"
4. Find or create App ID with Bundle ID: `com.superspacex.asr`
5. Enable "Sign In with Apple" capability
6. Save the configuration

### 2. Configure Sign In with Apple

1. In the same App ID configuration
2. Click "Configure" next to "Sign In with Apple"
3. Set up primary App ID (if this is your main app)
4. Configure domains and email sources if needed
5. Save the configuration

### 3. Create Provisioning Profile

1. Go to "Profiles" in Developer Portal
2. Create new "iOS App Development" profile
3. Select your App ID (`com.superspacex.asr`)
4. Select your development certificate
5. Select your test devices
6. Download and install the profile

## 📁 Project File Structure

Organize your project files as follows:

```
RockerSTT/
├── App.swift                      // Main app entry point
├── ContentView.swift              // Main UI view
├── Models/
│   └── RockerSTTModels.swift      // Data models and enums
├── Services/
│   ├── AuthenticationService.swift // Apple Sign In & token management
│   ├── WebSocketService.swift     // WebSocket connection management
│   └── AudioService.swift         // Audio recording and processing
├── Views/
│   ├── SignInView.swift           // Sign in interface (optional)
│   ├── RecordingView.swift        // Recording interface (optional)
│   └── TranscriptionView.swift    // Transcription display (optional)
├── Utilities/
│   ├── Extensions.swift           // Useful extensions
│   └── Constants.swift            // App constants
└── Resources/
    ├── Assets.xcassets            // App icons and images
    └── Info.plist                 // App configuration
```

## 🔐 Environment Configuration

### Development Environment

Create a `Config.swift` file for environment-specific settings:

```swift
import Foundation

struct AppConfig {
    #if DEBUG
    static let baseURL = "https://your-dev-domain.com"
    static let enableLogging = true
    static let allowInsecureConnections = true
    #else
    static let baseURL = "https://your-production-domain.com"
    static let enableLogging = false
    static let allowInsecureConnections = false
    #endif
    
    static let appleBundleID = "com.superspacex.asr"
    static let tokenEndpoint = "/api/token"
    static let webSocketEndpoint = "/ws/asr"
}
```

### Production Environment

For production builds:

1. **Update base URL** to your production domain
2. **Remove NSAppTransportSecurity exceptions** from Info.plist
3. **Disable debug logging**
4. **Use valid SSL certificates**

## 🧪 Testing Setup

### 1. Unit Testing

Create test targets for your services:

```swift
// AuthenticationServiceTests.swift
import XCTest
@testable import RockerSTT

class AuthenticationServiceTests: XCTestCase {
    var authService: AuthenticationService!
    
    override func setUp() {
        super.setUp()
        authService = AuthenticationService()
    }
    
    func testTokenExchange() async throws {
        // Test token exchange logic
        // Use mock Apple tokens for testing
    }
}
```

### 2. UI Testing

Create UI tests for the complete flow:

```swift
// RockerSTTUITests.swift
import XCTest

class RockerSTTUITests: XCTestCase {
    func testSignInFlow() throws {
        let app = XCUIApplication()
        app.launch()
        
        // Test Sign In with Apple button
        let signInButton = app.buttons["Sign in with Apple"]
        XCTAssertTrue(signInButton.exists)
        
        // Note: Actual Apple Sign In testing requires special setup
    }
}
```

### 3. Mock Services for Testing

Create mock services for testing without real backend:

```swift
// MockWebSocketService.swift
class MockWebSocketService: WebSocketService {
    override func connect(accessToken: String) {
        // Simulate connection
        connectionState = .connected
        isConnected = true
    }
    
    override func sendAudioData(_ audioData: Data) {
        // Simulate transcription response
        let mockTranscription = TranscriptionMessage(
            type: "transcription",
            text: "Mock transcription",
            confidence: 0.95,
            isFinal: true,
            timestamp: Date().iso8601String
        )
        handleTranscription(mockTranscription)
    }
}
```

## 🚀 Build and Deployment

### Development Build

1. Select your development team in project settings
2. Choose your device or simulator
3. Build and run (⌘+R)

### TestFlight Distribution

1. **Archive the app** (Product → Archive)
2. **Upload to App Store Connect**
3. **Configure TestFlight settings**
4. **Add internal/external testers**
5. **Submit for review** (for external testing)

### App Store Release

1. **Complete app metadata** in App Store Connect
2. **Add app screenshots** and descriptions
3. **Configure pricing** and availability
4. **Submit for App Store review**
5. **Release** after approval

## 🔍 Debugging and Troubleshooting

### Common Issues

1. **Sign In with Apple not working**
   - Check Bundle ID matches Apple Developer Portal
   - Verify capability is enabled
   - Check provisioning profile includes Sign In with Apple

2. **Microphone permission denied**
   - Check Info.plist has NSMicrophoneUsageDescription
   - Test on physical device (simulator may not have microphone)

3. **WebSocket connection fails**
   - Check network connectivity
   - Verify SSL certificate (use Charles Proxy for debugging)
   - Check backend server is running

4. **Audio recording issues**
   - Test on physical device (simulator limitations)
   - Check audio session configuration
   - Verify microphone permissions

### Debug Logging

Add comprehensive logging for debugging:

```swift
import os.log

extension OSLog {
    static let auth = OSLog(subsystem: "com.superspacex.asr", category: "Authentication")
    static let websocket = OSLog(subsystem: "com.superspacex.asr", category: "WebSocket")
    static let audio = OSLog(subsystem: "com.superspacex.asr", category: "Audio")
}

// Usage:
os_log("Token exchange successful", log: .auth, type: .info)
os_log("WebSocket connected", log: .websocket, type: .info)
os_log("Audio recording started", log: .audio, type: .info)
```

### Network Debugging

Use Charles Proxy or similar tools to debug network requests:

1. **Install Charles Proxy**
2. **Configure iOS device** to use Charles as proxy
3. **Install Charles certificate** on device
4. **Monitor API calls** and WebSocket connections

## 📊 Performance Optimization

### Memory Management

1. **Use weak references** in closures to prevent retain cycles
2. **Dispose of audio buffers** properly
3. **Monitor memory usage** in Instruments

### Battery Optimization

1. **Stop recording** when app goes to background
2. **Disconnect WebSocket** when not needed
3. **Use efficient audio processing**

### Network Optimization

1. **Implement connection pooling** for HTTP requests
2. **Use compression** for audio data if supported
3. **Implement exponential backoff** for reconnections

## 🔒 Security Best Practices

### Token Security

1. **Never log sensitive tokens**
2. **Use Keychain** for persistent storage
3. **Implement token refresh** logic
4. **Clear tokens** on sign out

### Network Security

1. **Use certificate pinning** for production
2. **Validate SSL certificates**
3. **Implement request signing** if required

### Audio Privacy

1. **Clear audio buffers** after processing
2. **Don't store audio data** locally
3. **Inform users** about audio processing

This setup guide provides everything needed to create a production-ready iOS app that integrates with your RockerSTT backend server.
