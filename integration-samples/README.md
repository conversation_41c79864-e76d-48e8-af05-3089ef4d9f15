# RockerSTT iOS Integration Guide

## 🚀 Quick Start

### 1. Xcode Project Setup

1. **Add Required Frameworks:**
   ```swift
   // In your Xcode project, add these frameworks:
   - AuthenticationServices.framework
   - AVFoundation.framework
   - Combine.framework
   ```

2. **Configure Apple Sign In:**
   - Enable "Sign In with Apple" capability in your Xcode project
   - Set Bundle ID to: `com.superspacex.asr`
   - Add Sign In with Apple entitlement

3. **Add Files to Project:**
   - Copy all `.swift` files from this directory to your Xcode project
   - Ensure they're added to your target

### 2. Backend Configuration

Update the `BackendConfig` in `RockerSTTModels.swift`:

```swift
struct BackendConfig {
    static let baseURL = "https://your-domain.com"  // Replace with your domain
    static let tokenEndpoint = "/api/token"
    static let webSocketEndpoint = "/ws/asr"
    static let appleBundleID = "com.superspacex.asr"
}
```

### 3. Info.plist Configuration

Add microphone usage description:

```xml
<key>NSMicrophoneUsageDescription</key>
<string>This app needs microphone access for speech recognition.</string>
```

## 🔐 Authentication Flow

### Step-by-Step Process

1. **User Initiates Sign In**
   ```swift
   authService.signInWithApple()
   ```

2. **Apple Sign In Flow**
   - User authenticates with Apple
   - App receives Apple Identity Token
   - Token contains user ID and email (if granted)

3. **Backend Token Exchange**
   ```swift
   POST https://your-domain.com/api/token
   {
     "identityToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
   }
   ```

4. **Backend Validation**
   - Backend validates Apple Identity Token with Apple's servers
   - Creates user record if new user
   - Generates backend access token (expires in 5 minutes)

5. **WebSocket Connection**
   ```swift
   wss://your-domain.com/ws/asr?token=backend_access_token
   ```

6. **Session Tracking**
   - Backend creates session record for billing
   - Tracks connection duration and usage

### Authentication State Management

```swift
enum AuthenticationState {
    case notAuthenticated
    case authenticating
    case authenticated(accessToken: String, expiresAt: Date)
    case failed(Error)
}
```

## 🔌 WebSocket Integration

### Connection Lifecycle

1. **Establish Connection**
   ```swift
   webSocketService.connect(accessToken: "your_token")
   ```

2. **Send Audio Data**
   ```swift
   let audioMessage = AudioMessage(data: audioData.base64String)
   webSocketService.sendAudioData(audioData)
   ```

3. **Receive Transcriptions**
   ```swift
   // Live transcription (not final)
   {
     "type": "transcription",
     "text": "Hello wor...",
     "confidence": 0.85,
     "isFinal": false
   }
   
   // Final transcription
   {
     "type": "transcription", 
     "text": "Hello world",
     "confidence": 0.95,
     "isFinal": true
   }
   ```

### Error Handling

```swift
// Token expired - need to re-authenticate
{
  "type": "error",
  "error": "AUTHENTICATION_FAILED",
  "code": 401
}

// Rate limit exceeded
{
  "type": "error",
  "error": "RATE_LIMIT_EXCEEDED", 
  "code": 429
}

// FunASR service unavailable
{
  "type": "error",
  "error": "FUNASR_UNAVAILABLE",
  "code": 503
}
```

## 🎤 Audio Processing

### Audio Configuration

- **Sample Rate:** 16,000 Hz
- **Channels:** 1 (Mono)
- **Format:** 16-bit PCM
- **Encoding:** Base64 for WebSocket transmission

### Recording Flow

1. **Request Permission**
   ```swift
   let hasPermission = await audioService.requestMicrophonePermission()
   ```

2. **Start Recording**
   ```swift
   audioService.startRecording()
   ```

3. **Process Audio**
   - Audio captured in 1024-frame buffers
   - Converted to 16kHz mono PCM
   - Encoded as Base64
   - Sent via WebSocket

4. **Stop Recording**
   ```swift
   audioService.stopRecording()
   ```

## 📱 Session Management

### Backend Session Tracking

The backend automatically tracks sessions for billing:

```sql
-- Session table structure
CREATE TABLE sessions (
    session_id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    duration_secs INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### iOS Session Lifecycle

1. **Session Start**
   - WebSocket connection established
   - Backend creates session record
   - `start_time` recorded

2. **Session Active**
   - Audio data flowing
   - Transcriptions received
   - Connection monitored with ping/pong

3. **Session End**
   - WebSocket disconnected
   - Backend calculates duration
   - `end_time` and `duration_secs` updated

### Background/Foreground Handling

```swift
// In your App.swift or SceneDelegate
.onReceive(NotificationCenter.default.publisher(for: UIApplication.willResignActiveNotification)) { _ in
    // App going to background
    audioService.stopRecording()
    // Keep WebSocket connected for quick resume
}

.onReceive(NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)) { _ in
    // App returning to foreground
    // WebSocket should auto-reconnect if needed
    if authService.userSession?.needsRefresh == true {
        // Re-authenticate if token expired
        authService.signInWithApple()
    }
}
```

## 🔧 Configuration Options

### Rate Limiting

- **API Endpoints:** 10 requests/second
- **WebSocket:** 5 connections/second
- **Burst:** 10 requests allowed

### Token Management

- **Expiry:** 5 minutes
- **Refresh Buffer:** 1 minute before expiry
- **Auto-refresh:** Not implemented (requires re-authentication)

### SSL/TLS

- **Production:** Use valid SSL certificates
- **Development:** Self-signed certificates supported
- **WebSocket:** WSS (secure WebSocket) required for production

## 🚨 Error Handling Best Practices

### Network Errors

```swift
switch error {
case .networkError(let networkError):
    // Show retry option
    // Check internet connectivity
case .rateLimitExceeded:
    // Show "too many requests" message
    // Implement exponential backoff
case .serverUnavailable:
    // Show "service unavailable" message
    // Retry after delay
}
```

### Authentication Errors

```swift
switch error {
case .authenticationFailed:
    // Clear stored credentials
    // Show sign-in screen
case .tokenExpired:
    // Attempt re-authentication
    // Clear session data
}
```

### Audio Errors

```swift
switch error {
case .audioError(let message):
    // Check microphone permission
    // Verify audio session configuration
    // Show user-friendly error message
}
```

## 📊 Monitoring & Analytics

### Connection Metrics

Track these metrics for monitoring:

- Connection success rate
- Average connection time
- Reconnection frequency
- Audio quality metrics
- Transcription accuracy

### User Experience Metrics

- Time to first transcription
- Session duration
- Error frequency
- User retention

## 🔒 Security Considerations

### Token Security

- Never store Apple Identity Tokens
- Backend access tokens are short-lived (5 minutes)
- Use HTTPS/WSS for all communications
- Implement proper certificate validation

### Audio Privacy

- Audio data is not stored on device
- Transmitted securely via WebSocket
- Processed by FunASR and discarded
- No audio data persistence on backend

### User Data

- Minimal user data collection
- Apple ID and email only (if granted)
- Session metadata for billing only
- Comply with privacy regulations

## 🧪 Testing

### Unit Tests

Test individual components:

```swift
// Test authentication service
func testTokenExchange() async throws {
    let authService = AuthenticationService()
    // Mock Apple token
    // Verify backend token received
}

// Test WebSocket service
func testWebSocketConnection() async throws {
    let webSocketService = WebSocketService()
    // Mock connection
    // Verify message handling
}
```

### Integration Tests

Test complete flows:

```swift
// Test end-to-end authentication
func testCompleteAuthFlow() async throws {
    // Sign in with Apple
    // Exchange for backend token
    // Connect WebSocket
    // Verify session created
}
```

### Manual Testing

1. Test with real Apple Sign In
2. Verify WebSocket connection
3. Test audio recording and transcription
4. Test error scenarios (network loss, token expiry)
5. Test background/foreground transitions

## 📚 Additional Resources

- [Apple Sign In Documentation](https://developer.apple.com/sign-in-with-apple/)
- [AVAudioEngine Documentation](https://developer.apple.com/documentation/avfaudio/avaudioengine)
- [WebSocket RFC 6455](https://tools.ietf.org/html/rfc6455)
- [RockerSTT Backend API Documentation](../FUNASR_INTEGRATION.md)
