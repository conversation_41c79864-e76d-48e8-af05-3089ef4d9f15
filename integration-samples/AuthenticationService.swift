import Foundation
import AuthenticationServices
import Combine

/// Service for handling Apple Sign In and backend authentication
@MainActor
class AuthenticationService: NSObject, ObservableObject {
    @Published var authState: AuthenticationState = .notAuthenticated
    @Published var userSession: UserSession?
    
    private var cancellables = Set<AnyCancellable>()
    private let urlSession = URLSession.shared
    
    // MARK: - Apple Sign In
    
    /// Initiate Apple Sign In flow
    func signInWithApple() {
        authState = .authenticating
        
        let request = ASAuthorizationAppleIDProvider().createRequest()
        request.requestedScopes = [.fullName, .email]
        
        let authorizationController = ASAuthorizationController(authorizationRequests: [request])
        authorizationController.delegate = self
        authorizationController.presentationContextProvider = self
        authorizationController.performRequests()
    }
    
    /// Sign out user
    func signOut() {
        userSession = nil
        authState = .notAuthenticated
    }
    
    /// Check if user is already signed in
    func checkExistingAuthentication() {
        guard let userSession = userSession else {
            authState = .notAuthenticated
            return
        }
        
        if userSession.isValid {
            authState = .authenticated(accessToken: userSession.accessToken, expiresAt: userSession.expiresAt)
        } else {
            // Token expired, need to re-authenticate
            self.userSession = nil
            authState = .notAuthenticated
        }
    }
    
    /// Refresh access token if needed
    func refreshTokenIfNeeded() async {
        guard let session = userSession, session.needsRefresh else { return }
        
        // For Apple Sign In, we need to re-authenticate
        // In a production app, you might want to implement silent refresh
        authState = .notAuthenticated
        userSession = nil
    }
    
    // MARK: - Backend Token Exchange
    
    /// Exchange Apple identity token for backend access token
    private func exchangeAppleToken(_ identityToken: String, userID: String, email: String?) async {
        do {
            let tokenRequest = TokenRequest(identityToken: identityToken)
            let tokenResponse = try await performTokenExchange(tokenRequest)
            
            let expiresAt = Date().addingTimeInterval(TimeInterval(tokenResponse.expiresIn))
            let session = UserSession(
                userID: userID,
                email: email,
                accessToken: tokenResponse.accessToken,
                expiresAt: expiresAt,
                sessionID: nil
            )
            
            userSession = session
            authState = .authenticated(accessToken: tokenResponse.accessToken, expiresAt: expiresAt)
            
        } catch {
            authState = .failed(error)
        }
    }
    
    /// Perform the actual token exchange API call
    private func performTokenExchange(_ request: TokenRequest) async throws -> TokenResponse {
        guard let url = URL(string: "\(BackendConfig.baseURL)\(BackendConfig.tokenEndpoint)") else {
            throw RockerSTTError.invalidResponse
        }
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.setValue("application/json", forHTTPHeaderField: "Accept")
        urlRequest.timeoutInterval = BackendConfig.connectionTimeout
        
        let requestData = try JSONEncoder().encode(request)
        urlRequest.httpBody = requestData
        
        let (data, response) = try await urlSession.data(for: urlRequest)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw RockerSTTError.networkError(URLError(.badServerResponse))
        }
        
        switch httpResponse.statusCode {
        case 200:
            return try JSONDecoder().decode(TokenResponse.self, from: data)
        case 400:
            let error = try JSONDecoder().decode(APIError.self, from: data)
            throw RockerSTTError.authenticationFailed(error.message)
        case 401:
            let error = try JSONDecoder().decode(APIError.self, from: data)
            throw RockerSTTError.authenticationFailed(error.message)
        case 429:
            throw RockerSTTError.rateLimitExceeded
        case 500...599:
            throw RockerSTTError.serverUnavailable
        default:
            throw RockerSTTError.invalidResponse
        }
    }
}

// MARK: - ASAuthorizationControllerDelegate

extension AuthenticationService: ASAuthorizationControllerDelegate {
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        guard let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential,
              let identityTokenData = appleIDCredential.identityToken,
              let identityToken = String(data: identityTokenData, encoding: .utf8) else {
            authState = .failed(RockerSTTError.authenticationFailed("Failed to get identity token"))
            return
        }
        
        let userID = appleIDCredential.user
        let email = appleIDCredential.email
        
        Task {
            await exchangeAppleToken(identityToken, userID: userID, email: email)
        }
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        authState = .failed(RockerSTTError.authenticationFailed(error.localizedDescription))
    }
}

// MARK: - ASAuthorizationControllerPresentationContextProviding

extension AuthenticationService: ASAuthorizationControllerPresentationContextProviding {
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return UIWindow()
        }
        return window
    }
}

// MARK: - Keychain Storage (Optional)

extension AuthenticationService {
    /// Save user session to keychain for persistence
    private func saveSessionToKeychain(_ session: UserSession) {
        // Implementation for keychain storage
        // This is optional but recommended for production apps
    }
    
    /// Load user session from keychain
    private func loadSessionFromKeychain() -> UserSession? {
        // Implementation for keychain retrieval
        // This is optional but recommended for production apps
        return nil
    }
}
