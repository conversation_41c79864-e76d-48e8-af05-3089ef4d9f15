import Foundation
import Combine

/// Service for managing WebSocket connection to RockerSTT backend
@MainActor
class WebSocketService: NSObject, ObservableObject {
    @Published var connectionState: WebSocketState = .disconnected
    @Published var transcriptionResults: [TranscriptionResult] = []
    @Published var currentTranscription: String = ""
    @Published var isConnected: Bool = false
    
    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession
    private var accessToken: String?
    private var reconnectAttempts = 0
    private let maxReconnectAttempts = 5
    private var reconnectTimer: Timer?
    private var pingTimer: Timer?
    
    private var cancellables = Set<AnyCancellable>()
    
    override init() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = BackendConfig.connectionTimeout
        config.timeoutIntervalForResource = BackendConfig.connectionTimeout
        self.urlSession = URLSession(configuration: config)
        super.init()
    }
    
    deinit {
        disconnect()
    }
    
    // MARK: - Connection Management
    
    /// Connect to WebSocket with access token
    func connect(accessToken: String) {
        self.accessToken = accessToken
        connectionState = .connecting
        
        guard let url = buildWebSocketURL(accessToken: accessToken) else {
            connectionState = .failed(RockerSTTError.invalidResponse)
            return
        }
        
        var request = URLRequest(url: url)
        request.timeoutInterval = BackendConfig.connectionTimeout
        
        webSocketTask = urlSession.webSocketTask(with: request)
        webSocketTask?.delegate = self
        webSocketTask?.resume()
        
        startListening()
        startPingTimer()
    }
    
    /// Disconnect from WebSocket
    func disconnect() {
        connectionState = .disconnected
        isConnected = false
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
        stopReconnectTimer()
        stopPingTimer()
        reconnectAttempts = 0
    }
    
    /// Reconnect with exponential backoff
    private func reconnect() {
        guard reconnectAttempts < maxReconnectAttempts,
              let accessToken = accessToken else {
            connectionState = .failed(RockerSTTError.webSocketError("Max reconnection attempts reached"))
            return
        }
        
        connectionState = .reconnecting
        reconnectAttempts += 1
        
        let delay = min(pow(2.0, Double(reconnectAttempts)), 30.0) // Max 30 seconds
        
        reconnectTimer = Timer.scheduledTimer(withTimeInterval: delay, repeats: false) { [weak self] _ in
            Task { @MainActor in
                self?.connect(accessToken: accessToken)
            }
        }
    }
    
    // MARK: - Message Handling
    
    /// Send audio data to server
    func sendAudioData(_ audioData: Data) {
        guard isConnected else { return }
        
        let audioMessage = AudioMessage(data: audioData.base64String)
        
        do {
            let messageData = try JSONEncoder().encode(audioMessage)
            let message = URLSessionWebSocketTask.Message.data(messageData)
            
            webSocketTask?.send(message) { [weak self] error in
                if let error = error {
                    Task { @MainActor in
                        self?.handleError(RockerSTTError.webSocketError(error.localizedDescription))
                    }
                }
            }
        } catch {
            handleError(RockerSTTError.webSocketError("Failed to encode audio message"))
        }
    }
    
    /// Start listening for messages
    private func startListening() {
        webSocketTask?.receive { [weak self] result in
            Task { @MainActor in
                switch result {
                case .success(let message):
                    self?.handleMessage(message)
                    self?.startListening() // Continue listening
                case .failure(let error):
                    self?.handleError(RockerSTTError.webSocketError(error.localizedDescription))
                }
            }
        }
    }
    
    /// Handle incoming WebSocket message
    private func handleMessage(_ message: URLSessionWebSocketTask.Message) {
        switch message {
        case .data(let data):
            handleDataMessage(data)
        case .string(let string):
            if let data = string.data(using: .utf8) {
                handleDataMessage(data)
            }
        @unknown default:
            break
        }
    }
    
    /// Handle data message from server
    private func handleDataMessage(_ data: Data) {
        do {
            let decoder = JSONDecoder()
            
            // Try to decode as generic message first to get type
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
               let type = json["type"] as? String {
                
                switch type {
                case "transcription":
                    let transcription = try decoder.decode(TranscriptionMessage.self, from: data)
                    handleTranscription(transcription)
                    
                case "error":
                    let error = try decoder.decode(ErrorMessage.self, from: data)
                    handleServerError(error)
                    
                case "pong":
                    // Handle pong response
                    break
                    
                default:
                    print("Unknown message type: \(type)")
                }
            }
        } catch {
            print("Failed to decode message: \(error)")
        }
    }
    
    /// Handle transcription result
    private func handleTranscription(_ transcription: TranscriptionMessage) {
        let result = TranscriptionResult(
            text: transcription.text,
            confidence: transcription.confidence,
            isFinal: transcription.isFinal,
            timestamp: Date(iso8601String: transcription.timestamp) ?? Date()
        )
        
        if transcription.isFinal {
            transcriptionResults.append(result)
            currentTranscription = ""
        } else {
            currentTranscription = transcription.text
        }
    }
    
    /// Handle server error
    private func handleServerError(_ error: ErrorMessage) {
        let rockerError: RockerSTTError
        
        switch error.code {
        case 401:
            rockerError = .tokenExpired
        case 429:
            rockerError = .rateLimitExceeded
        case 503:
            rockerError = .serverUnavailable
        default:
            rockerError = .webSocketError(error.message)
        }
        
        handleError(rockerError)
    }
    
    /// Handle errors
    private func handleError(_ error: RockerSTTError) {
        connectionState = .failed(error)
        isConnected = false
        
        // Attempt reconnection for certain errors
        switch error {
        case .networkError, .serverUnavailable:
            reconnect()
        case .tokenExpired:
            // Don't reconnect, user needs to re-authenticate
            break
        default:
            break
        }
    }
    
    // MARK: - Utility Methods
    
    /// Build WebSocket URL with access token
    private func buildWebSocketURL(accessToken: String) -> URL? {
        guard var components = URLComponents(string: BackendConfig.baseURL) else {
            return nil
        }
        
        // Convert HTTP to WebSocket scheme
        components.scheme = components.scheme == "https" ? "wss" : "ws"
        components.path = BackendConfig.webSocketEndpoint
        components.queryItems = [
            URLQueryItem(name: "token", value: accessToken)
        ]
        
        return components.url
    }
    
    /// Start ping timer to keep connection alive
    private func startPingTimer() {
        pingTimer = Timer.scheduledTimer(withTimeInterval: 30, repeats: true) { [weak self] _ in
            self?.sendPing()
        }
    }
    
    /// Stop ping timer
    private func stopPingTimer() {
        pingTimer?.invalidate()
        pingTimer = nil
    }
    
    /// Send ping to keep connection alive
    private func sendPing() {
        guard isConnected else { return }
        
        webSocketTask?.sendPing { [weak self] error in
            if let error = error {
                Task { @MainActor in
                    self?.handleError(RockerSTTError.webSocketError("Ping failed: \(error.localizedDescription)"))
                }
            }
        }
    }
    
    /// Stop reconnect timer
    private func stopReconnectTimer() {
        reconnectTimer?.invalidate()
        reconnectTimer = nil
    }
    
    /// Clear transcription results
    func clearResults() {
        transcriptionResults.removeAll()
        currentTranscription = ""
    }
}

// MARK: - URLSessionWebSocketDelegate

extension WebSocketService: URLSessionWebSocketDelegate {
    func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didOpenWithProtocol protocol: String?) {
        Task { @MainActor in
            connectionState = .connected
            isConnected = true
            reconnectAttempts = 0
        }
    }
    
    func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didCloseWith closeCode: URLSessionWebSocketTask.CloseCode, reason: Data?) {
        Task { @MainActor in
            connectionState = .disconnected
            isConnected = false
            
            // Attempt reconnection if not intentionally closed
            if closeCode != .goingAway {
                reconnect()
            }
        }
    }
}
