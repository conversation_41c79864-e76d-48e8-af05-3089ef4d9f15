# RockerSTT API Reference

## 🔐 Authentication API

### POST /api/token

Exchange Apple Identity Token for backend access token.

**URL:** `https://your-domain.com/api/token`

**Method:** `POST`

**Headers:**
```
Content-Type: application/json
Accept: application/json
```

**Request Body:**
```json
{
  "identityToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwczovL2FwcGxlaWQuYXBwbGUuY29tIiwiYXVkIjoiY29tLnN1cGVyc3BhY2V4LmFzciIsImV4cCI6MTY5MTAwMDAwMCwiaWF0IjoxNjkwOTk5NDAwLCJzdWIiOiIwMDEyMzQuNTY3ODkwYWJjZGVmIiwiY19oYXNoIjoiYWJjZGVmZ2hpams..."
}
```

**Success Response (200 OK):**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIwMDEyMzQuNTY3ODkwYWJjZGVmIiwiZXhwIjoxNjkxMDAwMzAwLCJpYXQiOjE2OTEwMDAwMDAsImJ1bmRsZUlkIjoiY29tLnN1cGVyc3BhY2V4LmFzciJ9...",
  "expiresIn": 300
}
```

**Error Responses:**

**400 Bad Request - Missing or invalid request:**
```json
{
  "error": "INVALID_REQUEST",
  "message": "identityToken is required",
  "code": 400
}
```

**401 Unauthorized - Invalid Apple token:**
```json
{
  "error": "INVALID_TOKEN",
  "message": "Apple identity token validation failed: Invalid signature",
  "code": 401
}
```

**429 Too Many Requests - Rate limit exceeded:**
```json
{
  "error": "RATE_LIMIT_EXCEEDED",
  "message": "Too many requests. Limit: 10 requests per second",
  "code": 429
}
```

**500 Internal Server Error:**
```json
{
  "error": "INTERNAL_ERROR",
  "message": "Internal server error",
  "code": 500
}
```

**Rate Limiting:**
- **Limit:** 10 requests per second
- **Burst:** 10 requests
- **Headers:** Rate limit info included in response headers

**Example cURL:**
```bash
curl -X POST https://your-domain.com/api/token \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "identityToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
  }'
```

---

## 🔌 WebSocket API

### WebSocket /ws/asr

Real-time speech recognition via WebSocket connection.

**URL:** `wss://your-domain.com/ws/asr?token=ACCESS_TOKEN`

**Protocol:** WebSocket (RFC 6455)

**Authentication:** Bearer token in query parameter

**Connection Example:**
```javascript
const ws = new WebSocket('wss://your-domain.com/ws/asr?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...');
```

### Message Types

#### 1. Audio Message (Client → Server)

Send audio data for transcription.

```json
{
  "type": "audio",
  "data": "UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA=",
  "format": "wav",
  "sampleRate": 16000,
  "channels": 1
}
```

**Fields:**
- `type`: Always "audio"
- `data`: Base64-encoded audio data (16-bit PCM, 16kHz, mono)
- `format`: Audio format ("wav")
- `sampleRate`: Sample rate in Hz (16000)
- `channels`: Number of channels (1 for mono)

#### 2. Transcription Message (Server → Client)

Receive transcription results.

**Partial Result:**
```json
{
  "type": "transcription",
  "text": "Hello wor",
  "confidence": 0.85,
  "isFinal": false,
  "timestamp": "2025-08-02T01:30:00.123Z"
}
```

**Final Result:**
```json
{
  "type": "transcription",
  "text": "Hello world, how are you today?",
  "confidence": 0.95,
  "isFinal": true,
  "timestamp": "2025-08-02T01:30:05.456Z"
}
```

**Fields:**
- `type`: Always "transcription"
- `text`: Transcribed text
- `confidence`: Confidence score (0.0 to 1.0)
- `isFinal`: Whether this is the final result
- `timestamp`: ISO 8601 timestamp

#### 3. Error Message (Server → Client)

Receive error notifications.

```json
{
  "type": "error",
  "error": "AUTHENTICATION_FAILED",
  "message": "Invalid or expired access token",
  "code": 401
}
```

**Common Error Codes:**
- `401`: Authentication failed (invalid/expired token)
- `429`: Rate limit exceeded
- `503`: FunASR service unavailable
- `500`: Internal server error

#### 4. Ping/Pong Messages

Keep connection alive.

**Ping (Client → Server):**
```json
{
  "type": "ping",
  "timestamp": "2025-08-02T01:30:00.000Z"
}
```

**Pong (Server → Client):**
```json
{
  "type": "pong",
  "timestamp": "2025-08-02T01:30:00.000Z"
}
```

### Connection States

1. **Connecting**: WebSocket handshake in progress
2. **Connected**: Ready to send/receive messages
3. **Reconnecting**: Attempting to reconnect after disconnection
4. **Disconnected**: Connection closed
5. **Failed**: Connection failed with error

### Rate Limiting

- **Connection Rate:** 5 connections per second
- **Burst:** 10 connections
- **Message Rate:** No specific limit (controlled by audio buffer size)

### Error Handling

**Connection Errors:**
```json
{
  "type": "error",
  "error": "CONNECTION_FAILED",
  "message": "WebSocket connection failed",
  "code": 1006
}
```

**Authentication Errors:**
```json
{
  "type": "error",
  "error": "AUTHENTICATION_FAILED",
  "message": "Invalid access token",
  "code": 401
}
```

**Service Errors:**
```json
{
  "type": "error",
  "error": "FUNASR_UNAVAILABLE",
  "message": "Speech recognition service temporarily unavailable",
  "code": 503
}
```

---

## 🏥 Health Check APIs

### GET /health

Basic health check endpoint.

**URL:** `https://your-domain.com/health`

**Method:** `GET`

**Response (200 OK):**
```json
{
  "status": "healthy",
  "timestamp": "2025-08-02T01:30:00.000Z",
  "version": "1.0.0",
  "uptime": "2h15m30s"
}
```

### GET /ready

Readiness check with dependency status.

**URL:** `https://your-domain.com/ready`

**Method:** `GET`

**Response (200 OK):**
```json
{
  "status": "ready",
  "timestamp": "2025-08-02T01:30:00.000Z",
  "checks": {
    "database": "healthy",
    "funasr": "healthy"
  }
}
```

**Response (503 Service Unavailable):**
```json
{
  "status": "not ready",
  "timestamp": "2025-08-02T01:30:00.000Z",
  "checks": {
    "database": "healthy",
    "funasr": "unhealthy: connection timeout"
  }
}
```

### GET /api/v1/status

API status endpoint.

**URL:** `https://your-domain.com/api/v1/status`

**Method:** `GET`

**Response (200 OK):**
```json
{
  "service": "rockerstt-backend",
  "status": "running",
  "uptime": "2h15m30s",
  "version": "1.0.0",
  "timestamp": "2025-08-02T01:30:00.000Z"
}
```

---

## 📊 Metrics API

### GET /metrics

System metrics (restricted to private networks).

**URL:** `https://your-domain.com/metrics`

**Method:** `GET`

**Access:** Restricted to private IP ranges (10.0.0.0/8, **********/12, ***********/16)

**Response (200 OK):**
```json
{
  "uptime": "2h15m30s",
  "active_sessions": 5,
  "total_users": 1250,
  "total_sessions": 8934,
  "database_status": "healthy"
}
```

**Response (403 Forbidden):**
```json
{
  "error": "FORBIDDEN",
  "message": "Access denied from this IP address",
  "code": 403
}
```

---

## 🔧 Configuration

### Backend Configuration

The backend is configured with the following settings:

- **Apple Bundle ID:** `com.superspacex.asr`
- **Token Expiry:** 5 minutes (300 seconds)
- **FunASR URL:** `ws://host.docker.internal:10096`
- **Database:** PostgreSQL with session tracking
- **SSL:** HTTPS/WSS required for production

### Rate Limiting

| Endpoint | Limit | Burst | Window |
|----------|-------|-------|--------|
| `/api/token` | 10 req/s | 10 | 1 second |
| `/ws/asr` | 5 conn/s | 10 | 1 second |
| `/health` | No limit | - | - |
| `/metrics` | No limit | - | - |

### CORS Configuration

The backend supports CORS for web clients:

```
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
Access-Control-Max-Age: 86400
```

### SSL/TLS Configuration

- **Minimum TLS Version:** 1.2
- **Cipher Suites:** Modern, secure ciphers only
- **HSTS:** Enabled for production
- **Certificate:** Valid SSL certificate required for production

---

## 🧪 Testing

### Test Endpoints

For development and testing, you can use these endpoints:

**Test Token (Development Only):**
```bash
curl -X POST https://dev.your-domain.com/api/token \
  -H "Content-Type: application/json" \
  -d '{"identityToken": "test_token_for_development"}'
```

**Test WebSocket Connection:**
```javascript
// Test WebSocket connection
const ws = new WebSocket('wss://dev.your-domain.com/ws/asr?token=test_token');

ws.onopen = () => console.log('Connected');
ws.onmessage = (event) => console.log('Message:', event.data);
ws.onerror = (error) => console.log('Error:', error);
```

### Mock Responses

For client-side testing, you can use these mock responses:

```javascript
// Mock transcription responses
const mockResponses = [
  {
    type: "transcription",
    text: "Hello",
    confidence: 0.9,
    isFinal: false,
    timestamp: new Date().toISOString()
  },
  {
    type: "transcription", 
    text: "Hello world",
    confidence: 0.95,
    isFinal: true,
    timestamp: new Date().toISOString()
  }
];
```

This API reference provides complete documentation for integrating with the RockerSTT backend server.
