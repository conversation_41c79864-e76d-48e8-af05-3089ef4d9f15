import SwiftUI

@main
struct RockerSTTApp: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }
}

// MARK: - App Configuration

extension RockerSTTApp {
    /// Configure app for production use
    static func configureForProduction() {
        // Update BackendConfig.baseURL to your production domain
        // Example: BackendConfig.baseURL = "https://api.yourdomain.com"
    }
}
