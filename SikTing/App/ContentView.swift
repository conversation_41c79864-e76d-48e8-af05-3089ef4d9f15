//
//  ContentView.swift
//  SikTing
//
//  Created by Rocker on 2025/6/28.
//

import SwiftUI

struct ContentView: View {
    @EnvironmentObject var authService: SecureAuthenticationService
    @State private var showingOnboarding = false

    init() {
        // For large titles
        UINavigationBar.appearance().largeTitleTextAttributes = [.foregroundColor: UIColor(DesignSystem.brandColors.persianPurple)]
        // For inline titles
        UINavigationBar.appearance().titleTextAttributes = [.foregroundColor: UIColor(DesignSystem.brandColors.persianPurple)]
    }

    var body: some View {
        MainTabView()
            .fullScreenCover(isPresented: $showingOnboarding) {
                OnboardingView(
                    isPresented: $showingOnboarding,
                    authService: authService,
                    onComplete: {
                        showingOnboarding = false
                    }
                )
            }
            .onAppear {
                // Show onboarding if this is the first launch
                if authService.isFirstLaunch {
                    showingOnboarding = true
                }
            }
            .onChange(of: authService.isFirstLaunch) { isFirstLaunch in
                if isFirstLaunch {
                    showingOnboarding = true
                }
            }
    }
}

#Preview {
    ContentView()
}
