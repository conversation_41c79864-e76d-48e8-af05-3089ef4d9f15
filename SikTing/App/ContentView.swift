//
//  ContentView.swift
//  SikTing
//
//  Created by Rocker on 2025/6/28.
//

import SwiftUI

struct ContentView: View {
    init() {
        // For large titles
        UINavigationBar.appearance().largeTitleTextAttributes = [.foregroundColor: UIColor(DesignSystem.brandColors.persianPurple)]
        // For inline titles
        UINavigationBar.appearance().titleTextAttributes = [.foregroundColor: UIColor(DesignSystem.brandColors.persianPurple)]
    }
    
    var body: some View {
        MainTabView()
    }
}

#Preview {
    ContentView()
}
