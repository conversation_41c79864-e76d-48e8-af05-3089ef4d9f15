//
//  SikTingApp.swift
//  SikTing
//
//  Created by <PERSON><PERSON> on 2025/6/28.
//

import SwiftUI
import CoreData

@main
struct SikTingApp: App {
    // Initialize Core Data stack on app launch
    let coreDataStack = CoreDataStack.shared

    // Initialize authentication service
    @StateObject private var authService = SecureAuthenticationService()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, coreDataStack.mainContext)
                .environmentObject(authService)
                .onAppear {
                    // Check existing authentication on app launch
                    authService.checkExistingAuthentication()
                }
        }
    }
}

extension Bundle {
    var displayName: String {
        return object(forInfoDictionaryKey: "CFBundleDisplayName") as? String ?? "SikTing"
    }
}
