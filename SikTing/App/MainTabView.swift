//
//  MainTabView.swift
//  SikTing
//
//  Created by Augment Agent on 2025-07-21.
//

import SwiftUI

/// Main tab view with enhanced navigation integration
struct MainTabView: View {
    
    // MARK: - Properties

    @StateObject private var navigationCoordinator = NavigationCoordinator()
    @StateObject private var speechRecognitionViewModel = SpeechRecognitionViewModel()
    @StateObject private var historyStorageService = HistoryStorageService()
    @EnvironmentObject var authService: SecureAuthenticationService
    
    // MARK: - Body
    
    var body: some View {
        ZStack {
            VStack(spacing: 0) {
                // Main content area with persistent tab views
                ZStack {
                    RecordTabView()
                        .opacity(navigationCoordinator.selectedTab == .record ? 1 : 0)
                        .disabled(navigationCoordinator.selectedTab != .record)

                    HistoryTabView()
                        .opacity(navigationCoordinator.selectedTab == .history ? 1 : 0)
                        .disabled(navigationCoordinator.selectedTab != .history)

                    SettingsTabView()
                        .opacity(navigationCoordinator.selectedTab == .settings ? 1 : 0)
                        .disabled(navigationCoordinator.selectedTab != .settings)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .animation(DesignSystem.animations.standard, value: navigationCoordinator.selectedTab)


            }

            // Dropdown menu overlay
            DropdownMenuOverlay()
        }
        .environmentObject(navigationCoordinator)
        .environmentObject(speechRecognitionViewModel)
        .environmentObject(historyStorageService)
        .environmentObject(authService)
        .onOpenURL { url in
            navigationCoordinator.handleDeepLink(url)
        }
        .onReceive(NotificationCenter.default.publisher(for: .navigateToSession)) { notification in
            if let session = notification.object as? HistorySession {
                navigationCoordinator.navigateToSession(session)
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .navigateToHistorySearch)) { _ in
            navigationCoordinator.navigateToHistorySearch()
        }
        .onReceive(NotificationCenter.default.publisher(for: .navigateToSettings)) { notification in
            if let section = notification.object as? SettingsSection {
                navigationCoordinator.navigateToSettings(section: section)
            } else {
                navigationCoordinator.navigateToSettings()
            }
        }
        .onAppear {
            updateHistoryBadgeCount()
            // Inject authentication service into speech recognition view model
            speechRecognitionViewModel.setAuthenticationService(authService)
        }
    }
    
    // MARK: - Private Methods
    
    private func updateHistoryBadgeCount() {
        // Update badge count based on new sessions
        let newSessionsCount = historyStorageService.getNewSessionsCount()
        navigationCoordinator.updateHistoryBadgeCount(newSessionsCount)
    }
}

// MARK: - Record Tab View

private struct RecordTabView: View {
    @EnvironmentObject private var speechRecognitionViewModel: SpeechRecognitionViewModel
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator
    
    var body: some View {
        NavigationView {
            TranscriptionView()
                .navigationTitle("")
                .navigationBarTitleDisplayMode(.inline)
//                .toolbarBackground(.clear, for: .navigationBar)
//                .toolbarBackground(.hidden, for: .navigationBar)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        ToolbarMenuButton()
                    }
                }
        }
        .environmentObject(speechRecognitionViewModel)
//        .statusBarHidden(true) // Hide the status bar to prevent overlap
    }
}

// MARK: - History Tab View

private struct HistoryTabView: View {
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator

    var body: some View {
        NavigationStack(path: $navigationCoordinator.historyNavigationPath) {
            FloatingHistoryView()
                .navigationDestination(for: NavigationDestination.self) { destination in
                    destinationView(for: destination)
                }
        }
        .onAppear {
            // Clear badge when viewing history
            navigationCoordinator.updateHistoryBadgeCount(0)
        }
    }
    
    @ViewBuilder
    private func destinationView(for destination: NavigationDestination) -> some View {
        switch destination {
        case .sessionDetail(let session):
            HistoryDetailView(session: session)
        case .sessionById(let sessionId):
            SessionByIdView(sessionId: sessionId)
        case .historySearch:
            HistorySearchView()
        case .exportOptions:
            ExportOptionsView(sessions: []) // This would be populated with selected sessions
        default:
            EmptyView()
        }
    }
}

// MARK: - Settings Tab View

private struct SettingsTabView: View {
    @EnvironmentObject private var speechRecognitionViewModel: SpeechRecognitionViewModel
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator
    
    var body: some View {
        NavigationStack(path: $navigationCoordinator.settingsNavigationPath) {
            SettingsView(viewModel: speechRecognitionViewModel)
                .navigationDestination(for: NavigationDestination.self) { destination in
                    destinationView(for: destination)
                }
        }
    }
    
    @ViewBuilder
    private func destinationView(for destination: NavigationDestination) -> some View {
        switch destination {
        case .settingsSection(let section):
            settingsSectionView(for: section)
        default:
            EmptyView()
        }
    }
    
    @ViewBuilder
    private func settingsSectionView(for section: SettingsSection) -> some View {
        switch section {
        case .language:
            LanguageSelectionView(translationService: speechRecognitionViewModel.translationService)
        case .smartMerging:
            SmartWordMergingSettingsView()
        case .developer:
            Text("Developer tools have been removed")
                .foregroundColor(.secondary)
        case .about:
            AboutView()
        }
    }
}



// MARK: - Session By ID View

private struct SessionByIdView: View {
    let sessionId: UUID
    @StateObject private var historyStorageService = HistoryStorageService()
    @State private var session: HistorySession?
    @State private var isLoading = true
    
    var body: some View {
        Group {
            if isLoading {
                HistoryLoadingStateView()
            } else if let session = session {
                HistoryDetailView(session: session)
            } else {
                HistoryEmptyStateView(type: .noHistory) {
                    // Navigate back
                }
            }
        }
        .onAppear {
            loadSession()
        }
    }
    
    private func loadSession() {
        // Find session by ID
        if let foundSession = historyStorageService.sessions.first(where: { $0.id == sessionId }) {
            session = foundSession
        }
        isLoading = false
    }
}

// MARK: - About View

private struct AboutView: View {
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 24) {
                // App Info
                VStack(spacing: 16) {
                    Image(systemName: "mic.circle.fill")
                        .font(.system(size: 80))
                        .foregroundColor(.blue)
                    
                    Text("SikTing")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("Version 1.0.0")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 32)
                
                // Features
                VStack(alignment: .leading, spacing: 16) {
                    Text("Features")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    FeatureRow(icon: "waveform", title: "Real-time Transcription", description: "Convert speech to text in real-time")
                    FeatureRow(icon: "globe", title: "Multi-language Support", description: "Support for multiple languages")
                    FeatureRow(icon: "wand.and.rays", title: "Smart Word Merging", description: "Intelligent word combination")
                    FeatureRow(icon: "tag", title: "Tagging System", description: "Organize with tags and categories")
                    FeatureRow(icon: "square.and.arrow.up", title: "Export Options", description: "Export in multiple formats")
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
        }
        .navigationTitle("About")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - Feature Row

private struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.brandPersianPurple)
                .frame(width: 32)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.body)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

// MARK: - Settings Button with Debug Access

struct SettingsButtonWithDebugAccess: View {
    @Binding var showingSettings: Bool
    @Binding var showingDeveloperDebug: Bool
    
    @State private var tapCount = 0
    @State private var tapTimer: Timer?
    
    var body: some View {
        Button("Settings") {
            handleTap()
        }
        .foregroundColor(DesignSystem.brandColors.persianPurple)
    }
    
    private func handleTap() {
        tapCount += 1
        
        // Cancel previous timer
        tapTimer?.invalidate()
        
        if tapCount == 3 {
            // Triple tap detected - show developer debug
            HapticPattern.success.trigger()
            showingDeveloperDebug = true
            tapCount = 0
        } else {
            // Set timer to reset tap count or show settings
            tapTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { _ in
                if tapCount == 1 {
                    // Single tap - show settings
                    showingSettings = true
                } else if tapCount == 2 {
                    // Double tap - just show settings (could be extended for other features)
                    showingSettings = true
                }
                tapCount = 0
            }
        }
    }
}

// MARK: - Preview

#Preview {
    MainTabView()
}
