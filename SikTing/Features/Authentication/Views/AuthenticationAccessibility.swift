//
//  AuthenticationAccessibility.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import SwiftUI

// MARK: - Accessibility Identifiers

/// Accessibility identifiers for authentication components
struct AuthenticationAccessibilityIdentifiers {
    
    // MARK: - Onboarding
    static let onboardingView = "onboarding_view"
    static let onboardingPageIndicator = "onboarding_page_indicator"
    static let onboardingNextButton = "onboarding_next_button"
    static let onboardingPreviousButton = "onboarding_previous_button"
    static let onboardingSkipButton = "onboarding_skip_button"
    static let onboardingPageTitle = "onboarding_page_title"
    static let onboardingPageDescription = "onboarding_page_description"
    
    // MARK: - Apple Sign In
    static let appleSignInButton = "apple_sign_in_button"
    static let appleSignInLoadingIndicator = "apple_sign_in_loading"
    static let appleSignInErrorMessage = "apple_sign_in_error"
    
    // MARK: - User Profile
    static let userProfileSection = "user_profile_section"
    static let userAvatar = "user_avatar"
    static let userDisplayName = "user_display_name"
    static let userEmail = "user_email"
    static let signOutButton = "sign_out_button"
    static let signInPrompt = "sign_in_prompt"
    
    // MARK: - Error Handling
    static let errorFeedbackView = "error_feedback_view"
    static let errorTitle = "error_title"
    static let errorMessage = "error_message"
    static let errorRetryButton = "error_retry_button"
    static let errorDismissButton = "error_dismiss_button"
    
    // MARK: - Loading States
    static let loadingStateView = "loading_state_view"
    static let loadingProgressIndicator = "loading_progress_indicator"
    static let loadingMessage = "loading_message"
}

// MARK: - Accessibility Labels

/// Accessibility labels for authentication components
struct AuthenticationAccessibilityLabels {
    
    // MARK: - Onboarding
    static func onboardingPageTitle(_ title: String) -> String {
        return "Onboarding page: \(title)"
    }
    
    static func onboardingPageDescription(_ description: String) -> String {
        return "Page description: \(description)"
    }
    
    static func onboardingPageIndicator(current: Int, total: Int) -> String {
        return "Page \(current + 1) of \(total)"
    }
    
    static let onboardingNextButton = "Next page"
    static let onboardingPreviousButton = "Previous page"
    static let onboardingSkipButton = "Skip onboarding"
    static let onboardingGetStartedButton = "Get started with authentication"
    
    // MARK: - Apple Sign In
    static let appleSignInButton = "Sign in with Apple"
    static let appleSignInLoading = "Signing in with Apple, please wait"
    static let appleSignInSuccess = "Successfully signed in with Apple"
    static let appleSignInFailed = "Apple Sign In failed"
    
    // MARK: - User Profile
    static let userProfileSection = "User profile section"
    static func userAvatar(_ name: String) -> String {
        return "\(name)'s profile picture"
    }
    
    static func userDisplayName(_ name: String) -> String {
        return "Display name: \(name)"
    }
    
    static func userEmail(_ email: String) -> String {
        return "Email address: \(email)"
    }
    
    static let signOutButton = "Sign out of your account"
    static let signInPrompt = "Sign in required to access features"
    static let authenticationStatus = "Signed in with Apple"
    
    // MARK: - Error Handling
    static func errorTitle(_ title: String) -> String {
        return "Error: \(title)"
    }
    
    static func errorMessage(_ message: String) -> String {
        return "Error details: \(message)"
    }
    
    static let errorRetryButton = "Retry the failed action"
    static let errorDismissButton = "Dismiss error message"
    
    // MARK: - Loading States
    static func loadingMessage(_ message: String) -> String {
        return "Loading: \(message)"
    }
    
    static let loadingProgress = "Loading in progress"
    
    // MARK: - Connection States
    static let connectionEstablished = "Connection established successfully"
    static let connectionLost = "Connection lost"
    static let connectionRetrying = "Retrying connection"
    
    // MARK: - Session States
    static let sessionRestored = "Session restored from previous login"
    static let sessionExpired = "Session expired, please sign in again"
    static let sessionCleared = "Session cleared, signed out successfully"
}

// MARK: - Accessibility Hints

/// Accessibility hints for authentication components
struct AuthenticationAccessibilityHints {
    
    // MARK: - Onboarding
    static let onboardingNextButton = "Double tap to proceed to the next onboarding page"
    static let onboardingPreviousButton = "Double tap to go back to the previous page"
    static let onboardingSkipButton = "Double tap to skip the onboarding process"
    static let onboardingPageSwipe = "Swipe left or right to navigate between pages"
    
    // MARK: - Apple Sign In
    static let appleSignInButton = "Double tap to sign in with your Apple ID"
    static let appleSignInRetry = "Double tap to retry Apple Sign In"
    
    // MARK: - User Profile
    static let signOutButton = "Double tap to sign out of your account"
    static let signInPromptButton = "Double tap to start the sign in process"
    
    // MARK: - Error Handling
    static let errorRetryButton = "Double tap to retry the failed operation"
    static let errorDismissButton = "Double tap to close this error message"
    
    // MARK: - Navigation
    static let backButton = "Double tap to go back"
    static let closeButton = "Double tap to close"
}

// MARK: - Accessibility Traits

/// Accessibility traits for authentication components
struct AuthenticationAccessibilityTraits {
    static let primaryButton: AccessibilityTraits = [.isButton]
    static let secondaryButton: AccessibilityTraits = [.isButton]
    static let navigationButton: AccessibilityTraits = [.isButton]
    static let loadingIndicator: AccessibilityTraits = [.updatesFrequently]
    static let errorMessage: AccessibilityTraits = [.isStaticText]
    static let userInfo: AccessibilityTraits = [.isStaticText]
    static let pageIndicator: AccessibilityTraits = [.isStaticText, .updatesFrequently]
}

// MARK: - Accessibility Announcements

/// Helper for making accessibility announcements
class AuthenticationAccessibilityAnnouncer {
    
    /// Announce authentication state changes
    static func announceAuthenticationStateChange(_ state: AuthenticationState) {
        let announcement: String
        
        switch state {
        case .notAuthenticated:
            announcement = "Not signed in"
        case .authenticating:
            announcement = "Signing in, please wait"
        case .authenticated:
            announcement = "Successfully signed in"
        case .failed(let error):
            announcement = "Sign in failed: \(error.localizedDescription)"
        }
        
        UIAccessibility.post(notification: .announcement, argument: announcement)
    }
    
    /// Announce onboarding page changes
    static func announceOnboardingPageChange(current: Int, total: Int, title: String) {
        let announcement = "Page \(current + 1) of \(total): \(title)"
        UIAccessibility.post(notification: .announcement, argument: announcement)
    }
    
    /// Announce error states
    static func announceError(_ error: SecureAuthenticationError) {
        let announcement = "Error: \(error.userFriendlyMessage)"
        UIAccessibility.post(notification: .announcement, argument: announcement)
    }
    
    /// Announce success states
    static func announceSuccess(_ message: String) {
        UIAccessibility.post(notification: .announcement, argument: message)
    }
    
    /// Announce loading states
    static func announceLoadingState(_ isLoading: Bool, message: String = "") {
        let announcement = isLoading ? "Loading \(message)" : "Loading complete"
        UIAccessibility.post(notification: .announcement, argument: announcement)
    }
    
    /// Announce connection state changes
    static func announceConnectionStateChange(_ state: WebSocketConnectionState) {
        let announcement: String
        
        switch state {
        case .disconnected:
            announcement = "Connection lost"
        case .connecting:
            announcement = "Connecting"
        case .connected:
            announcement = "Connected successfully"
        }
        
        UIAccessibility.post(notification: .announcement, argument: announcement)
    }
}

// MARK: - View Extensions for Accessibility

extension View {
    
    /// Apply authentication accessibility modifiers
    func authenticationAccessibility(
        identifier: String,
        label: String,
        hint: String? = nil,
        traits: AccessibilityTraits = []
    ) -> some View {
        self
            .accessibilityIdentifier(identifier)
            .accessibilityLabel(label)
            .accessibilityHint(hint ?? "")
            .accessibilityAddTraits(traits)
    }
    
    /// Apply onboarding page accessibility
    func onboardingPageAccessibility(
        pageIndex: Int,
        totalPages: Int,
        title: String,
        description: String
    ) -> some View {
        self
            .accessibilityElement(children: .combine)
            .accessibilityLabel(AuthenticationAccessibilityLabels.onboardingPageTitle(title))
            .accessibilityValue(AuthenticationAccessibilityLabels.onboardingPageDescription(description))
            .accessibilityHint(AuthenticationAccessibilityHints.onboardingPageSwipe)
            .accessibilityAddTraits(.isStaticText)
    }
    
    /// Apply Apple Sign In button accessibility
    func appleSignInButtonAccessibility(isLoading: Bool) -> some View {
        self
            .accessibilityIdentifier(AuthenticationAccessibilityIdentifiers.appleSignInButton)
            .accessibilityLabel(AuthenticationAccessibilityLabels.appleSignInButton)
            .accessibilityHint(AuthenticationAccessibilityHints.appleSignInButton)
            .accessibilityAddTraits(.isButton)
            .accessibilityValue(isLoading ? AuthenticationAccessibilityLabels.appleSignInLoading : "")
    }
    
    /// Apply error feedback accessibility
    func errorFeedbackAccessibility(error: AuthenticationErrorHandler.ErrorPresentation) -> some View {
        self
            .accessibilityElement(children: .combine)
            .accessibilityIdentifier(AuthenticationAccessibilityIdentifiers.errorFeedbackView)
            .accessibilityLabel(AuthenticationAccessibilityLabels.errorTitle(error.title))
            .accessibilityValue(AuthenticationAccessibilityLabels.errorMessage(error.message))
            .accessibilityAddTraits(.isStaticText)
    }
    
    /// Apply user profile accessibility
    func userProfileAccessibility(userInfo: UserInfo?) -> some View {
        self
            .accessibilityElement(children: .combine)
            .accessibilityIdentifier(AuthenticationAccessibilityIdentifiers.userProfileSection)
            .accessibilityLabel(AuthenticationAccessibilityLabels.userProfileSection)
            .accessibilityValue(userInfo != nil ? 
                AuthenticationAccessibilityLabels.userDisplayName(userInfo!.displayName) : 
                AuthenticationAccessibilityLabels.signInPrompt)
    }
}
