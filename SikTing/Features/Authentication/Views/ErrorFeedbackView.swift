//
//  ErrorFeedbackView.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import SwiftUI

/// Reusable error feedback view with loading states and retry options
struct ErrorFeedbackView: View {
    
    // MARK: - Properties
    
    @ObservedObject var errorHandler: AuthenticationErrorHandler
    
    // MARK: - Body
    
    var body: some View {
        Group {
            if let error = errorHandler.currentError {
                errorCard(for: error)
                    .transition(.asymmetric(
                        insertion: .move(edge: .top).combined(with: .opacity),
                        removal: .move(edge: .top).combined(with: .opacity)
                    ))
            }
        }
        .animation(.easeInOut(duration: 0.3), value: errorHandler.currentError?.id)
    }
    
    // MARK: - Error Card
    
    private func errorCard(for error: AuthenticationErrorHandler.ErrorPresentation) -> some View {
        VStack(spacing: DesignSystem.spacing.medium) {
            // Header with icon and title
            HStack(spacing: DesignSystem.spacing.small) {
                Image(systemName: error.severity.icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(error.severity.color)
                
                Text(error.title)
                    .font(DesignSystem.typography.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(DesignSystem.brandColors.persianPurple)
                
                Spacer()
                
                // Retry indicator
                if errorHandler.isRetrying {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: error.severity.color))
                }
            }
            
            // Error message
            Text(error.message)
                .font(DesignSystem.typography.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
                .lineLimit(nil)
            
            // Action buttons
            if error.primaryAction != nil || error.secondaryAction != nil {
                HStack(spacing: DesignSystem.spacing.small) {
                    // Secondary action (usually dismiss)
                    if let secondaryAction = error.secondaryAction {
                        Button(action: {
                            Task {
                                await secondaryAction.action()
                            }
                        }) {
                            Text(secondaryAction.title)
                                .font(DesignSystem.typography.body)
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: DesignSystem.cornerRadius.small)
                                        .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                                )
                        }
                        .disabled(errorHandler.isRetrying)
                    }
                    
                    Spacer()
                    
                    // Primary action (usually retry)
                    if let primaryAction = error.primaryAction {
                        Button(action: {
                            Task {
                                await primaryAction.action()
                            }
                        }) {
                            HStack(spacing: 6) {
                                if errorHandler.isRetrying {
                                    ProgressView()
                                        .scaleEffect(0.7)
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                } else {
                                    Image(systemName: "arrow.clockwise")
                                        .font(.system(size: 14, weight: .medium))
                                }
                                
                                Text(errorHandler.isRetrying ? "Retrying..." : primaryAction.title)
                                    .font(DesignSystem.typography.body)
                                    .fontWeight(.medium)
                            }
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: DesignSystem.cornerRadius.small)
                                    .fill(primaryActionColor(for: primaryAction.style))
                            )
                        }
                        .disabled(errorHandler.isRetrying)
                    }
                }
            }
        }
        .padding(DesignSystem.spacing.medium)
        .background(
            RoundedRectangle(cornerRadius: DesignSystem.cornerRadius.medium)
                .fill(DesignSystem.brandColors.alabaster)
                .shadow(
                    color: error.severity.color.opacity(0.2),
                    radius: 4,
                    x: 0,
                    y: 2
                )
        )
        .overlay(
            RoundedRectangle(cornerRadius: DesignSystem.cornerRadius.medium)
                .stroke(error.severity.color.opacity(0.3), lineWidth: 1)
        )
        .padding(.horizontal, DesignSystem.spacing.medium)
    }
    
    // MARK: - Helper Methods
    
    private func primaryActionColor(for style: AuthenticationErrorHandler.ErrorAction.ActionStyle) -> Color {
        switch style {
        case .primary:
            return DesignSystem.brandColors.persianPurple
        case .secondary:
            return DesignSystem.brandColors.orchid
        case .destructive:
            return .orange
        }
    }
}

// MARK: - Loading State View

/// Reusable loading state view with progress indicators
struct LoadingStateView: View {
    
    // MARK: - Properties
    
    let message: String
    let showProgress: Bool
    let progress: Double?
    
    init(message: String = "Loading...", showProgress: Bool = true, progress: Double? = nil) {
        self.message = message
        self.showProgress = showProgress
        self.progress = progress
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: DesignSystem.spacing.medium) {
            // Progress indicator
            if showProgress {
                if let progress = progress {
                    // Determinate progress
                    ProgressView(value: progress)
                        .progressViewStyle(LinearProgressViewStyle(tint: DesignSystem.brandColors.persianPurple))
                        .scaleEffect(1.2)
                } else {
                    // Indeterminate progress
                    ProgressView()
                        .scaleEffect(1.2)
                        .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.brandColors.persianPurple))
                }
            }
            
            // Loading message
            Text(message)
                .font(DesignSystem.typography.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(DesignSystem.spacing.large)
        .background(
            RoundedRectangle(cornerRadius: DesignSystem.cornerRadius.medium)
                .fill(DesignSystem.brandColors.alabaster)
                .shadow(color: DesignSystem.brandColors.frenchLilac.opacity(0.1), radius: 2, x: 0, y: 1)
        )
        .padding(.horizontal, DesignSystem.spacing.medium)
    }
}

// MARK: - Success Feedback View

/// Reusable success feedback view
struct SuccessFeedbackView: View {
    
    // MARK: - Properties
    
    let title: String
    let message: String
    let onDismiss: (() -> Void)?
    
    init(title: String, message: String, onDismiss: (() -> Void)? = nil) {
        self.title = title
        self.message = message
        self.onDismiss = onDismiss
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: DesignSystem.spacing.medium) {
            // Success icon and title
            HStack(spacing: DesignSystem.spacing.small) {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(.green)
                
                Text(title)
                    .font(DesignSystem.typography.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(DesignSystem.brandColors.persianPurple)
                
                Spacer()
                
                if let onDismiss = onDismiss {
                    Button(action: onDismiss) {
                        Image(systemName: "xmark")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            // Success message
            Text(message)
                .font(DesignSystem.typography.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
                .lineLimit(nil)
        }
        .padding(DesignSystem.spacing.medium)
        .background(
            RoundedRectangle(cornerRadius: DesignSystem.cornerRadius.medium)
                .fill(DesignSystem.brandColors.alabaster)
                .shadow(
                    color: Color.green.opacity(0.2),
                    radius: 4,
                    x: 0,
                    y: 2
                )
        )
        .overlay(
            RoundedRectangle(cornerRadius: DesignSystem.cornerRadius.medium)
                .stroke(Color.green.opacity(0.3), lineWidth: 1)
        )
        .padding(.horizontal, DesignSystem.spacing.medium)
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        ErrorFeedbackView(errorHandler: AuthenticationErrorHandler())
        LoadingStateView(message: "Signing in...")
        SuccessFeedbackView(title: "Success", message: "You have been signed in successfully.")
    }
    .padding()
    .background(DesignSystem.brandColors.adaptiveBackgroundGradient)
}
