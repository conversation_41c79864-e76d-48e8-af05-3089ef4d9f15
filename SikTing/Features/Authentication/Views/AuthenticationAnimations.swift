//
//  AuthenticationAnimations.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import SwiftUI

// MARK: - Authentication State Transitions

/// Custom view modifier for authentication state transitions
struct AuthenticationStateTransition: ViewModifier {
    let isAuthenticated: Bool
    let animationDuration: Double
    
    init(isAuthenticated: Bool, animationDuration: Double = 0.6) {
        self.isAuthenticated = isAuthenticated
        self.animationDuration = animationDuration
    }
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isAuthenticated ? 1.0 : 0.95)
            .opacity(isAuthenticated ? 1.0 : 0.8)
            .animation(
                .easeInOut(duration: animationDuration)
                .delay(isAuthenticated ? 0.1 : 0),
                value: isAuthenticated
            )
    }
}

/// Custom view modifier for loading state animations
struct LoadingStateAnimation: ViewModifier {
    let isLoading: Bool
    @State private var rotationAngle: Double = 0
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isLoading ? 0.9 : 1.0)
            .opacity(isLoading ? 0.7 : 1.0)
            .rotationEffect(.degrees(isLoading ? rotationAngle : 0))
            .animation(
                .easeInOut(duration: 0.3),
                value: isLoading
            )
            .onAppear {
                if isLoading {
                    withAnimation(.linear(duration: 2.0).repeatForever(autoreverses: false)) {
                        rotationAngle = 360
                    }
                }
            }
            .onChange(of: isLoading) { newValue in
                if newValue {
                    withAnimation(.linear(duration: 2.0).repeatForever(autoreverses: false)) {
                        rotationAngle = 360
                    }
                } else {
                    withAnimation(.easeOut(duration: 0.3)) {
                        rotationAngle = 0
                    }
                }
            }
    }
}

/// Custom view modifier for success state animations
struct SuccessStateAnimation: ViewModifier {
    let showSuccess: Bool
    @State private var scale: CGFloat = 0.8
    @State private var opacity: Double = 0
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(scale)
            .opacity(opacity)
            .onChange(of: showSuccess) { newValue in
                if newValue {
                    withAnimation(.spring(response: 0.6, dampingFraction: 0.7, blendDuration: 0)) {
                        scale = 1.0
                        opacity = 1.0
                    }
                } else {
                    withAnimation(.easeOut(duration: 0.3)) {
                        scale = 0.8
                        opacity = 0
                    }
                }
            }
    }
}

/// Custom view modifier for error state animations
struct ErrorStateAnimation: ViewModifier {
    let showError: Bool
    @State private var offset: CGFloat = 0
    
    func body(content: Content) -> some View {
        content
            .offset(x: offset)
            .onChange(of: showError) { newValue in
                if newValue {
                    // Shake animation for errors
                    withAnimation(.easeInOut(duration: 0.1).repeatCount(3, autoreverses: true)) {
                        offset = 5
                    }
                    
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        withAnimation(.easeOut(duration: 0.2)) {
                            offset = 0
                        }
                    }
                }
            }
    }
}

// MARK: - Onboarding Animations

/// Custom view modifier for onboarding page transitions
struct OnboardingPageTransition: ViewModifier {
    let currentPage: Int
    let pageIndex: Int
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(currentPage == pageIndex ? 1.0 : 0.9)
            .opacity(currentPage == pageIndex ? 1.0 : 0.6)
            .offset(x: CGFloat(pageIndex - currentPage) * 20)
            .animation(
                .spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0),
                value: currentPage
            )
    }
}

/// Custom view modifier for onboarding content reveal
struct OnboardingContentReveal: ViewModifier {
    let isVisible: Bool
    let delay: Double
    
    func body(content: Content) -> some View {
        content
            .opacity(isVisible ? 1.0 : 0)
            .offset(y: isVisible ? 0 : 30)
            .animation(
                .easeOut(duration: 0.8).delay(delay),
                value: isVisible
            )
    }
}

// MARK: - Button Animations

/// Custom view modifier for interactive button animations
struct InteractiveButtonAnimation: ViewModifier {
    @State private var isPressed = false
    let isEnabled: Bool
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .opacity(isEnabled ? 1.0 : 0.6)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
            .animation(.easeInOut(duration: 0.3), value: isEnabled)
            .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                isPressed = pressing && isEnabled
            }, perform: {})
    }
}

/// Custom view modifier for Apple Sign In button animation
struct AppleSignInButtonAnimation: ViewModifier {
    let isLoading: Bool
    @State private var pulseScale: CGFloat = 1.0
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(pulseScale)
            .animation(
                isLoading ? 
                .easeInOut(duration: 1.0).repeatForever(autoreverses: true) :
                .easeOut(duration: 0.3),
                value: pulseScale
            )
            .onAppear {
                if isLoading {
                    pulseScale = 1.05
                }
            }
            .onChange(of: isLoading) { newValue in
                if newValue {
                    pulseScale = 1.05
                } else {
                    pulseScale = 1.0
                }
            }
    }
}

// MARK: - Profile Animations

/// Custom view modifier for profile avatar animations
struct ProfileAvatarAnimation: ViewModifier {
    let isAuthenticated: Bool
    @State private var rotationAngle: Double = 0
    
    func body(content: Content) -> some View {
        content
            .rotationEffect(.degrees(rotationAngle))
            .scaleEffect(isAuthenticated ? 1.0 : 0.8)
            .animation(
                .spring(response: 0.6, dampingFraction: 0.7, blendDuration: 0),
                value: isAuthenticated
            )
            .onChange(of: isAuthenticated) { newValue in
                if newValue {
                    withAnimation(.easeInOut(duration: 0.6)) {
                        rotationAngle += 360
                    }
                }
            }
    }
}

/// Custom view modifier for profile section reveal
struct ProfileSectionReveal: ViewModifier {
    let isVisible: Bool
    
    func body(content: Content) -> some View {
        content
            .opacity(isVisible ? 1.0 : 0)
            .offset(y: isVisible ? 0 : -20)
            .animation(
                .spring(response: 0.8, dampingFraction: 0.8, blendDuration: 0),
                value: isVisible
            )
    }
}

// MARK: - View Extensions

extension View {
    /// Apply authentication state transition animation
    func authenticationStateTransition(isAuthenticated: Bool, duration: Double = 0.6) -> some View {
        modifier(AuthenticationStateTransition(isAuthenticated: isAuthenticated, animationDuration: duration))
    }
    
    /// Apply loading state animation
    func loadingStateAnimation(isLoading: Bool) -> some View {
        modifier(LoadingStateAnimation(isLoading: isLoading))
    }
    
    /// Apply success state animation
    func successStateAnimation(showSuccess: Bool) -> some View {
        modifier(SuccessStateAnimation(showSuccess: showSuccess))
    }
    
    /// Apply error state animation
    func errorStateAnimation(showError: Bool) -> some View {
        modifier(ErrorStateAnimation(showError: showError))
    }
    
    /// Apply onboarding page transition
    func onboardingPageTransition(currentPage: Int, pageIndex: Int) -> some View {
        modifier(OnboardingPageTransition(currentPage: currentPage, pageIndex: pageIndex))
    }
    
    /// Apply onboarding content reveal
    func onboardingContentReveal(isVisible: Bool, delay: Double = 0) -> some View {
        modifier(OnboardingContentReveal(isVisible: isVisible, delay: delay))
    }
    
    /// Apply interactive button animation
    func interactiveButtonAnimation(isEnabled: Bool = true) -> some View {
        modifier(InteractiveButtonAnimation(isEnabled: isEnabled))
    }
    
    /// Apply Apple Sign In button animation
    func appleSignInButtonAnimation(isLoading: Bool) -> some View {
        modifier(AppleSignInButtonAnimation(isLoading: isLoading))
    }
    
    /// Apply profile avatar animation
    func profileAvatarAnimation(isAuthenticated: Bool) -> some View {
        modifier(ProfileAvatarAnimation(isAuthenticated: isAuthenticated))
    }
    
    /// Apply profile section reveal
    func profileSectionReveal(isVisible: Bool) -> some View {
        modifier(ProfileSectionReveal(isVisible: isVisible))
    }
}
