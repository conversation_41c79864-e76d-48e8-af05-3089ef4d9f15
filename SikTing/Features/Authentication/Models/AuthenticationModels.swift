//
//  AuthenticationModels.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import Foundation

// MARK: - Authentication State

/// Represents the current authentication state of the user
enum AuthenticationState: Equatable {
    case notAuthenticated
    case authenticating
    case authenticated(accessToken: String, expiresAt: Date)
    case failed(Error)
    
    /// Whether the user is currently authenticated
    var isAuthenticated: Bool {
        switch self {
        case .authenticated:
            return true
        default:
            return false
        }
    }
    
    /// Get the access token if authenticated
    var accessToken: String? {
        switch self {
        case .authenticated(let token, _):
            return token
        default:
            return nil
        }
    }
    
    /// Get the expiration date if authenticated
    var expiresAt: Date? {
        switch self {
        case .authenticated(_, let expiresAt):
            return expiresAt
        default:
            return nil
        }
    }
    
    static func == (lhs: AuthenticationState, rhs: AuthenticationState) -> Bool {
        switch (lhs, rhs) {
        case (.notAuthenticated, .notAuthenticated),
             (.authenticating, .authenticating):
            return true
        case (.authenticated(let lToken, let lDate), .authenticated(let rToken, let rDate)):
            return lToken == rToken && lDate == rDate
        case (.failed(let lError), .failed(let rError)):
            return lError.localizedDescription == rError.localizedDescription
        default:
            return false
        }
    }
}

// MARK: - User Information

/// User information with profile data
struct UserInfo: Codable, Equatable {
    let userID: String
    let email: String?
    let accessToken: String
    let expiresAt: Date
    let displayName: String
    let avatarSeed: String // For generating consistent avatar
    
    /// Whether the token is still valid
    var isValid: Bool {
        return Date() < expiresAt.addingTimeInterval(-SecureBackendConfig.tokenRefreshBuffer)
    }
    
    /// Whether the token needs refresh
    var needsRefresh: Bool {
        return Date() > expiresAt.addingTimeInterval(-SecureBackendConfig.tokenRefreshBuffer)
    }
    
    /// Time remaining until token expires
    var timeUntilExpiration: TimeInterval {
        return expiresAt.timeIntervalSinceNow
    }
    
    /// Initialize with Apple Sign In data
    init(userID: String, email: String?, accessToken: String, expiresAt: Date) {
        self.userID = userID
        self.email = email
        self.accessToken = accessToken
        self.expiresAt = expiresAt
        
        // Generate display name from email or use default
        if let email = email {
            let components = email.components(separatedBy: "@")
            self.displayName = components.first?.capitalized ?? "User"
        } else {
            self.displayName = "User"
        }
        
        // Generate consistent avatar seed from userID
        self.avatarSeed = String(userID.hashValue)
    }
}

// MARK: - API Models

/// Request model for token exchange with backend
struct TokenRequest: Codable {
    let identityToken: String
    
    enum CodingKeys: String, CodingKey {
        case identityToken = "identity_token"
    }
}

/// Response model for successful token exchange
struct TokenResponse: Codable {
    let accessToken: String
    let expiresIn: Int
    let tokenType: String?
    
    enum CodingKeys: String, CodingKey {
        case accessToken = "access_token"
        case expiresIn = "expires_in"
        case tokenType = "token_type"
    }
}

/// Error response model from API
struct APIError: Codable, Error {
    let error: String
    let message: String
    let code: Int?
    
    var localizedDescription: String {
        return message
    }
}

// MARK: - Authentication Errors

/// Custom error types for authentication failures
enum SecureAuthenticationError: Error, LocalizedError, Equatable {
    case appleSignInFailed(String)
    case appleSignInCancelled
    case tokenExchangeFailed(String)
    case networkError(Error)
    case tokenExpired
    case rateLimitExceeded
    case serverUnavailable
    case invalidResponse
    case invalidToken
    case userNotFound
    
    var errorDescription: String? {
        switch self {
        case .appleSignInFailed(let message):
            return "Apple Sign In failed: \(message)"
        case .appleSignInCancelled:
            return "Apple Sign In was cancelled"
        case .tokenExchangeFailed(let message):
            return "Token exchange failed: \(message)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .tokenExpired:
            return "Access token has expired"
        case .rateLimitExceeded:
            return "Too many requests. Please try again later."
        case .serverUnavailable:
            return "Server is temporarily unavailable"
        case .invalidResponse:
            return "Invalid response from server"
        case .invalidToken:
            return "Invalid access token"
        case .userNotFound:
            return "User not found"
        }
    }
    
    /// User-friendly error message for display
    var userFriendlyMessage: String {
        switch self {
        case .appleSignInFailed:
            return "Sign in with Apple failed. Please try again."
        case .appleSignInCancelled:
            return "Sign in was cancelled."
        case .tokenExchangeFailed:
            return "Authentication failed. Please try signing in again."
        case .networkError:
            return "Please check your internet connection and try again."
        case .tokenExpired:
            return "Your session has expired. Please sign in again."
        case .rateLimitExceeded:
            return "Too many attempts. Please wait a moment and try again."
        case .serverUnavailable:
            return "Service is temporarily unavailable. Please try again later."
        case .invalidResponse, .invalidToken:
            return "Authentication error. Please try signing in again."
        case .userNotFound:
            return "Account not found. Please sign up first."
        }
    }
    
    /// Whether the error should trigger an automatic retry
    var shouldRetry: Bool {
        switch self {
        case .networkError, .serverUnavailable:
            return true
        case .tokenExpired:
            return true
        default:
            return false
        }
    }
    
    /// Whether the error should clear the current session
    var shouldClearSession: Bool {
        switch self {
        case .tokenExpired, .invalidToken, .userNotFound:
            return true
        default:
            return false
        }
    }
    
    static func == (lhs: SecureAuthenticationError, rhs: SecureAuthenticationError) -> Bool {
        switch (lhs, rhs) {
        case (.appleSignInFailed(let lMsg), .appleSignInFailed(let rMsg)):
            return lMsg == rMsg
        case (.appleSignInCancelled, .appleSignInCancelled),
             (.tokenExpired, .tokenExpired),
             (.rateLimitExceeded, .rateLimitExceeded),
             (.serverUnavailable, .serverUnavailable),
             (.invalidResponse, .invalidResponse),
             (.invalidToken, .invalidToken),
             (.userNotFound, .userNotFound):
            return true
        case (.tokenExchangeFailed(let lMsg), .tokenExchangeFailed(let rMsg)):
            return lMsg == rMsg
        case (.networkError(let lError), .networkError(let rError)):
            return lError.localizedDescription == rError.localizedDescription
        default:
            return false
        }
    }
}

// MARK: - WebSocket Authentication Errors

/// Errors specific to WebSocket authentication
enum SecureWebSocketError: Error, LocalizedError {
    case authenticationRequired
    case tokenExpired
    case connectionFailed(String)
    case serverError(Int, String)
    case networkUnavailable
    case invalidURL
    case tokenMissing
    
    var errorDescription: String? {
        switch self {
        case .authenticationRequired:
            return "Authentication required for WebSocket connection"
        case .tokenExpired:
            return "Access token expired during WebSocket connection"
        case .connectionFailed(let message):
            return "WebSocket connection failed: \(message)"
        case .serverError(let code, let message):
            return "Server error (\(code)): \(message)"
        case .networkUnavailable:
            return "Network unavailable for WebSocket connection"
        case .invalidURL:
            return "Invalid WebSocket URL"
        case .tokenMissing:
            return "Access token missing for WebSocket connection"
        }
    }
    
    /// Whether this error should trigger re-authentication
    var shouldReauthenticate: Bool {
        switch self {
        case .authenticationRequired, .tokenExpired, .tokenMissing:
            return true
        case .serverError(let code, _):
            return code == 401 || code == 403
        default:
            return false
        }
    }
}

// MARK: - Configuration

/// Configuration for secure backend integration
struct SecureBackendConfig {
    #if DEBUG
    static let baseURL = "https://dev.your-domain.com"
    static let enableLogging = true
    #else
    static let baseURL = "https://your-production-domain.com"
    static let enableLogging = false
    #endif
    
    static let appleBundleID = "com.superspacex.asr"
    static let tokenEndpoint = "/api/token"
    static let webSocketEndpoint = "/ws/asr"
    static let connectionTimeout: TimeInterval = 30
    static let tokenRefreshBuffer: TimeInterval = 60 // Refresh 1 minute before expiry
    static let maxRetryAttempts = 3
    static let retryDelay: TimeInterval = 2.0
}
