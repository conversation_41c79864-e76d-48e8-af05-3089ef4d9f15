//
//  SecureAuthenticationService.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import Foundation
import AuthenticationServices
import Combine

/// Service for handling Apple Sign In and secure backend authentication
@MainActor
class SecureAuthenticationService: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var authState: AuthenticationState = .notAuthenticated
    @Published var userInfo: UserInfo?
    @Published var isFirstLaunch: Bool = true
    @Published var isLoading: Bool = false
    @Published var lastError: SecureAuthenticationError?

    // Error handling
    @Published var errorHandler = AuthenticationErrorHandler()
    
    // MARK: - Private Properties
    
    private var cancellables = Set<AnyCancellable>()
    private let urlSession = URLSession.shared
    private let userDefaults = UserDefaults.standard
    private let sessionPersistence = SessionPersistenceService.shared
    private let hapticManager = AuthenticationHapticManager.shared
    
    // MARK: - Constants
    
    private enum UserDefaultsKeys {
        static let isFirstLaunch = "secure_auth_first_launch"
        static let lastUserID = "secure_auth_last_user_id"
    }
    
    // MARK: - Initialization
    
    override init() {
        super.init()
        checkFirstLaunch()
        setupNotificationObservers()
    }
    
    deinit {
        cancellables.removeAll()
    }
    
    // MARK: - Public Methods
    
    /// Initiate Apple Sign In flow
    func signInWithApple() async {
        guard !isLoading else { return }
        
        isLoading = true
        authState = .authenticating
        lastError = nil

        hapticManager.authenticationStarted()
        
        do {
            let (identityToken, userID, email) = try await performAppleSignIn()
            await exchangeAppleToken(identityToken, userID: userID, email: email)
        } catch {
            handleAuthenticationError(error)
        }
        
        isLoading = false
    }
    
    /// Sign out user and clear all authentication state
    func signOut() {
        userInfo = nil
        authState = .notAuthenticated
        lastError = nil

        // Clear session from secure storage
        sessionPersistence.clearSession()

        // Clear error state
        errorHandler.clearError()

        print("🔐 SecureAuthenticationService: User signed out")
    }
    
    /// Check for existing authentication state
    func checkExistingAuthentication() {
        // Try to load from persistence if not already loaded
        if userInfo == nil {
            userInfo = sessionPersistence.loadSession()
        }

        guard let userInfo = userInfo else {
            authState = .notAuthenticated
            return
        }

        if userInfo.isValid {
            authState = .authenticated(accessToken: userInfo.accessToken, expiresAt: userInfo.expiresAt)
            print("🔐 SecureAuthenticationService: Existing authentication is valid")
        } else {
            // Token expired, clear session
            self.userInfo = nil
            sessionPersistence.clearSession()
            authState = .notAuthenticated
            print("🔐 SecureAuthenticationService: Existing authentication expired")
        }
    }
    
    /// Refresh access token if needed
    func refreshTokenIfNeeded() async {
        // Check if session needs refresh based on persistence service
        guard sessionPersistence.sessionNeedsRefresh() else { return }

        guard let userInfo = userInfo, userInfo.needsRefresh else { return }

        print("🔐 SecureAuthenticationService: Token needs refresh, re-authenticating...")

        // For Apple Sign In, we need to re-authenticate
        // In a production app, you might implement silent refresh if supported by backend
        authState = .notAuthenticated
        self.userInfo = nil
        sessionPersistence.clearSession()

        // Automatically trigger re-authentication
        await signInWithApple()
    }
    
    /// Complete onboarding flow
    func completeOnboarding() {
        isFirstLaunch = false
        userDefaults.set(false, forKey: UserDefaultsKeys.isFirstLaunch)
        print("🔐 SecureAuthenticationService: Onboarding completed")
    }
    
    // MARK: - Private Methods
    
    /// Check if this is the first app launch and load existing session
    private func checkFirstLaunch() {
        isFirstLaunch = sessionPersistence.isFirstLaunch()

        // Try to load existing session
        if let savedUserInfo = sessionPersistence.loadSession() {
            userInfo = savedUserInfo
            authState = .authenticated(accessToken: savedUserInfo.accessToken, expiresAt: savedUserInfo.expiresAt)
            print("🔐 SecureAuthenticationService: Restored session from storage")
        }
    }
    
    /// Setup notification observers for app lifecycle
    private func setupNotificationObservers() {
        // Handle app entering foreground
        NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.handleAppWillEnterForeground()
                }
            }
            .store(in: &cancellables)

        // Handle app entering background
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { [weak self] _ in
                self?.handleAppDidEnterBackground()
            }
            .store(in: &cancellables)
    }

    /// Handle app entering foreground
    private func handleAppWillEnterForeground() {
        // Check if session is still valid after background time
        let sessionValid = sessionPersistence.handleAppWillEnterForeground()

        if !sessionValid {
            // Session was cleared due to long background time
            userInfo = nil
            authState = .notAuthenticated
            errorHandler.clearError()
            return
        }

        // Validate current session
        if sessionPersistence.validateSessionAfterBackground() {
            // Check if token needs refresh
            Task {
                await refreshTokenIfNeeded()
            }
        } else {
            // Session expired while in background
            userInfo = nil
            authState = .notAuthenticated
            errorHandler.clearError()
        }
    }

    /// Handle app entering background
    private func handleAppDidEnterBackground() {
        sessionPersistence.handleAppDidEnterBackground()
    }
    
    /// Perform Apple Sign In and return credentials
    private func performAppleSignIn() async throws -> (identityToken: String, userID: String, email: String?) {
        return try await withCheckedThrowingContinuation { continuation in
            let request = ASAuthorizationAppleIDProvider().createRequest()
            request.requestedScopes = [.fullName, .email]
            
            let authorizationController = ASAuthorizationController(authorizationRequests: [request])
            authorizationController.delegate = AppleSignInDelegate(continuation: continuation)
            authorizationController.presentationContextProvider = self
            authorizationController.performRequests()
        }
    }
    
    /// Exchange Apple identity token for backend access token
    private func exchangeAppleToken(_ identityToken: String, userID: String, email: String?) async {
        do {
            let tokenRequest = TokenRequest(identityToken: identityToken)
            let tokenResponse = try await performTokenExchange(tokenRequest)
            
            let expiresAt = Date().addingTimeInterval(TimeInterval(tokenResponse.expiresIn))
            let userInfo = UserInfo(
                userID: userID,
                email: email,
                accessToken: tokenResponse.accessToken,
                expiresAt: expiresAt
            )
            
            self.userInfo = userInfo
            authState = .authenticated(accessToken: tokenResponse.accessToken, expiresAt: expiresAt)

            // Save session to secure storage
            let sessionSaved = sessionPersistence.saveSession(userInfo)
            if !sessionSaved {
                print("⚠️ SecureAuthenticationService: Failed to save session to secure storage")
            }

            // Clear any previous errors
            errorHandler.clearError()

            // Haptic feedback for success
            hapticManager.authenticationSucceeded()

            print("🔐 SecureAuthenticationService: Authentication successful")
            
        } catch {
            handleAuthenticationError(error)
        }
    }
    
    /// Perform the actual token exchange API call
    private func performTokenExchange(_ request: TokenRequest) async throws -> TokenResponse {
        guard let url = URL(string: "\(SecureBackendConfig.baseURL)\(SecureBackendConfig.tokenEndpoint)") else {
            throw SecureAuthenticationError.invalidResponse
        }
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.setValue("application/json", forHTTPHeaderField: "Accept")
        urlRequest.timeoutInterval = SecureBackendConfig.connectionTimeout
        
        let requestData = try JSONEncoder().encode(request)
        urlRequest.httpBody = requestData
        
        if SecureBackendConfig.enableLogging {
            print("🔐 SecureAuthenticationService: Sending token exchange request to \(url)")
        }
        
        let (data, response) = try await urlSession.data(for: urlRequest)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw SecureAuthenticationError.networkError(URLError(.badServerResponse))
        }
        
        if SecureBackendConfig.enableLogging {
            print("🔐 SecureAuthenticationService: Token exchange response status: \(httpResponse.statusCode)")
        }
        
        switch httpResponse.statusCode {
        case 200:
            return try JSONDecoder().decode(TokenResponse.self, from: data)
        case 400:
            let error = try? JSONDecoder().decode(APIError.self, from: data)
            throw SecureAuthenticationError.tokenExchangeFailed(error?.message ?? "Bad request")
        case 401:
            let error = try? JSONDecoder().decode(APIError.self, from: data)
            throw SecureAuthenticationError.tokenExchangeFailed(error?.message ?? "Unauthorized")
        case 429:
            throw SecureAuthenticationError.rateLimitExceeded
        case 500...599:
            throw SecureAuthenticationError.serverUnavailable
        default:
            throw SecureAuthenticationError.invalidResponse
        }
    }
    
    /// Handle authentication errors
    private func handleAuthenticationError(_ error: Error) {
        let authError: SecureAuthenticationError

        if let secureError = error as? SecureAuthenticationError {
            authError = secureError
        } else if let asError = error as? ASAuthorizationError {
            switch asError.code {
            case .canceled:
                authError = .appleSignInCancelled
            default:
                authError = .appleSignInFailed(asError.localizedDescription)
            }
        } else {
            authError = .networkError(error)
        }

        lastError = authError
        authState = .failed(authError)

        if authError.shouldClearSession {
            userInfo = nil
        }

        // Use error handler for user feedback
        errorHandler.handleAuthenticationError(authError, context: "authentication") { [weak self] in
            await self?.signInWithApple()
        }

        // Haptic feedback for error
        hapticManager.authenticationFailed()

        print("🔐 SecureAuthenticationService: Authentication error: \(authError.localizedDescription)")
    }
}

// MARK: - ASAuthorizationControllerPresentationContextProviding

extension SecureAuthenticationService: ASAuthorizationControllerPresentationContextProviding {
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return UIWindow()
        }
        return window
    }
}

// MARK: - Apple Sign In Delegate

/// Helper class to handle Apple Sign In delegate callbacks
private class AppleSignInDelegate: NSObject, ASAuthorizationControllerDelegate {
    private let continuation: CheckedContinuation<(identityToken: String, userID: String, email: String?), Error>
    
    init(continuation: CheckedContinuation<(identityToken: String, userID: String, email: String?), Error>) {
        self.continuation = continuation
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        guard let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential,
              let identityTokenData = appleIDCredential.identityToken,
              let identityToken = String(data: identityTokenData, encoding: .utf8) else {
            continuation.resume(throwing: SecureAuthenticationError.appleSignInFailed("Failed to get identity token"))
            return
        }
        
        let userID = appleIDCredential.user
        let email = appleIDCredential.email
        
        continuation.resume(returning: (identityToken: identityToken, userID: userID, email: email))
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        continuation.resume(throwing: error)
    }
}
