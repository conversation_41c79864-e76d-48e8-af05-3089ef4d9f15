//
//  SessionPersistenceService.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import Foundation
import Security

/// Service for persisting authentication sessions securely
class SessionPersistenceService {
    
    // MARK: - Constants
    
    private enum KeychainKeys {
        static let userInfo = "sikting_user_info"
        static let accessToken = "sikting_access_token"
        static let refreshToken = "sikting_refresh_token"
        static let service = "com.superspacex.asr.authentication"
    }
    
    private enum UserDefaultsKeys {
        static let isFirstLaunch = "sikting_first_launch"
        static let lastSignInDate = "sikting_last_signin_date"
        static let sessionExpiryDate = "sikting_session_expiry"
    }
    
    // MARK: - Singleton
    
    static let shared = SessionPersistenceService()
    private init() {}
    
    // MARK: - Public Methods
    
    /// Save user session securely
    func saveSession(_ userInfo: UserInfo) -> Bo<PERSON> {
        do {
            // Save user info to keychain
            let userInfoData = try J<PERSON>NEncoder().encode(userInfo)
            let userInfoSaved = saveToKeychain(
                key: KeychainKeys.userInfo,
                data: userInfoData
            )
            
            // Save access token separately for additional security
            let tokenData = userInfo.accessToken.data(using: .utf8) ?? Data()
            let tokenSaved = saveToKeychain(
                key: KeychainKeys.accessToken,
                data: tokenData
            )
            
            // Save session metadata to UserDefaults
            UserDefaults.standard.set(Date(), forKey: UserDefaultsKeys.lastSignInDate)
            UserDefaults.standard.set(userInfo.expiresAt, forKey: UserDefaultsKeys.sessionExpiryDate)
            
            let success = userInfoSaved && tokenSaved
            print("🔐 SessionPersistence: Session saved successfully: \(success)")
            return success
            
        } catch {
            print("❌ SessionPersistence: Failed to save session: \(error)")
            return false
        }
    }
    
    /// Load user session from secure storage
    func loadSession() -> UserInfo? {
        do {
            // Load user info from keychain
            guard let userInfoData = loadFromKeychain(key: KeychainKeys.userInfo),
                  let userInfo = try? JSONDecoder().decode(UserInfo.self, from: userInfoData) else {
                print("🔐 SessionPersistence: No saved session found")
                return nil
            }
            
            // Verify token is still available
            guard let tokenData = loadFromKeychain(key: KeychainKeys.accessToken),
                  let savedToken = String(data: tokenData, encoding: .utf8),
                  savedToken == userInfo.accessToken else {
                print("❌ SessionPersistence: Token mismatch or missing")
                clearSession()
                return nil
            }
            
            // Check if session is still valid
            if userInfo.expiresAt < Date() {
                print("⏰ SessionPersistence: Session expired, clearing")
                clearSession()
                return nil
            }
            
            print("✅ SessionPersistence: Session loaded successfully")
            return userInfo
            
        } catch {
            print("❌ SessionPersistence: Failed to load session: \(error)")
            clearSession()
            return nil
        }
    }
    
    /// Clear all session data
    func clearSession() {
        // Remove from keychain
        deleteFromKeychain(key: KeychainKeys.userInfo)
        deleteFromKeychain(key: KeychainKeys.accessToken)
        deleteFromKeychain(key: KeychainKeys.refreshToken)
        
        // Remove from UserDefaults
        UserDefaults.standard.removeObject(forKey: UserDefaultsKeys.lastSignInDate)
        UserDefaults.standard.removeObject(forKey: UserDefaultsKeys.sessionExpiryDate)
        
        print("🔐 SessionPersistence: Session cleared")
    }
    
    /// Check if this is the first app launch
    func isFirstLaunch() -> Bool {
        let isFirst = !UserDefaults.standard.bool(forKey: UserDefaultsKeys.isFirstLaunch)
        if isFirst {
            UserDefaults.standard.set(true, forKey: UserDefaultsKeys.isFirstLaunch)
        }
        return isFirst
    }
    
    /// Get last sign-in date
    func getLastSignInDate() -> Date? {
        return UserDefaults.standard.object(forKey: UserDefaultsKeys.lastSignInDate) as? Date
    }
    
    /// Get session expiry date
    func getSessionExpiryDate() -> Date? {
        return UserDefaults.standard.object(forKey: UserDefaultsKeys.sessionExpiryDate) as? Date
    }
    
    /// Check if session needs refresh (within buffer time)
    func sessionNeedsRefresh() -> Bool {
        guard let expiryDate = getSessionExpiryDate() else { return true }
        let bufferTime = SecureBackendConfig.tokenRefreshBuffer
        return Date() > expiryDate.addingTimeInterval(-bufferTime)
    }
    
    // MARK: - Keychain Operations
    
    private func saveToKeychain(key: String, data: Data) -> Bool {
        // Delete existing item first
        deleteFromKeychain(key: key)
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: KeychainKeys.service,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        let status = SecItemAdd(query as CFDictionary, nil)
        let success = status == errSecSuccess
        
        if !success {
            print("❌ SessionPersistence: Failed to save to keychain: \(status)")
        }
        
        return success
    }
    
    private func loadFromKeychain(key: String) -> Data? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: KeychainKeys.service,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        if status == errSecSuccess {
            return result as? Data
        } else if status != errSecItemNotFound {
            print("❌ SessionPersistence: Failed to load from keychain: \(status)")
        }
        
        return nil
    }
    
    private func deleteFromKeychain(key: String) {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: KeychainKeys.service,
            kSecAttrAccount as String: key
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        if status != errSecSuccess && status != errSecItemNotFound {
            print("❌ SessionPersistence: Failed to delete from keychain: \(status)")
        }
    }
}

// MARK: - Background/Foreground Handling

/// Extension for handling app lifecycle events
extension SessionPersistenceService {
    
    /// Handle app entering background
    func handleAppDidEnterBackground() {
        // Update last active timestamp
        UserDefaults.standard.set(Date(), forKey: "sikting_last_active_date")
        print("🔐 SessionPersistence: App entered background")
    }
    
    /// Handle app entering foreground
    func handleAppWillEnterForeground() -> Bool {
        guard let lastActiveDate = UserDefaults.standard.object(forKey: "sikting_last_active_date") as? Date else {
            return true // First launch or no previous session
        }
        
        let backgroundDuration = Date().timeIntervalSince(lastActiveDate)
        let maxBackgroundDuration: TimeInterval = 30 * 60 // 30 minutes
        
        if backgroundDuration > maxBackgroundDuration {
            print("🔐 SessionPersistence: App was in background too long, clearing session")
            clearSession()
            return false
        }
        
        print("🔐 SessionPersistence: App entered foreground, session valid")
        return true
    }
    
    /// Check if session is still valid after background time
    func validateSessionAfterBackground() -> Bool {
        guard let expiryDate = getSessionExpiryDate() else { return false }
        
        let isValid = Date() < expiryDate
        if !isValid {
            print("🔐 SessionPersistence: Session expired while in background")
            clearSession()
        }
        
        return isValid
    }
}
