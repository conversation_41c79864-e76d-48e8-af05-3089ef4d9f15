//
//  AuthenticationHapticManager.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import UIKit

/// Specialized haptic feedback manager for authentication interactions
class AuthenticationHapticManager {
    
    // MARK: - Singleton
    
    static let shared = AuthenticationHapticManager()
    private init() {}
    
    // MARK: - Haptic Generators
    
    private let impactLight = UIImpactFeedbackGenerator(style: .light)
    private let impactMedium = UIImpactFeedbackGenerator(style: .medium)
    private let impactHeavy = UIImpactFeedbackGenerator(style: .heavy)
    private let notificationGenerator = UINotificationFeedbackGenerator()
    private let selectionGenerator = UISelectionFeedbackGenerator()
    
    // MARK: - Authentication Events
    
    /// Haptic feedback for starting authentication
    func authenticationStarted() {
        impactLight.prepare()
        impactLight.impactOccurred()
    }
    
    /// Haptic feedback for successful authentication
    func authenticationSucceeded() {
        notificationGenerator.prepare()
        notificationGenerator.notificationOccurred(.success)
        
        // Add a subtle secondary impact for emphasis
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.impactLight.impactOccurred()
        }
    }
    
    /// Haptic feedback for authentication failure
    func authenticationFailed() {
        notificationGenerator.prepare()
        notificationGenerator.notificationOccurred(.error)
        
        // Add a secondary error impact
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
            self.impactMedium.impactOccurred()
        }
    }
    
    /// Haptic feedback for authentication warning (e.g., token expiring)
    func authenticationWarning() {
        notificationGenerator.prepare()
        notificationGenerator.notificationOccurred(.warning)
    }
    
    // MARK: - Onboarding Events
    
    /// Haptic feedback for onboarding page navigation
    func onboardingPageChanged() {
        selectionGenerator.prepare()
        selectionGenerator.selectionChanged()
    }
    
    /// Haptic feedback for onboarding completion
    func onboardingCompleted() {
        // Success pattern with multiple impacts
        impactMedium.prepare()
        impactMedium.impactOccurred()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.impactLight.impactOccurred()
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.impactLight.impactOccurred()
        }
    }
    
    /// Haptic feedback for onboarding skip
    func onboardingSkipped() {
        impactLight.prepare()
        impactLight.impactOccurred()
    }
    
    // MARK: - Button Interactions
    
    /// Haptic feedback for Apple Sign In button press
    func appleSignInButtonPressed() {
        impactMedium.prepare()
        impactMedium.impactOccurred()
    }
    
    /// Haptic feedback for sign out button press
    func signOutButtonPressed() {
        impactLight.prepare()
        impactLight.impactOccurred()
    }
    
    /// Haptic feedback for retry button press
    func retryButtonPressed() {
        selectionGenerator.prepare()
        selectionGenerator.selectionChanged()
    }
    
    /// Haptic feedback for navigation button press
    func navigationButtonPressed() {
        selectionGenerator.prepare()
        selectionGenerator.selectionChanged()
    }
    
    // MARK: - Connection Events
    
    /// Haptic feedback for WebSocket connection established
    func connectionEstablished() {
        impactLight.prepare()
        impactLight.impactOccurred()
    }
    
    /// Haptic feedback for WebSocket connection lost
    func connectionLost() {
        impactMedium.prepare()
        impactMedium.impactOccurred()
    }
    
    /// Haptic feedback for connection retry
    func connectionRetrying() {
        selectionGenerator.prepare()
        selectionGenerator.selectionChanged()
    }
    
    // MARK: - Session Events
    
    /// Haptic feedback for session restored
    func sessionRestored() {
        impactLight.prepare()
        impactLight.impactOccurred()
    }
    
    /// Haptic feedback for session expired
    func sessionExpired() {
        notificationGenerator.prepare()
        notificationGenerator.notificationOccurred(.warning)
    }
    
    /// Haptic feedback for session cleared
    func sessionCleared() {
        impactLight.prepare()
        impactLight.impactOccurred()
    }
    
    // MARK: - Error Events
    
    /// Haptic feedback for network error
    func networkError() {
        impactMedium.prepare()
        impactMedium.impactOccurred()
        
        // Add a second impact for network errors
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.impactLight.impactOccurred()
        }
    }
    
    /// Haptic feedback for server error
    func serverError() {
        impactHeavy.prepare()
        impactHeavy.impactOccurred()
    }
    
    /// Haptic feedback for rate limit error
    func rateLimitError() {
        // Triple tap pattern for rate limiting
        impactLight.prepare()
        impactLight.impactOccurred()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.impactLight.impactOccurred()
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.impactLight.impactOccurred()
        }
    }
    
    // MARK: - UI Feedback Events
    
    /// Haptic feedback for error message display
    func errorMessageShown() {
        selectionGenerator.prepare()
        selectionGenerator.selectionChanged()
    }
    
    /// Haptic feedback for success message display
    func successMessageShown() {
        impactLight.prepare()
        impactLight.impactOccurred()
    }
    
    /// Haptic feedback for loading state start
    func loadingStarted() {
        selectionGenerator.prepare()
        selectionGenerator.selectionChanged()
    }
    
    /// Haptic feedback for loading state end
    func loadingCompleted() {
        impactLight.prepare()
        impactLight.impactOccurred()
    }
    
    // MARK: - Utility Methods
    
    /// Prepare all haptic generators for optimal performance
    func prepareHaptics() {
        impactLight.prepare()
        impactMedium.prepare()
        impactHeavy.prepare()
        notificationGenerator.prepare()
        selectionGenerator.prepare()
    }
    
    /// Check if haptic feedback is available on the device
    var isHapticFeedbackAvailable: Bool {
        return UIDevice.current.userInterfaceIdiom == .phone
    }
    
    /// Execute haptic feedback only if available and enabled
    private func executeHaptic(_ hapticBlock: @escaping () -> Void) {
        guard isHapticFeedbackAvailable else { return }
        
        // Check if haptics are enabled in system settings
        // Note: There's no direct API to check this, so we assume they're enabled
        hapticBlock()
    }
    
    // MARK: - Custom Patterns
    
    /// Custom haptic pattern for authentication success with celebration
    func authenticationSuccessWithCelebration() {
        guard isHapticFeedbackAvailable else { return }
        
        // Success notification
        notificationGenerator.prepare()
        notificationGenerator.notificationOccurred(.success)
        
        // Celebration pattern
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.impactLight.impactOccurred()
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.impactLight.impactOccurred()
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.impactMedium.impactOccurred()
        }
    }
    
    /// Custom haptic pattern for critical authentication error
    func criticalAuthenticationError() {
        guard isHapticFeedbackAvailable else { return }
        
        // Error notification
        notificationGenerator.prepare()
        notificationGenerator.notificationOccurred(.error)
        
        // Emphasis pattern
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.impactHeavy.impactOccurred()
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.impactMedium.impactOccurred()
        }
    }
}
