//
//  AuthenticationErrorHandler.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import Foundation
import SwiftUI

/// Centralized error handling for authentication and network errors
@MainActor
class AuthenticationErrorHandler: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var currentError: ErrorPresentation?
    @Published var isShowingError = false
    @Published var isRetrying = false
    
    // MARK: - Error Presentation Model
    
    struct ErrorPresentation: Identifiable {
        let id = UUID()
        let title: String
        let message: String
        let primaryAction: ErrorAction?
        let secondaryAction: ErrorAction?
        let severity: ErrorSeverity
        let shouldAutoRetry: Bool
        
        enum ErrorSeverity {
            case info
            case warning
            case error
            case critical
            
            var color: Color {
                switch self {
                case .info:
                    return .blue
                case .warning:
                    return .orange
                case .error:
                    return .red
                case .critical:
                    return .purple
                }
            }
            
            var icon: String {
                switch self {
                case .info:
                    return "info.circle.fill"
                case .warning:
                    return "exclamationmark.triangle.fill"
                case .error:
                    return "xmark.circle.fill"
                case .critical:
                    return "exclamationmark.octagon.fill"
                }
            }
        }
    }
    
    struct ErrorAction {
        let title: String
        let action: () async -> Void
        let style: ActionStyle
        
        enum ActionStyle {
            case primary
            case secondary
            case destructive
        }
    }
    
    // MARK: - Retry Logic
    
    private var retryAttempts: [String: Int] = [:]
    private let maxRetryAttempts = SecureBackendConfig.maxRetryAttempts
    private let retryDelay = SecureBackendConfig.retryDelay
    
    // MARK: - Public Methods
    
    /// Handle authentication errors with appropriate user feedback
    func handleAuthenticationError(_ error: SecureAuthenticationError, context: String = "", retryAction: (() async -> Void)? = nil) {
        let errorKey = "\(context)_\(error.localizedDescription)"
        let currentAttempts = retryAttempts[errorKey, default: 0]
        
        let presentation = createErrorPresentation(
            for: error,
            context: context,
            retryAction: retryAction,
            currentAttempts: currentAttempts
        )
        
        showError(presentation)
        
        // Auto-retry for certain errors
        if error.shouldRetry && currentAttempts < maxRetryAttempts && presentation.shouldAutoRetry {
            scheduleAutoRetry(errorKey: errorKey, retryAction: retryAction)
        }
    }
    
    /// Handle WebSocket errors with appropriate user feedback
    func handleWebSocketError(_ error: SecureWebSocketError, context: String = "", retryAction: (() async -> Void)? = nil) {
        let errorKey = "\(context)_\(error.localizedDescription)"
        let currentAttempts = retryAttempts[errorKey, default: 0]
        
        let presentation = createWebSocketErrorPresentation(
            for: error,
            context: context,
            retryAction: retryAction,
            currentAttempts: currentAttempts
        )
        
        showError(presentation)
        
        // Auto-retry for certain errors
        if error.shouldReauthenticate && currentAttempts < maxRetryAttempts && presentation.shouldAutoRetry {
            scheduleAutoRetry(errorKey: errorKey, retryAction: retryAction)
        }
    }
    
    /// Handle general network errors
    func handleNetworkError(_ error: Error, context: String = "", retryAction: (() async -> Void)? = nil) {
        let errorKey = "\(context)_\(error.localizedDescription)"
        let currentAttempts = retryAttempts[errorKey, default: 0]
        
        let presentation = createNetworkErrorPresentation(
            for: error,
            context: context,
            retryAction: retryAction,
            currentAttempts: currentAttempts
        )
        
        showError(presentation)
        
        // Auto-retry for network errors
        if currentAttempts < maxRetryAttempts && presentation.shouldAutoRetry {
            scheduleAutoRetry(errorKey: errorKey, retryAction: retryAction)
        }
    }
    
    /// Clear current error
    func clearError() {
        currentError = nil
        isShowingError = false
        isRetrying = false
    }
    
    /// Reset retry attempts for a specific context
    func resetRetryAttempts(for context: String) {
        retryAttempts = retryAttempts.filter { key, _ in !key.hasPrefix(context) }
    }
    
    // MARK: - Private Methods
    
    private func showError(_ presentation: ErrorPresentation) {
        currentError = presentation
        isShowingError = true
    }
    
    private func createErrorPresentation(
        for error: SecureAuthenticationError,
        context: String,
        retryAction: (() async -> Void)?,
        currentAttempts: Int
    ) -> ErrorPresentation {
        let title: String
        let severity: ErrorPresentation.ErrorSeverity
        let shouldAutoRetry: Bool
        
        switch error {
        case .appleSignInFailed, .appleSignInCancelled:
            title = "Sign In Failed"
            severity = .error
            shouldAutoRetry = false
            
        case .tokenExchangeFailed:
            title = "Authentication Error"
            severity = .error
            shouldAutoRetry = false
            
        case .networkError:
            title = "Network Error"
            severity = .warning
            shouldAutoRetry = currentAttempts < 2
            
        case .tokenExpired:
            title = "Session Expired"
            severity = .warning
            shouldAutoRetry = true
            
        case .rateLimitExceeded:
            title = "Too Many Requests"
            severity = .warning
            shouldAutoRetry = false
            
        case .serverUnavailable:
            title = "Service Unavailable"
            severity = .error
            shouldAutoRetry = currentAttempts < 1
            
        case .invalidResponse, .invalidToken, .userNotFound:
            title = "Authentication Error"
            severity = .error
            shouldAutoRetry = false
        }
        
        let primaryAction: ErrorAction? = retryAction != nil ? ErrorAction(
            title: currentAttempts > 0 ? "Try Again" : "Retry",
            action: retryAction!,
            style: .primary
        ) : nil
        
        let secondaryAction = ErrorAction(
            title: "Dismiss",
            action: { [weak self] in
                self?.clearError()
            },
            style: .secondary
        )
        
        return ErrorPresentation(
            title: title,
            message: error.userFriendlyMessage,
            primaryAction: primaryAction,
            secondaryAction: secondaryAction,
            severity: severity,
            shouldAutoRetry: shouldAutoRetry
        )
    }
    
    private func createWebSocketErrorPresentation(
        for error: SecureWebSocketError,
        context: String,
        retryAction: (() async -> Void)?,
        currentAttempts: Int
    ) -> ErrorPresentation {
        let title: String
        let severity: ErrorPresentation.ErrorSeverity
        let shouldAutoRetry: Bool
        
        switch error {
        case .authenticationRequired, .tokenExpired, .tokenMissing:
            title = "Authentication Required"
            severity = .warning
            shouldAutoRetry = true
            
        case .connectionFailed:
            title = "Connection Failed"
            severity = .error
            shouldAutoRetry = currentAttempts < 2
            
        case .serverError(let code, _):
            title = code >= 500 ? "Server Error" : "Connection Error"
            severity = code >= 500 ? .error : .warning
            shouldAutoRetry = code >= 500 && currentAttempts < 1
            
        case .networkUnavailable:
            title = "Network Unavailable"
            severity = .warning
            shouldAutoRetry = false
            
        case .invalidURL:
            title = "Configuration Error"
            severity = .error
            shouldAutoRetry = false
        }
        
        let primaryAction: ErrorAction? = retryAction != nil ? ErrorAction(
            title: "Retry",
            action: retryAction!,
            style: .primary
        ) : nil
        
        let secondaryAction = ErrorAction(
            title: "Dismiss",
            action: { [weak self] in
                self?.clearError()
            },
            style: .secondary
        )
        
        return ErrorPresentation(
            title: title,
            message: error.localizedDescription,
            primaryAction: primaryAction,
            secondaryAction: secondaryAction,
            severity: severity,
            shouldAutoRetry: shouldAutoRetry
        )
    }
    
    private func createNetworkErrorPresentation(
        for error: Error,
        context: String,
        retryAction: (() async -> Void)?,
        currentAttempts: Int
    ) -> ErrorPresentation {
        let title = "Network Error"
        let severity: ErrorPresentation.ErrorSeverity = .warning
        let shouldAutoRetry = currentAttempts < 2
        
        let primaryAction: ErrorAction? = retryAction != nil ? ErrorAction(
            title: "Retry",
            action: retryAction!,
            style: .primary
        ) : nil
        
        let secondaryAction = ErrorAction(
            title: "Dismiss",
            action: { [weak self] in
                self?.clearError()
            },
            style: .secondary
        )
        
        return ErrorPresentation(
            title: title,
            message: error.localizedDescription,
            primaryAction: primaryAction,
            secondaryAction: secondaryAction,
            severity: severity,
            shouldAutoRetry: shouldAutoRetry
        )
    }
    
    private func scheduleAutoRetry(errorKey: String, retryAction: (() async -> Void)?) {
        guard let retryAction = retryAction else { return }
        
        isRetrying = true
        retryAttempts[errorKey, default: 0] += 1
        
        Task {
            try? await Task.sleep(nanoseconds: UInt64(retryDelay * 1_000_000_000))
            
            if !Task.isCancelled {
                await retryAction()
                isRetrying = false
            }
        }
    }
}
