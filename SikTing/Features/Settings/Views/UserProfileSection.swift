//
//  UserProfileSection.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import SwiftUI

/// User profile section for settings view
struct UserProfileSection: View {
    
    // MARK: - Properties
    
    @ObservedObject var authService: SecureAuthenticationService
    @State private var showingSignOutAlert = false
    @State private var showingErrorAlert = false
    @State private var errorMessage = ""
    
    // MARK: - Body
    
    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.spacing.medium) {
            sectionHeader("Account")

            VStack(spacing: DesignSystem.spacing.small) {
                if authService.authState.isAuthenticated {
                    authenticatedUserView
                } else {
                    signInPromptView
                }

                // Error feedback
                if authService.errorHandler.isShowingError {
                    ErrorFeedbackView(errorHandler: authService.errorHandler)
                }
            }
        }
        .alert("Sign Out", isPresented: $showingSignOutAlert) {
            But<PERSON>("Cancel", role: .cancel) {}
            <PERSON><PERSON>("Sign Out", role: .destructive) {
                authService.signOut()
            }
        } message: {
            Text("Are you sure you want to sign out? You'll need to sign in again to access speech recognition services.")
        }
        .alert("Error", isPresented: $showingErrorAlert) {
            Button("OK") {}
        } message: {
            Text(errorMessage)
        }
        .onChange(of: authService.lastError) { error in
            if let error = error {
                errorMessage = error.userFriendlyMessage
                showingErrorAlert = true
            }
        }
    }
    
    // MARK: - Section Header
    
    private func sectionHeader(_ title: String) -> some View {
        Text(title)
            .font(DesignSystem.typography.sectionHeader)
            .foregroundColor(DesignSystem.brandColors.persianPurple)
            .fontWeight(.semibold)
    }
    
    // MARK: - Authenticated User View
    
    private var authenticatedUserView: some View {
        VStack(spacing: DesignSystem.spacing.medium) {
            // User info card
            userInfoCard
            
            // Sign out button
            signOutButton
        }
    }
    
    private var userInfoCard: some View {
        HStack(spacing: DesignSystem.spacing.medium) {
            // Avatar
            userAvatar
            
            // User details
            VStack(alignment: .leading, spacing: 4) {
                Text(authService.userInfo?.displayName ?? "User")
                    .font(DesignSystem.typography.headline)
                    .foregroundColor(DesignSystem.brandColors.persianPurple)
                    .fontWeight(.semibold)
                
                if let email = authService.userInfo?.email {
                    Text(email)
                        .font(DesignSystem.typography.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                
                // Authentication status
                HStack(spacing: 4) {
                    Image(systemName: "checkmark.shield.fill")
                        .font(.system(size: 12))
                        .foregroundColor(DesignSystem.brandColors.emeraldGreen)
                    
                    Text("Signed in with Apple")
                        .font(DesignSystem.typography.caption)
                        .foregroundColor(DesignSystem.brandColors.emeraldGreen)
                }
            }
            
            Spacer()
        }
        .padding(DesignSystem.spacing.medium)
        .background(
            RoundedRectangle(cornerRadius: DesignSystem.cornerRadius.medium)
                .fill(DesignSystem.brandColors.alabaster)
                .shadow(color: DesignSystem.brandColors.frenchLilac.opacity(0.1), radius: 2, x: 0, y: 1)
        )
    }
    
    private var userAvatar: some View {
        ZStack {
            // Background circle with gradient
            Circle()
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            DesignSystem.brandColors.persianPurple.opacity(0.8),
                            DesignSystem.brandColors.electricBlue.opacity(0.8)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 50, height: 50)
            
            // Avatar content
            if let userInfo = authService.userInfo {
                // Use first letter of display name or email
                let initial = String(userInfo.displayName.prefix(1)).uppercased()
                Text(initial)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white)
            } else {
                Image(systemName: "person.fill")
                    .font(.system(size: 20))
                    .foregroundColor(.white)
            }
        }
    }
    
    private var signOutButton: some View {
        Button(action: {
            showingSignOutAlert = true
        }) {
            HStack(spacing: 8) {
                Image(systemName: "rectangle.portrait.and.arrow.right")
                    .font(.system(size: 16))
                Text("Sign Out")
                    .font(DesignSystem.typography.body)
                    .fontWeight(.medium)
            }
            .foregroundColor(DesignSystem.brandColors.sunsetOrange)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.cornerRadius.medium)
                    .stroke(DesignSystem.brandColors.sunsetOrange, lineWidth: 1)
            )
        }
    }
    
    // MARK: - Sign In Prompt View
    
    private var signInPromptView: some View {
        VStack(spacing: DesignSystem.spacing.medium) {
            // Sign in prompt card
            signInPromptCard
            
            // Sign in button
            signInButton
        }
    }
    
    private var signInPromptCard: some View {
        VStack(spacing: DesignSystem.spacing.small) {
            // Icon
            Image(systemName: "person.circle.fill")
                .font(.system(size: 40))
                .foregroundColor(DesignSystem.brandColors.persianPurple.opacity(0.6))
            
            // Title
            Text("Sign In Required")
                .font(DesignSystem.typography.headline)
                .foregroundColor(DesignSystem.brandColors.persianPurple)
                .fontWeight(.semibold)
            
            // Description
            Text("Sign in with your Apple ID to access secure speech recognition services and sync your transcription history.")
                .font(DesignSystem.typography.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(3)
        }
        .padding(DesignSystem.spacing.medium)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: DesignSystem.cornerRadius.medium)
                .fill(DesignSystem.brandColors.alabaster)
                .shadow(color: DesignSystem.brandColors.frenchLilac.opacity(0.1), radius: 2, x: 0, y: 1)
        )
    }
    
    private var signInButton: some View {
        Button(action: {
            Task {
                await authService.signInWithApple()
            }
        }) {
            HStack(spacing: 8) {
                Image(systemName: "applelogo")
                    .font(.system(size: 16))
                Text("Sign in with Apple")
                    .font(DesignSystem.typography.body)
                    .fontWeight(.medium)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.cornerRadius.medium)
                    .fill(Color.black)
            )
        }
        .disabled(authService.isLoading)
        .opacity(authService.isLoading ? 0.6 : 1.0)
        .overlay(
            Group {
                if authService.isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                }
            }
        )
    }
}

// MARK: - Preview

#Preview {
    VStack {
        UserProfileSection(authService: SecureAuthenticationService())
        Spacer()
    }
    .padding()
    .background(DesignSystem.brandColors.adaptiveBackgroundGradient)
}
