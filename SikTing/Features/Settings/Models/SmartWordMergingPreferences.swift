//
//  SmartWordMergingPreferences.swift
//  SikTing
//
//  Created by <PERSON><PERSON> on 2025/7/19.
//

import Foundation

/// User preferences manager for Smart Word Merging configuration
/// Handles persistence and retrieval of user settings
/// Requirements: 5.4, 2.4
class SmartWordMergingPreferences: ObservableObject {
    
    // MARK: - UserDefaults Keys
    
    private enum Keys {
        static let enableWordMerging = "smartWordMerging.enableWordMerging"
        static let enableCaching = "smartWordMerging.enableCaching"
        static let enableLanguageDetection = "smartWordMerging.enableLanguageDetection"
        // Performance monitoring removed
        static let maxCacheSize = "smartWordMerging.maxCacheSize"
        static let dictionaryTimeout = "smartWordMerging.dictionaryTimeout"
        static let aggressiveMerging = "smartWordMerging.aggressiveMerging"
        static let preserveOriginalSpacing = "smartWordMerging.preserveOriginalSpacing"
        static let enabledLanguages = "smartWordMerging.enabledLanguages"
        static let configurationPreset = "smartWordMerging.configurationPreset"
        static let showPerformanceWarnings = "smartWordMerging.showPerformanceWarnings"
        static let enableDebugLogging = "smartWordMerging.enableDebugLogging"
    }
    
    // MARK: - Configuration Presets
    
    enum ConfigurationPreset: String, CaseIterable {
        case `default` = "default"
        case debug = "debug"
        case performance = "performance"
        case conservative = "conservative"
        case disabled = "disabled"
        case custom = "custom"
        
        var displayName: String {
            switch self {
            case .default: return "Default"
            case .debug: return "Debug"
            case .performance: return "Performance"
            case .conservative: return "Conservative"
            case .disabled: return "Disabled"
            case .custom: return "Custom"
            }
        }
        
        var description: String {
            switch self {
            case .default: return "Balanced settings for general use"
            case .debug: return "Enhanced logging and monitoring"
            case .performance: return "Optimized for speed and efficiency"
            case .conservative: return "Minimal processing, preserves original text"
            case .disabled: return "Word merging completely disabled"
            case .custom: return "User-customized settings"
            }
        }
        
        var configuration: MergingConfiguration {
            switch self {
            case .default: return .default
            case .debug: return .debug
            case .performance: return .performance
            case .conservative: return .conservative
            case .disabled: return .disabled
            case .custom: return .default // Will be overridden by user settings
            }
        }
    }
    
    // MARK: - Published Properties
    
    @Published var currentPreset: ConfigurationPreset {
        didSet {
            UserDefaults.standard.set(currentPreset.rawValue, forKey: Keys.configurationPreset)
            if currentPreset != .custom {
                loadPresetConfiguration(currentPreset)
            }
        }
    }
    
    @Published var enableWordMerging: Bool {
        didSet {
            UserDefaults.standard.set(enableWordMerging, forKey: Keys.enableWordMerging)
            updatePresetIfNeeded()
        }
    }
    
    @Published var enableCaching: Bool {
        didSet {
            UserDefaults.standard.set(enableCaching, forKey: Keys.enableCaching)
            updatePresetIfNeeded()
        }
    }
    
    @Published var enableLanguageDetection: Bool {
        didSet {
            UserDefaults.standard.set(enableLanguageDetection, forKey: Keys.enableLanguageDetection)
            updatePresetIfNeeded()
        }
    }
    
    // Performance monitoring property removed
    
    @Published var maxCacheSize: Int {
        didSet {
            UserDefaults.standard.set(maxCacheSize, forKey: Keys.maxCacheSize)
            updatePresetIfNeeded()
        }
    }
    
    @Published var dictionaryTimeout: Double {
        didSet {
            UserDefaults.standard.set(dictionaryTimeout, forKey: Keys.dictionaryTimeout)
            updatePresetIfNeeded()
        }
    }
    
    @Published var aggressiveMerging: Bool {
        didSet {
            UserDefaults.standard.set(aggressiveMerging, forKey: Keys.aggressiveMerging)
            updatePresetIfNeeded()
        }
    }
    
    @Published var preserveOriginalSpacing: Bool {
        didSet {
            UserDefaults.standard.set(preserveOriginalSpacing, forKey: Keys.preserveOriginalSpacing)
            updatePresetIfNeeded()
        }
    }
    
    @Published var enabledLanguages: Set<RecognizedLanguage> {
        didSet {
            let languageStrings = enabledLanguages.map { $0.rawValue }
            UserDefaults.standard.set(languageStrings, forKey: Keys.enabledLanguages)
            updatePresetIfNeeded()
        }
    }
    
    @Published var showPerformanceWarnings: Bool {
        didSet {
            UserDefaults.standard.set(showPerformanceWarnings, forKey: Keys.showPerformanceWarnings)
            updatePresetIfNeeded()
        }
    }
    
    @Published var enableDebugLogging: Bool {
        didSet {
            UserDefaults.standard.set(enableDebugLogging, forKey: Keys.enableDebugLogging)
            updatePresetIfNeeded()
        }
    }
    
    // MARK: - Initialization
    
    init() {
        // Load current preset
        let presetString = UserDefaults.standard.string(forKey: Keys.configurationPreset) ?? ConfigurationPreset.default.rawValue
        self.currentPreset = ConfigurationPreset(rawValue: presetString) ?? .default
        
        // Load individual settings
        self.enableWordMerging = UserDefaults.standard.object(forKey: Keys.enableWordMerging) as? Bool ?? true
        self.enableCaching = UserDefaults.standard.object(forKey: Keys.enableCaching) as? Bool ?? true
        self.enableLanguageDetection = UserDefaults.standard.object(forKey: Keys.enableLanguageDetection) as? Bool ?? true
        // Performance monitoring initialization removed
        self.maxCacheSize = UserDefaults.standard.object(forKey: Keys.maxCacheSize) as? Int ?? 1000
        self.dictionaryTimeout = UserDefaults.standard.object(forKey: Keys.dictionaryTimeout) as? Double ?? 0.01
        self.aggressiveMerging = UserDefaults.standard.object(forKey: Keys.aggressiveMerging) as? Bool ?? false
        self.preserveOriginalSpacing = UserDefaults.standard.object(forKey: Keys.preserveOriginalSpacing) as? Bool ?? false
        self.showPerformanceWarnings = UserDefaults.standard.object(forKey: Keys.showPerformanceWarnings) as? Bool ?? false
        self.enableDebugLogging = UserDefaults.standard.object(forKey: Keys.enableDebugLogging) as? Bool ?? false
        
        // Load enabled languages
        let languageStrings = UserDefaults.standard.stringArray(forKey: Keys.enabledLanguages) ?? ["english", "chinese", "japanese"]
        self.enabledLanguages = Set(languageStrings.compactMap { RecognizedLanguage(rawValue: $0) })
        
        // If no settings have been saved yet, load the default preset
        if !UserDefaults.standard.bool(forKey: "smartWordMerging.hasBeenConfigured") {
            loadPresetConfiguration(.default)
            UserDefaults.standard.set(true, forKey: "smartWordMerging.hasBeenConfigured")
        }
    }
    
    // MARK: - Public Methods
    
    /// Get the current configuration based on user preferences
    func getCurrentConfiguration() -> MergingConfiguration {
        return MergingConfiguration(
            enableWordMerging: enableWordMerging,
            enableCaching: enableCaching,
            enableLanguageDetection: enableLanguageDetection,
            enablePerformanceLogging: enableDebugLogging,
            enablePerformanceMonitoring: false, // Performance monitoring disabled
            maxCacheSize: maxCacheSize,
            dictionaryTimeout: dictionaryTimeout,
            maxTokenLength: 50, // Fixed for now
            performanceReportingInterval: 300.0, // Fixed for now
            fallbackToSpacing: true, // Fixed for now
            aggressiveMerging: aggressiveMerging,
            preserveOriginalSpacing: preserveOriginalSpacing,
            enabledLanguages: enabledLanguages,
            chineseWordSegmentation: enabledLanguages.contains(.chinese),
            japaneseWordSegmentation: enabledLanguages.contains(.japanese),
            showPerformanceWarnings: showPerformanceWarnings,
            enableDebugLogging: enableDebugLogging
        )
    }
    
    /// Load a preset configuration
    func loadPresetConfiguration(_ preset: ConfigurationPreset) {
        let config = preset.configuration
        
        // Update properties without triggering preset change
        updatePropertiesFromConfiguration(config)
        
        // Update current preset if it's not already set
        if currentPreset != preset {
            currentPreset = preset
        }
    }
    
    /// Reset to default settings
    func resetToDefaults() {
        loadPresetConfiguration(.default)
    }
    
    /// Export current settings as a dictionary
    func exportSettings() -> [String: Any] {
        return [
            "preset": currentPreset.rawValue,
            "enableWordMerging": enableWordMerging,
            "enableCaching": enableCaching,
            "enableLanguageDetection": enableLanguageDetection,
            // Performance monitoring removed from export
            "maxCacheSize": maxCacheSize,
            "dictionaryTimeout": dictionaryTimeout,
            "aggressiveMerging": aggressiveMerging,
            "preserveOriginalSpacing": preserveOriginalSpacing,
            "enabledLanguages": enabledLanguages.map { $0.rawValue },
            "showPerformanceWarnings": showPerformanceWarnings,
            "enableDebugLogging": enableDebugLogging
        ]
    }
    
    /// Import settings from a dictionary
    func importSettings(_ settings: [String: Any]) {
        if let presetString = settings["preset"] as? String,
           let preset = ConfigurationPreset(rawValue: presetString) {
            currentPreset = preset
        }
        
        if let value = settings["enableWordMerging"] as? Bool { enableWordMerging = value }
        if let value = settings["enableCaching"] as? Bool { enableCaching = value }
        if let value = settings["enableLanguageDetection"] as? Bool { enableLanguageDetection = value }
        // Performance monitoring import removed
        if let value = settings["maxCacheSize"] as? Int { maxCacheSize = value }
        if let value = settings["dictionaryTimeout"] as? Double { dictionaryTimeout = value }
        if let value = settings["aggressiveMerging"] as? Bool { aggressiveMerging = value }
        if let value = settings["preserveOriginalSpacing"] as? Bool { preserveOriginalSpacing = value }
        if let value = settings["showPerformanceWarnings"] as? Bool { showPerformanceWarnings = value }
        if let value = settings["enableDebugLogging"] as? Bool { enableDebugLogging = value }
        
        if let languageStrings = settings["enabledLanguages"] as? [String] {
            enabledLanguages = Set(languageStrings.compactMap { RecognizedLanguage(rawValue: $0) })
        }
    }
    
    // MARK: - Private Methods
    
    private func updatePropertiesFromConfiguration(_ config: MergingConfiguration) {
        enableWordMerging = config.enableWordMerging
        enableCaching = config.enableCaching
        enableLanguageDetection = config.enableLanguageDetection
        // Performance monitoring property update removed
        maxCacheSize = config.maxCacheSize
        dictionaryTimeout = config.dictionaryTimeout
        aggressiveMerging = config.aggressiveMerging
        preserveOriginalSpacing = config.preserveOriginalSpacing
        enabledLanguages = config.enabledLanguages
        showPerformanceWarnings = config.showPerformanceWarnings
        enableDebugLogging = config.enableDebugLogging
    }
    
    private func updatePresetIfNeeded() {
        // Check if current settings match any preset
        let currentConfig = getCurrentConfiguration()
        
        for preset in ConfigurationPreset.allCases where preset != .custom {
            if configurationMatches(currentConfig, preset.configuration) {
                if currentPreset != preset {
                    currentPreset = preset
                }
                return
            }
        }
        
        // If no preset matches, set to custom
        if currentPreset != .custom {
            currentPreset = .custom
        }
    }
    
    private func configurationMatches(_ config1: MergingConfiguration, _ config2: MergingConfiguration) -> Bool {
        return config1.enableWordMerging == config2.enableWordMerging &&
               config1.enableCaching == config2.enableCaching &&
               config1.enableLanguageDetection == config2.enableLanguageDetection &&
               // Performance monitoring comparison removed
               config1.maxCacheSize == config2.maxCacheSize &&
               abs(config1.dictionaryTimeout - config2.dictionaryTimeout) < 0.001 &&
               config1.aggressiveMerging == config2.aggressiveMerging &&
               config1.preserveOriginalSpacing == config2.preserveOriginalSpacing &&
               config1.enabledLanguages == config2.enabledLanguages &&
               config1.showPerformanceWarnings == config2.showPerformanceWarnings &&
               config1.enableDebugLogging == config2.enableDebugLogging
    }
}

/// Global shared instance
extension SmartWordMergingPreferences {
    static let shared = SmartWordMergingPreferences()
}
