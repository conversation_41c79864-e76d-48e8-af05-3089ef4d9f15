//
//  OnboardingPageView.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import SwiftUI

/// Individual onboarding page view
struct OnboardingPageView: View {
    
    // MARK: - Properties
    
    let page: OnboardingPage
    let isLastPage: Bool
    let onNext: () -> Void
    let onSkip: (() -> Void)?
    
    // MARK: - Body
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // Skip button (if allowed)
                if let onSkip = onSkip {
                    HStack {
                        Spacer()
                        Button("Skip") {
                            onSkip()
                        }
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.brandColors.persianPurple)
                        .padding(.trailing, 20)
                        .padding(.top, 10)
                    }
                } else {
                    Spacer()
                        .frame(height: 50)
                }
                
                Spacer()
                
                // Main content
                VStack(spacing: 40) {
                    // Icon
                    iconView
                        .frame(height: geometry.size.height * 0.25)
                    
                    // Text content
                    textContent
                        .frame(maxHeight: geometry.size.height * 0.35)
                }
                .padding(.horizontal, 30)
                
                Spacer()
                Spacer() // Extra space for navigation controls
            }
        }
        .background(page.backgroundColor)
    }
    
    // MARK: - Icon View
    
    private var iconView: some View {
        ZStack {
            // Background circle
            Circle()
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            DesignSystem.brandColors.persianPurple.opacity(0.2),
                            DesignSystem.brandColors.electricBlue.opacity(0.2)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 120, height: 120)
            
            // Icon
            Image(systemName: page.imageName)
                .font(.system(size: 50, weight: .light))
                .foregroundColor(DesignSystem.brandColors.persianPurple)
        }
        .scaleEffect(1.0)
        .animation(.easeInOut(duration: 0.6).delay(0.2), value: page.id)
    }
    
    // MARK: - Text Content
    
    private var textContent: some View {
        VStack(spacing: 20) {
            // Title
            Text(page.title)
                .font(.system(size: 28, weight: .bold, design: .rounded))
                .foregroundColor(DesignSystem.brandColors.persianPurple)
                .multilineTextAlignment(.center)
                .lineLimit(2)
            
            // Subtitle
            Text(page.subtitle)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(DesignSystem.brandColors.electricBlue)
                .multilineTextAlignment(.center)
                .lineLimit(2)
            
            // Description
            Text(page.description)
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(4)
                .lineSpacing(4)
                .padding(.horizontal, 10)
        }
        .opacity(1.0)
        .animation(.easeInOut(duration: 0.6).delay(0.4), value: page.id)
    }
}

// MARK: - Preview

#Preview {
    OnboardingPageView(
        page: OnboardingData.pages[0],
        isLastPage: false,
        onNext: {},
        onSkip: {}
    )
}
