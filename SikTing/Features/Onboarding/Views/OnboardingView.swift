//
//  OnboardingView.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import SwiftUI

/// Main onboarding view with swipeable pages
struct OnboardingView: View {
    
    // MARK: - Properties
    
    @Binding var isPresented: Bool
    @ObservedObject var authService: SecureAuthenticationService
    @State private var currentPage = 0
    @State private var onboardingState: OnboardingState = .notStarted
    @State private var showingAppleSignIn = false

    // Haptic feedback
    private let hapticManager = AuthenticationHapticManager.shared
    
    private let configuration = OnboardingConfiguration.default
    private let pages = OnboardingData.pages
    
    let onComplete: () -> Void
    
    // MARK: - Body
    
    var body: some View {
        ZStack {
            // Background gradient
            LinearGradient(
                gradient: Gradient(colors: [
                    DesignSystem.brandColors.persianPurple.opacity(0.1),
                    DesignSystem.brandColors.orchid.opacity(0.1)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // Page indicator
                if configuration.showPageIndicator {
                    pageIndicator
                        .padding(.top, 20)
                }
                
                // Main content
                TabView(selection: $currentPage) {
                    ForEach(Array(pages.enumerated()), id: \.element.id) { index, page in
                        if page.isAppleSignInPage {
                            AppleSignInPageView(
                                page: page,
                                authService: authService,
                                onSignInSuccess: handleSignInSuccess,
                                onSkip: handleSkip
                            )
                            .tag(index)
                        } else {
                            OnboardingPageView(
                                page: page,
                                isLastPage: OnboardingData.isLastPage(index),
                                onNext: handleNextPage,
                                onSkip: configuration.allowSkipping ? handleSkip : nil
                            )
                            .tag(index)
                        }
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .onboardingPageTransition(currentPage: currentPage, pageIndex: currentPage)
                
                // Navigation controls
                if !OnboardingData.isAppleSignInPage(currentPage) {
                    navigationControls
                        .padding(.bottom, 40)
                }
            }
        }
        .authenticationAccessibility(
            identifier: AuthenticationAccessibilityIdentifiers.onboardingView,
            label: AuthenticationAccessibilityLabels.onboardingPageTitle(pages[currentPage].title),
            hint: AuthenticationAccessibilityHints.onboardingPageSwipe
        )
        .onAppear {
            onboardingState = .inProgress(currentPage: 0)
            hapticManager.prepareHaptics()
        }
        .onChange(of: currentPage) { newPage in
            if OnboardingData.isAppleSignInPage(newPage) {
                onboardingState = .appleSignIn
            } else {
                onboardingState = .inProgress(currentPage: newPage)
            }

            // Haptic feedback and accessibility
            hapticManager.onboardingPageChanged()
            AuthenticationAccessibilityAnnouncer.announceOnboardingPageChange(
                current: newPage,
                total: pages.count,
                title: pages[newPage].title
            )
        }
        .onChange(of: authService.authState) { authState in
            if authState.isAuthenticated {
                handleSignInSuccess()
            }
        }
    }
    
    // MARK: - Page Indicator
    
    private var pageIndicator: some View {
        HStack(spacing: 8) {
            ForEach(0..<pages.count, id: \.self) { index in
                Circle()
                    .fill(index == currentPage ? DesignSystem.brandColors.persianPurple : Color.gray.opacity(0.3))
                    .frame(width: 8, height: 8)
                    .scaleEffect(index == currentPage ? 1.2 : 1.0)
                    .animation(.easeInOut(duration: 0.2), value: currentPage)
            }
        }
    }
    
    // MARK: - Navigation Controls
    
    private var navigationControls: some View {
        HStack {
            // Previous button
            if currentPage > 0 {
                Button(action: handlePreviousPage) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                        Text("Previous")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(DesignSystem.brandColors.persianPurple)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .stroke(DesignSystem.brandColors.persianPurple, lineWidth: 1)
                    )
                }
            } else {
                Spacer()
                    .frame(width: 100) // Maintain layout balance
            }
            
            Spacer()
            
            // Next button
            Button(action: handleNextPage) {
                HStack(spacing: 8) {
                    Text(OnboardingData.isLastPage(currentPage) ? "Get Started" : "Next")
                        .font(.system(size: 16, weight: .medium))
                    if !OnboardingData.isLastPage(currentPage) {
                        Image(systemName: "chevron.right")
                            .font(.system(size: 16, weight: .medium))
                    }
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(DesignSystem.brandColors.persianPurple)
                )
            }
        }
        .padding(.horizontal, 30)
    }
    
    // MARK: - Actions
    
    private func handleNextPage() {
        hapticManager.navigationButtonPressed()

        if OnboardingData.isLastPage(currentPage) {
            // Move to Apple Sign In page
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0)) {
                currentPage = pages.count - 1
            }
        } else {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0)) {
                currentPage = min(currentPage + 1, pages.count - 1)
            }
        }
    }
    
    private func handlePreviousPage() {
        hapticManager.navigationButtonPressed()

        withAnimation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0)) {
            currentPage = max(currentPage - 1, 0)
        }
    }

    private func handleSkip() {
        hapticManager.onboardingSkipped()
        onboardingState = .skipped
        completeOnboarding()
    }

    private func handleSignInSuccess() {
        hapticManager.onboardingCompleted()
        onboardingState = .completed
        completeOnboarding()
    }

    private func completeOnboarding() {
        authService.completeOnboarding()
        isPresented = false
        onComplete()
    }
}

// MARK: - Preview

#Preview {
    OnboardingView(
        isPresented: .constant(true),
        authService: SecureAuthenticationService(),
        onComplete: {}
    )
}
