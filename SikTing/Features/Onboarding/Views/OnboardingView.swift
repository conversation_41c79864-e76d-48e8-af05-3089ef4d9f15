//
//  OnboardingView.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import SwiftUI

/// Main onboarding view with swipeable pages
struct OnboardingView: View {
    
    // MARK: - Properties
    
    @Binding var isPresented: Bool
    @ObservedObject var authService: SecureAuthenticationService
    @State private var currentPage = 0
    @State private var onboardingState: OnboardingState = .notStarted
    @State private var showingAppleSignIn = false
    
    private let configuration = OnboardingConfiguration.default
    private let pages = OnboardingData.pages
    
    let onComplete: () -> Void
    
    // MARK: - Body
    
    var body: some View {
        ZStack {
            // Background gradient
            LinearGradient(
                gradient: Gradient(colors: [
                    DesignSystem.brandColors.persianPurple.opacity(0.1),
                    DesignSystem.brandColors.electricBlue.opacity(0.1)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // Page indicator
                if configuration.showPageIndicator {
                    pageIndicator
                        .padding(.top, 20)
                }
                
                // Main content
                TabView(selection: $currentPage) {
                    ForEach(Array(pages.enumerated()), id: \.element.id) { index, page in
                        if page.isAppleSignInPage {
                            AppleSignInPageView(
                                page: page,
                                authService: authService,
                                onSignInSuccess: handleSignInSuccess,
                                onSkip: handleSkip
                            )
                            .tag(index)
                        } else {
                            OnboardingPageView(
                                page: page,
                                isLastPage: OnboardingData.isLastPage(index),
                                onNext: handleNextPage,
                                onSkip: configuration.allowSkipping ? handleSkip : nil
                            )
                            .tag(index)
                        }
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .animation(.easeInOut(duration: 0.3), value: currentPage)
                
                // Navigation controls
                if !OnboardingData.isAppleSignInPage(currentPage) {
                    navigationControls
                        .padding(.bottom, 40)
                }
            }
        }
        .onAppear {
            onboardingState = .inProgress(currentPage: 0)
        }
        .onChange(of: currentPage) { newPage in
            if OnboardingData.isAppleSignInPage(newPage) {
                onboardingState = .appleSignIn
            } else {
                onboardingState = .inProgress(currentPage: newPage)
            }
        }
        .onChange(of: authService.authState) { authState in
            if authState.isAuthenticated {
                handleSignInSuccess()
            }
        }
    }
    
    // MARK: - Page Indicator
    
    private var pageIndicator: some View {
        HStack(spacing: 8) {
            ForEach(0..<pages.count, id: \.self) { index in
                Circle()
                    .fill(index == currentPage ? DesignSystem.brandColors.persianPurple : Color.gray.opacity(0.3))
                    .frame(width: 8, height: 8)
                    .scaleEffect(index == currentPage ? 1.2 : 1.0)
                    .animation(.easeInOut(duration: 0.2), value: currentPage)
            }
        }
    }
    
    // MARK: - Navigation Controls
    
    private var navigationControls: some View {
        HStack {
            // Previous button
            if currentPage > 0 {
                Button(action: handlePreviousPage) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                        Text("Previous")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(DesignSystem.brandColors.persianPurple)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .stroke(DesignSystem.brandColors.persianPurple, lineWidth: 1)
                    )
                }
            } else {
                Spacer()
                    .frame(width: 100) // Maintain layout balance
            }
            
            Spacer()
            
            // Next button
            Button(action: handleNextPage) {
                HStack(spacing: 8) {
                    Text(OnboardingData.isLastPage(currentPage) ? "Get Started" : "Next")
                        .font(.system(size: 16, weight: .medium))
                    if !OnboardingData.isLastPage(currentPage) {
                        Image(systemName: "chevron.right")
                            .font(.system(size: 16, weight: .medium))
                    }
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(DesignSystem.brandColors.persianPurple)
                )
            }
        }
        .padding(.horizontal, 30)
    }
    
    // MARK: - Actions
    
    private func handleNextPage() {
        if OnboardingData.isLastPage(currentPage) {
            // Move to Apple Sign In page
            withAnimation(.easeInOut(duration: 0.3)) {
                currentPage = pages.count - 1
            }
        } else {
            withAnimation(.easeInOut(duration: 0.3)) {
                currentPage = min(currentPage + 1, pages.count - 1)
            }
        }
    }
    
    private func handlePreviousPage() {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentPage = max(currentPage - 1, 0)
        }
    }
    
    private func handleSkip() {
        onboardingState = .skipped
        completeOnboarding()
    }
    
    private func handleSignInSuccess() {
        onboardingState = .completed
        completeOnboarding()
    }
    
    private func completeOnboarding() {
        authService.completeOnboarding()
        isPresented = false
        onComplete()
    }
}

// MARK: - Preview

#Preview {
    OnboardingView(
        isPresented: .constant(true),
        authService: SecureAuthenticationService(),
        onComplete: {}
    )
}
