//
//  AppleSignInPageView.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import SwiftUI
import AuthenticationServices

/// Apple Sign In page for onboarding
struct AppleSignInPageView: View {
    
    // MARK: - Properties
    
    let page: OnboardingPage
    @ObservedObject var authService: SecureAuthenticationService
    let onSignInSuccess: () -> Void
    let onSkip: (() -> Void)?
    
    @State private var isSigningIn = false
    @State private var showingError = false
    @State private var errorMessage = ""

    // Haptic feedback
    private let hapticManager = AuthenticationHapticManager.shared
    
    // MARK: - Body
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // Skip button (if allowed)
                if let onSkip = onSkip {
                    HStack {
                        Spacer()
                        Button("Skip") {
                            onSkip()
                        }
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.brandColors.persianPurple)
                        .padding(.trailing, 20)
                        .padding(.top, 10)
                    }
                } else {
                    Spacer()
                        .frame(height: 50)
                }
                
                Spacer()
                
                // Main content
                VStack(spacing: 40) {
                    // Icon
                    iconView
                        .frame(height: geometry.size.height * 0.2)
                    
                    // Text content
                    textContent
                        .frame(maxHeight: geometry.size.height * 0.3)
                    
                    // Apple Sign In button
                    signInSection
                        .frame(maxHeight: geometry.size.height * 0.2)
                }
                .padding(.horizontal, 30)

                // Error feedback
                if authService.errorHandler.isShowingError {
                    ErrorFeedbackView(errorHandler: authService.errorHandler)
                        .padding(.horizontal, 20)
                }

                Spacer()
            }
        }
        .background(page.backgroundColor)
        .onChange(of: authService.authState) { authState in
            handleAuthStateChange(authState)
        }
        .onChange(of: authService.lastError) { error in
            if let error = error {
                handleAuthError(error)
            }
        }
        .alert("Sign In Error", isPresented: $showingError) {
            Button("OK") {
                showingError = false
            }
        } message: {
            Text(errorMessage)
        }
    }
    
    // MARK: - Icon View
    
    private var iconView: some View {
        ZStack {
            // Background circle
            Circle()
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            DesignSystem.brandColors.persianPurple.opacity(0.2),
                            DesignSystem.brandColors.orchid.opacity(0.2)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 100, height: 100)
            
            // Icon
            Image(systemName: page.imageName)
                .font(.system(size: 40, weight: .light))
                .foregroundColor(DesignSystem.brandColors.persianPurple)
        }
    }
    
    // MARK: - Text Content
    
    private var textContent: some View {
        VStack(spacing: 16) {
            // Title
            Text(page.title)
                .font(.system(size: 26, weight: .bold, design: .rounded))
                .foregroundColor(DesignSystem.brandColors.persianPurple)
                .multilineTextAlignment(.center)
                .lineLimit(2)
            
            // Subtitle
            Text(page.subtitle)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.brandColors.orchid)
                .multilineTextAlignment(.center)
                .lineLimit(2)
            
            // Description
            Text(page.description)
                .font(.system(size: 15, weight: .regular))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(3)
                .lineSpacing(3)
                .padding(.horizontal, 10)
        }
    }
    
    // MARK: - Sign In Section
    
    private var signInSection: some View {
        VStack(spacing: 20) {
            // Apple Sign In Button
            SignInWithAppleButton(
                onRequest: { request in
                    request.requestedScopes = [.fullName, .email]
                },
                onCompletion: { _ in
                    // Handled by authService
                }
            )
            .signInWithAppleButtonStyle(.black)
            .frame(height: 50)
            .cornerRadius(25)
            .disabled(isSigningIn)
            .appleSignInButtonAnimation(isLoading: isSigningIn)
            .interactiveButtonAnimation(isEnabled: !isSigningIn)
            .appleSignInButtonAccessibility(isLoading: isSigningIn)
            .onTapGesture {
                handleSignInTap()
            }
            
            // Loading indicator
            if isSigningIn {
                HStack(spacing: 8) {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("Signing in...")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                }
                .transition(.opacity)
            }
            
            // Privacy note
            Text("Your privacy is protected. We only use your Apple ID for secure authentication.")
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
                .padding(.horizontal, 20)
        }
    }
    
    // MARK: - Actions
    
    private func handleSignInTap() {
        guard !isSigningIn else { return }

        hapticManager.appleSignInButtonPressed()
        isSigningIn = true

        Task {
            await authService.signInWithApple()
        }
    }
    
    private func handleAuthStateChange(_ authState: AuthenticationState) {
        switch authState {
        case .authenticating:
            isSigningIn = true
            hapticManager.authenticationStarted()
        case .authenticated:
            isSigningIn = false
            hapticManager.authenticationSuccessWithCelebration()
            AuthenticationAccessibilityAnnouncer.announceSuccess(AuthenticationAccessibilityLabels.appleSignInSuccess)
            onSignInSuccess()
        case .failed:
            isSigningIn = false
            hapticManager.authenticationFailed()
        case .notAuthenticated:
            isSigningIn = false
        }

        // Announce state changes
        AuthenticationAccessibilityAnnouncer.announceAuthenticationStateChange(authState)
    }
    
    private func handleAuthError(_ error: SecureAuthenticationError) {
        isSigningIn = false
        
        // Don't show error for cancelled sign in
        if case .appleSignInCancelled = error {
            return
        }
        
        errorMessage = error.userFriendlyMessage
        showingError = true
    }
}

// MARK: - Preview

#Preview {
    AppleSignInPageView(
        page: OnboardingData.pages.last!,
        authService: SecureAuthenticationService(),
        onSignInSuccess: {},
        onSkip: nil
    )
}
