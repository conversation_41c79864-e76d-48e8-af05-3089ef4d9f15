//
//  OnboardingModels.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import Foundation
import SwiftUI

// MARK: - Onboarding Page Model

/// Represents a single onboarding page
struct OnboardingPage: Identifiable, Equatable {
    let id = UUID()
    let title: String
    let subtitle: String
    let description: String
    let imageName: String
    let backgroundColor: Color
    let isAppleSignInPage: Bool
    
    init(title: String, subtitle: String, description: String, imageName: String, backgroundColor: Color = .clear, isAppleSignInPage: Bool = false) {
        self.title = title
        self.subtitle = subtitle
        self.description = description
        self.imageName = imageName
        self.backgroundColor = backgroundColor
        self.isAppleSignInPage = isAppleSignInPage
    }
}

// MARK: - Onboarding Data

/// Static data for onboarding pages
struct OnboardingData {
    static let pages: [OnboardingPage] = [
        OnboardingPage(
            title: "Welcome to SikTing",
            subtitle: "Your AI-Powered Speech Assistant",
            description: "Transform your voice into text with advanced AI speech recognition technology. Perfect for notes, transcriptions, and more.",
            imageName: "mic.circle.fill",
            backgroundColor: DesignSystem.brandColors.persianPurple.opacity(0.1)
        ),
        OnboardingPage(
            title: "Real-Time Transcription",
            subtitle: "Instant Voice-to-Text",
            description: "Watch your words appear in real-time as you speak. Our advanced AI provides accurate transcriptions with confidence scoring.",
            imageName: "waveform.circle.fill",
            backgroundColor: DesignSystem.brandColors.orchid.opacity(0.1)
        ),
        OnboardingPage(
            title: "Smart Translation",
            subtitle: "Break Language Barriers",
            description: "Instantly translate your transcribed text into multiple languages. Perfect for international communication and learning.",
            imageName: "globe.circle.fill",
            backgroundColor: DesignSystem.brandColors.frenchLilac.opacity(0.1)
        ),
        OnboardingPage(
            title: "History & Search",
            subtitle: "Never Lose Your Words",
            description: "All your transcriptions are automatically saved and searchable. Find any conversation or note instantly with powerful search.",
            imageName: "clock.circle.fill",
            backgroundColor: DesignSystem.brandColors.amber.opacity(0.1)
        ),
        OnboardingPage(
            title: "Secure & Private",
            subtitle: "Your Data, Protected",
            description: "Sign in with Apple for secure, private access to your transcriptions. Your privacy is our priority.",
            imageName: "shield.checkered.circle.fill",
            backgroundColor: DesignSystem.brandColors.persianPurple.opacity(0.1),
            isAppleSignInPage: true
        )
    ]
    
    /// Get the total number of pages
    static var pageCount: Int {
        return pages.count
    }
    
    /// Check if a page index is the last page
    static func isLastPage(_ index: Int) -> Bool {
        return index == pages.count - 1
    }
    
    /// Check if a page index is the Apple Sign In page
    static func isAppleSignInPage(_ index: Int) -> Bool {
        guard index < pages.count else { return false }
        return pages[index].isAppleSignInPage
    }
}

// MARK: - Onboarding State

/// Tracks the current state of the onboarding flow
enum OnboardingState {
    case notStarted
    case inProgress(currentPage: Int)
    case appleSignIn
    case completed
    case skipped
    
    var currentPageIndex: Int {
        switch self {
        case .inProgress(let currentPage):
            return currentPage
        case .appleSignIn:
            return OnboardingData.pageCount - 1
        default:
            return 0
        }
    }
    
    var isCompleted: Bool {
        switch self {
        case .completed:
            return true
        default:
            return false
        }
    }
    
    var canProceed: Bool {
        switch self {
        case .notStarted, .inProgress:
            return true
        default:
            return false
        }
    }
}

// MARK: - Onboarding Actions

/// Actions that can be performed during onboarding
enum OnboardingAction {
    case nextPage
    case previousPage
    case skipOnboarding
    case startAppleSignIn
    case completeOnboarding
    case dismissOnboarding
}

// MARK: - Onboarding Configuration

/// Configuration for onboarding behavior
struct OnboardingConfiguration {
    let allowSkipping: Bool
    let autoAdvanceDelay: TimeInterval?
    let showPageIndicator: Bool
    let enableSwipeGestures: Bool
    
    static let `default` = OnboardingConfiguration(
        allowSkipping: false, // Don't allow skipping since authentication is required
        autoAdvanceDelay: nil, // No auto-advance
        showPageIndicator: true,
        enableSwipeGestures: true
    )
}
