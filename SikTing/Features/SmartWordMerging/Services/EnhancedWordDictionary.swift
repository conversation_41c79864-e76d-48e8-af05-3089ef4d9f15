//
//  EnhancedWordDictionary.swift
//  SikTing
//
//  Created by <PERSON><PERSON> on 2025/7/19.
//  Enhanced word dictionary for smart word merging
//

import Foundation

/// Enhanced dictionary that supplements UITextChecker with custom word patterns
/// and common compound words that might be split during speech recognition
class EnhancedWordDictionary {
    
    // MARK: - Static Word Lists
    
    /// Common compound words that are frequently split during speech recognition
    private static let commonCompoundWords: Set<String> = [
        // Some- compounds
        "something", "someone", "somebody", "somewhere", "sometime", "sometimes",
        "somewhat", "someday", "somehow", "someplace", "someway", "someways",
        
        // Any- compounds  
        "anything", "anyone", "anybody", "anywhere", "anytime", "anyway",
        "anyplace", "anyways", "anyhow", "anymore",
        
        // Every- compounds
        "everything", "everyone", "everybody", "everywhere", "everyday", "everytime",
        "everyplace", "everyway", "everytime",
        
        // No- compounds
        "nothing", "nobody", "nowhere", "noone", "nowadays", "nonetheless",
        "notwithstanding", "nothingness",
        
        // Other common compounds
        "into", "onto", "upon", "within", "without", "throughout", "outside",
        "inside", "upstairs", "downstairs", "underground", "background",
        "foreground", "playground", "backyard", "frontyard", "sidewalk",
        "highway", "freeway", "pathway", "walkway", "driveway", "doorway",
        "hallway", "stairway", "gateway", "runway", "subway", "railway",
        
        // Time-related compounds
        "today", "tomorrow", "yesterday", "tonight", "afternoon", "morning",
        "evening", "midnight", "daylight", "sunlight", "moonlight", "starlight",
        "daybreak", "nightfall", "sunrise", "sunset", "daytime", "nighttime",
        "weekday", "weekend", "weeknight", "birthday", "holiday",
        
        // Technology compounds
        "smartphone", "laptop", "desktop", "keyboard", "mousepad", "headphones",
        "earphones", "bluetooth", "wifi", "internet", "website", "homepage",
        "database", "software", "hardware", "firmware", "malware", "spyware",
        "antivirus", "firewall", "password", "username", "email", "voicemail",
        
        // Household compounds
        "bedroom", "bathroom", "kitchen", "living room", "dining room",
        "basement", "attic", "garage", "backyard", "frontyard", "driveway",
        "doorbell", "doorknob", "window", "rooftop", "floorboard", "wallpaper",
        "bookshelf", "nightstand", "wardrobe", "cupboard", "refrigerator",
        
        // Common phrases that might be merged
        "alright", "already", "altogether", "although", "always", "maybe",
        "cannot", "everybody", "everything", "everywhere", "however",
        "whatever", "whenever", "wherever", "whoever", "whichever"
    ]
    
    /// Common word patterns that should be merged (prefix -> suffix -> result)
    private static let mergingPatterns: [(prefix: String, suffix: String, result: String)] = [
        // Some patterns
        ("some", "thing", "something"),
        ("some", "one", "someone"),
        ("some", "body", "somebody"),
        ("some", "where", "somewhere"),
        ("some", "time", "sometime"),
        ("some", "times", "sometimes"),
        ("some", "what", "somewhat"),
        ("some", "day", "someday"),
        ("some", "how", "somehow"),
        ("some", "place", "someplace"),
        
        // Any patterns
        ("any", "thing", "anything"),
        ("any", "one", "anyone"),
        ("any", "body", "anybody"),
        ("any", "where", "anywhere"),
        ("any", "time", "anytime"),
        ("any", "way", "anyway"),
        ("any", "place", "anyplace"),
        ("any", "how", "anyhow"),
        ("any", "more", "anymore"),
        
        // Every patterns
        ("every", "thing", "everything"),
        ("every", "one", "everyone"),
        ("every", "body", "everybody"),
        ("every", "where", "everywhere"),
        ("every", "day", "everyday"),
        ("every", "time", "everytime"),
        ("every", "place", "everyplace"),
        
        // No patterns
        ("no", "thing", "nothing"),
        ("no", "body", "nobody"),
        ("no", "where", "nowhere"),
        ("no", "one", "noone"),
        
        // Preposition patterns
        ("in", "to", "into"),
        ("on", "to", "onto"),
        ("up", "on", "upon"),
        ("with", "in", "within"),
        ("with", "out", "without"),
        ("through", "out", "throughout"),
        ("out", "side", "outside"),
        ("in", "side", "inside"),
        
        // Direction patterns
        ("up", "stairs", "upstairs"),
        ("down", "stairs", "downstairs"),
        ("under", "ground", "underground"),
        ("back", "ground", "background"),
        ("fore", "ground", "foreground"),
        ("play", "ground", "playground"),
        ("back", "yard", "backyard"),
        ("front", "yard", "frontyard"),
        
        // Way patterns
        ("side", "walk", "sidewalk"),
        ("high", "way", "highway"),
        ("free", "way", "freeway"),
        ("path", "way", "pathway"),
        ("walk", "way", "walkway"),
        ("drive", "way", "driveway"),
        ("door", "way", "doorway"),
        ("hall", "way", "hallway"),
        ("stair", "way", "stairway"),
        ("gate", "way", "gateway"),
        ("run", "way", "runway"),
        ("sub", "way", "subway"),
        ("rail", "way", "railway")
    ]
    
    // MARK: - Public Methods
    
    /// Check if a combined word is valid using enhanced dictionary
    /// - Parameter combinedWord: The word to validate
    /// - Returns: True if the word is valid according to enhanced dictionary
    static func isValidCompoundWord(_ combinedWord: String) -> Bool {
        let lowercased = combinedWord.lowercased()
        return commonCompoundWords.contains(lowercased)
    }
    
    /// Check if two tokens should be merged based on known patterns
    /// - Parameters:
    ///   - prefix: The first token (prefix)
    ///   - suffix: The second token (suffix)
    /// - Returns: The merged word if pattern is found, nil otherwise
    static func getMergedWord(prefix: String, suffix: String) -> String? {
        let lowercasePrefix = prefix.lowercased()
        let lowercaseSuffix = suffix.lowercased()
        
        // Check direct patterns
        for pattern in mergingPatterns {
            if pattern.prefix == lowercasePrefix && pattern.suffix == lowercaseSuffix {
                return pattern.result
            }
        }
        
        // Check if direct concatenation is in compound words list
        let directCombination = lowercasePrefix + lowercaseSuffix
        if commonCompoundWords.contains(directCombination) {
            return directCombination
        }
        
        return nil
    }
    
    /// Get all possible merging patterns for debugging/testing
    /// - Returns: Array of all known merging patterns
    static func getAllMergingPatterns() -> [(prefix: String, suffix: String, result: String)] {
        return mergingPatterns
    }
    
    /// Get all compound words for debugging/testing
    /// - Returns: Set of all known compound words
    static func getAllCompoundWords() -> Set<String> {
        return commonCompoundWords
    }
    
    /// Check if a word is a common prefix that might be part of a compound
    /// - Parameter word: The word to check
    /// - Returns: True if the word is a common compound prefix
    static func isCommonCompoundPrefix(_ word: String) -> Bool {
        let lowercased = word.lowercased()
        let commonPrefixes: Set<String> = [
            "some", "any", "every", "no", "in", "on", "up", "with", "through",
            "out", "down", "under", "back", "fore", "play", "front", "side",
            "high", "free", "path", "walk", "drive", "door", "hall", "stair",
            "gate", "run", "sub", "rail"
        ]
        return commonPrefixes.contains(lowercased)
    }
    
    /// Check if a word is a common suffix that might be part of a compound
    /// - Parameter word: The word to check
    /// - Returns: True if the word is a common compound suffix
    static func isCommonCompoundSuffix(_ word: String) -> Bool {
        let lowercased = word.lowercased()
        let commonSuffixes: Set<String> = [
            "thing", "one", "body", "where", "time", "times", "what", "day",
            "how", "place", "way", "ways", "more", "to", "on", "in", "out",
            "stairs", "ground", "yard", "walk", "side"
        ]
        return commonSuffixes.contains(lowercased)
    }
}
