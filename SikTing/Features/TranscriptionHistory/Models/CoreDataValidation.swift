//
//  CoreDataValidation.swift
//  SikTing
//
//  Created by <PERSON><PERSON> on 7/19/25.
//

import Foundation
import CoreData

/// Utility class for validating Core Data setup and performing basic operations
class CoreDataValidation {
    
    private let coreDataStack = CoreDataStack.shared
    
    /// Validates that Core Data is properly set up and can perform basic operations
    func validateSetup() -> Bool {
        do {
            // Test 1: Validate model integrity
            guard coreDataStack.validateModelIntegrity() else {
                print("❌ Core Data model validation failed")
                return false
            }
            print("✅ Core Data model validation passed")
            
            // Test 2: Create a test session
            let testSession = HistorySession.create(in: coreDataStack.mainContext)
            testSession.title = "Test Session"
            testSession.contentPreview = "This is a test session for validation"
            
            // Test 3: Create a test entry
            let testEntry = HistoryEntry.create(
                in: coreDataStack.mainContext,
                originalText: "Test transcription entry",
                timestamp: Date(),
                sessionId: testSession.id ?? UUID()
            )
            testEntry.detectedLanguage = "en"
            testEntry.isFinal = true
            
            // Test 4: Add entry to session
            testSession.addEntry(testEntry)
            
            // Test 5: Save context
            coreDataStack.saveMainContext()
            print("✅ Core Data save operation successful")
            
            // Test 6: Fetch the created data
            let fetchRequest: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "title == %@", "Test Session")
            
            let results = try coreDataStack.mainContext.fetch(fetchRequest)
            guard let fetchedSession = results.first else {
                print("❌ Failed to fetch created session")
                return false
            }
            
            // Test 7: Validate relationships
            guard fetchedSession.sortedEntries.count == 1 else {
                print("❌ Session-Entry relationship validation failed")
                return false
            }
            print("✅ Core Data relationships working correctly")
            
            // Test 8: Clean up test data
            coreDataStack.mainContext.delete(fetchedSession)
            coreDataStack.saveMainContext()
            print("✅ Core Data cleanup successful")
            
            return true
            
        } catch {
            print("❌ Core Data validation failed with error: \(error)")
            return false
        }
    }
    
    /// Performs a comprehensive test of Core Data functionality
    func performComprehensiveTest() -> Bool {
        print("🧪 Starting comprehensive Core Data test...")
        
        // Basic setup validation
        guard validateSetup() else {
            return false
        }
        
        // Test session categories
        guard testSessionCategories() else {
            return false
        }
        
        // Test search functionality
        guard testSearchFunctionality() else {
            return false
        }
        
        print("✅ All Core Data tests passed successfully!")
        return true
    }
    
    private func testSessionCategories() -> Bool {
        do {
            let context = coreDataStack.mainContext
            
            // Create test sessions for different categories
            let recentSession = HistorySession.create(in: context)
            recentSession.title = "Recent Session"
            recentSession.createdAt = Date()
            
            let favouriteSession = HistorySession.create(in: context)
            favouriteSession.title = "Favourite Session"
            favouriteSession.isFavourite = true
            
            let savedSession = HistorySession.create(in: context)
            savedSession.title = "Saved Session"
            savedSession.isSaved = true
            
            coreDataStack.saveMainContext()
            
            // Test fetch requests for different categories
            let recentResults = try context.fetch(HistorySession.recentSessionsFetchRequest())
            let favouriteResults = try context.fetch(HistorySession.favouriteSessionsFetchRequest())
            let savedResults = try context.fetch(HistorySession.savedSessionsFetchRequest())
            
            let recentFound = recentResults.contains { $0.title == "Recent Session" }
            let favouriteFound = favouriteResults.contains { $0.title == "Favourite Session" }
            let savedFound = savedResults.contains { $0.title == "Saved Session" }
            
            // Clean up
            context.delete(recentSession)
            context.delete(favouriteSession)
            context.delete(savedSession)
            coreDataStack.saveMainContext()
            
            if recentFound && favouriteFound && savedFound {
                print("✅ Session categories test passed")
                return true
            } else {
                print("❌ Session categories test failed")
                return false
            }
            
        } catch {
            print("❌ Session categories test failed with error: \(error)")
            return false
        }
    }
    
    private func testSearchFunctionality() -> Bool {
        do {
            let context = coreDataStack.mainContext
            
            // Create test session with searchable content
            let searchSession = HistorySession.create(in: context)
            searchSession.title = "Searchable Session"
            searchSession.contentPreview = "This session contains searchable content"
            
            let searchEntry = HistoryEntry.create(
                in: context,
                originalText: "This is a unique search term test",
                timestamp: Date(),
                sessionId: searchSession.id ?? UUID()
            )
            
            searchSession.addEntry(searchEntry)
            coreDataStack.saveMainContext()
            
            // Test search functionality
            let searchRequest = HistorySession.searchFetchRequest(query: "unique search term")
            let searchResults = try context.fetch(searchRequest)
            
            let searchFound = searchResults.contains { $0.title == "Searchable Session" }
            
            // Clean up
            context.delete(searchSession)
            coreDataStack.saveMainContext()
            
            if searchFound {
                print("✅ Search functionality test passed")
                return true
            } else {
                print("❌ Search functionality test failed")
                return false
            }
            
        } catch {
            print("❌ Search functionality test failed with error: \(error)")
            return false
        }
    }
}