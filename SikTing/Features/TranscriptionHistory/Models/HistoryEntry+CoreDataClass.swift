//
//  HistoryEntry+CoreDataClass.swift
//  SikTing
//
//  Created by <PERSON><PERSON> on 7/19/25.
//

import Foundation
import CoreData

@objc(HistoryEntry)
public class HistoryEntry: NSManagedObject {
    
    // MARK: - Computed Properties
    
    /// Returns formatted timestamp for display
    var formattedTimestamp: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .medium
        formatter.dateStyle = .none
        return formatter.string(from: timestamp ?? Date())
    }
    
    /// Returns relative timestamp from session start
    func relativeTimestamp(from startTime: Date) -> String {
        guard let entryTimestamp = timestamp else { return "0:00" }
        let interval = entryTimestamp.timeIntervalSince(startTime)
        let minutes = Int(interval) / 60
        let seconds = Int(interval) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    /// Returns the display text (original or translated based on preference)
    var displayText: String {
        return originalText ?? ""
    }
    
    /// Returns the text to display for translation toggle
    var translationDisplayText: String? {
        return translatedText
    }
    
    /// Checks if this entry has a valid translation
    var hasTranslation: Bool {
        return translatedText != nil && !translatedText!.isEmpty
    }
    
    /// Returns the detected language display name
    var detectedLanguageDisplayName: String {
        guard let language = detectedLanguage else { return "Unknown" }
        
        // Convert language codes to display names
        let locale = Locale.current
        return locale.localizedString(forLanguageCode: language) ?? language
    }
    
    /// Returns the target language display name
    var targetLanguageDisplayName: String? {
        guard let language = targetLanguage else { return nil }
        
        let locale = Locale.current
        return locale.localizedString(forLanguageCode: language) ?? language
    }
    
    /// Returns emotion display text with appropriate formatting
    var emotionDisplayText: String? {
        guard let emotion = emotion, !emotion.isEmpty else { return nil }
        return emotion.capitalized
    }
    
    /// Returns audio type display text
    var audioTypeDisplayText: String? {
        guard let audioType = audioType, !audioType.isEmpty else { return nil }
        return audioType.capitalized
    }
}

// MARK: - Core Data Extensions

extension HistoryEntry {
    
    /// Creates a new HistoryEntry with required values
    static func create(
        in context: NSManagedObjectContext,
        originalText: String,
        timestamp: Date,
        sessionId: UUID
    ) -> HistoryEntry {
        let entry = HistoryEntry(context: context)
        entry.id = UUID()
        entry.originalText = originalText
        entry.timestamp = timestamp
        entry.sessionId = sessionId
        entry.isFinal = false
        return entry
    }
    
    /// Creates a HistoryEntry from an existing TranscriptionEntry
    static func create(
        from transcriptionEntry: TranscriptionEntry,
        in context: NSManagedObjectContext,
        sessionId: UUID
    ) -> HistoryEntry {
        let entry = HistoryEntry(context: context)
        entry.id = UUID()
        // Use processed/clean text from displayText instead of raw text
        entry.originalText = transcriptionEntry.displayText
        entry.timestamp = transcriptionEntry.timestamp
        entry.translatedText = transcriptionEntry.translatedText
        entry.targetLanguage = transcriptionEntry.targetLanguage?.rawValue
        entry.isFinal = transcriptionEntry.isFinal
        entry.sessionId = sessionId

        // Extract additional metadata from parsed content if available
        if let parsedContent = transcriptionEntry.parsedContent {
            entry.detectedLanguage = parsedContent.language.rawValue
            entry.emotion = parsedContent.emotion.rawValue
            entry.audioType = parsedContent.audioType.rawValue
        }

        return entry
    }
    
    /// Updates this entry with data from a TranscriptionEntry
    func update(from transcriptionEntry: TranscriptionEntry) {
        // Use processed/clean text from displayText instead of raw text
        originalText = transcriptionEntry.displayText
        translatedText = transcriptionEntry.translatedText
        targetLanguage = transcriptionEntry.targetLanguage?.rawValue
        isFinal = transcriptionEntry.isFinal

        // Update metadata from parsed content if available
        if let parsedContent = transcriptionEntry.parsedContent {
            detectedLanguage = parsedContent.language.rawValue
            emotion = parsedContent.emotion.rawValue
            audioType = parsedContent.audioType.rawValue
        }
    }
}

// MARK: - Search Support

extension HistoryEntry {
    
    /// Returns searchable text content for this entry
    var searchableContent: String {
        var content = [originalText ?? ""]

        if let translated = translatedText {
            content.append(translated)
        }

        return content.filter { !$0.isEmpty }.joined(separator: " ")
    }
    
    /// Checks if this entry matches a search query
    func matches(searchQuery: String) -> Bool {
        let query = searchQuery.lowercased()
        return searchableContent.lowercased().contains(query)
    }
    
    /// Returns highlighted text for search results
    func highlightedText(for searchQuery: String) -> String {
        guard !searchQuery.isEmpty else { return originalText ?? "" }

        // Simple highlighting - in a real implementation you might want more sophisticated highlighting
        let highlightedText = (originalText ?? "").replacingOccurrences(
            of: searchQuery,
            with: "**\(searchQuery)**",
            options: .caseInsensitive
        )
        
        return highlightedText
    }
    
    /// Returns the word count for this entry
    var wordCount: Int {
        guard let text = originalText else { return 0 }
        return text.components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }.count
    }

    /// Returns character count for this entry
    var characterCount: Int {
        return originalText?.count ?? 0
    }
}