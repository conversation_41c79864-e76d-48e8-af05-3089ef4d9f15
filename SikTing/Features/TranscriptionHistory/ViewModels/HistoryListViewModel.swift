//
//  HistoryListViewModel.swift
//  SikTing
//
//  Created by Augment Agent on 2025-07-21.
//

import Foundation
import SwiftUI
import Combine

/// ViewModel for managing the history list interface and data
class HistoryListViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    /// Currently selected tab in the history interface
    @Published var selectedTab: HistoryTab = .recents
    
    /// All history sessions loaded from storage
    @Published var allSessions: [HistorySession] = []
    
    /// Filtered sessions based on current tab
    @Published var filteredSessions: [HistorySession] = []
    
    /// Loading state for data operations
    @Published var isLoading: Bool = false
    
    /// Error state for displaying alerts
    @Published var errorMessage: String?

    /// Current error for comprehensive error handling
    @Published var error: HistoryError?
    
    /// Search text for filtering sessions
    @Published var searchText: String = ""
    
    /// Whether search is active
    @Published var isSearching: Bool = false

    // MARK: - Performance Properties

    /// Whether there's more data to load
    @Published var hasMoreData = true

    /// Current page for pagination
    @Published var currentPage = 0

    /// Time taken for last load operation
    @Published var loadTime: TimeInterval = 0

    /// Current memory usage formatted string
    @Published var memoryUsage: String = ""

    /// Performance metrics summary
    @Published var performanceMetrics: String = ""

    // MARK: - Computed Properties

    /// Computed property to check if we should show empty state (not during refresh)
    var shouldShowEmptyState: Bool {
        print("📱 HistoryListViewModel: shouldShowEmptyState filteredSessions.isEmpty=\(filteredSessions.isEmpty), paginationService.isRefreshing=\(paginationService.isRefreshing)")
        return filteredSessions.isEmpty && !paginationService.isRefreshing
    }

    // MARK: - Private Properties

    private let historyStorageService: HistoryStorageService
    internal let paginationService: HistoryPaginationService
    private let optimizedFetchService: OptimizedFetchService
    // Memory management service removed for simplicity
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init(historyStorageService: HistoryStorageService = HistoryStorageService()) {
        self.historyStorageService = historyStorageService
        self.paginationService = HistoryPaginationService(storageService: historyStorageService)
        self.optimizedFetchService = OptimizedFetchService()
        setupBindings()
        // Performance monitoring removed for simplicity
        loadSessions()
    }
    
    // MARK: - Setup
    
    private func setupBindings() {
        // Listen to pagination service changes instead of storage service
        paginationService.$sessions
            .receive(on: DispatchQueue.main)
            .sink { [weak self] sessions in
                print("📱 HistoryListViewModel: Pagination service updated with \(sessions.count) sessions")
                if let firstSession = sessions.first {
                    let title = firstSession.title ?? "Untitled"
                    let updatedAt = firstSession.updatedAt?.description ?? "No date"
                    print("📱   First session: '\(title)' updated at \(updatedAt)")
                }
                self?.allSessions = sessions
                print("📱 HistoryListViewModel: allSessions updated to \(self?.allSessions.count ?? 0) sessions")
                self?.filterSessions()
            }
            .store(in: &cancellables)

        // Listen to tab changes
        $selectedTab
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.loadSessions() // Reload sessions when tab changes
            }
            .store(in: &cancellables)

        // Listen to search text changes
        $searchText
            .debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.filterSessions()
            }
            .store(in: &cancellables)

        // Listen to storage service errors
        historyStorageService.$lastError
            .compactMap { $0 }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] error in
                self?.errorMessage = error.localizedDescription
            }
            .store(in: &cancellables)

        // Listen to pagination service errors
        paginationService.$error
            .compactMap { $0 }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] error in
                self?.error = error
                self?.errorMessage = error.localizedDescription
            }
            .store(in: &cancellables)

        // Listen to pagination service refreshing state
        paginationService.$isRefreshing
            .receive(on: DispatchQueue.main)
            .sink { [weak self] refreshing in
                // Don't show empty state during refresh
                if refreshing {
                    print("📱 HistoryListViewModel: Refresh started, suppressing empty state")
                } else {
                    print("📱 HistoryListViewModel: Refresh ended")
                }
            }
            .store(in: &cancellables)

        // Listen for new session notifications to refresh immediately
        NotificationCenter.default.publisher(for: .newSessionSaved)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] notification in
                print("📱 HistoryListViewModel: New session saved notification received")
                if let sessionId = notification.userInfo?["sessionId"] as? UUID {
                    print("📱 HistoryListViewModel: Session ID: \(sessionId)")
                }
                print("📱 HistoryListViewModel: Current sessions count: \(self?.allSessions.count ?? 0)")
                print("📱 HistoryListViewModel: Triggering refresh...")
                self?.refreshSessions()
            }
            .store(in: &cancellables)

        // Listen for session deletion notifications to refresh immediately
        NotificationCenter.default.publisher(for: .sessionDeleted)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] notification in
                print("📱 HistoryListViewModel: Session deleted notification received")
                if let sessionId = notification.userInfo?["sessionId"] as? UUID,
                   let sessionTitle = notification.userInfo?["sessionTitle"] as? String {
                    print("📱 HistoryListViewModel: Deleted session '\(sessionTitle)' with ID: \(sessionId)")
                }
                print("📱 HistoryListViewModel: Current sessions count: \(self?.allSessions.count ?? 0)")
                print("📱 HistoryListViewModel: Triggering refresh after deletion...")
                self?.refreshSessions()
            }
            .store(in: &cancellables)

        // Listen for multiple sessions deletion notifications to refresh immediately
        NotificationCenter.default.publisher(for: .sessionsDeleted)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] notification in
                print("📱 HistoryListViewModel: Multiple sessions deleted notification received")
                if let deletedCount = notification.userInfo?["deletedCount"] as? Int {
                    print("📱 HistoryListViewModel: Deleted \(deletedCount) sessions")
                }
                print("📱 HistoryListViewModel: Current sessions count: \(self?.allSessions.count ?? 0)")
                print("📱 HistoryListViewModel: Triggering refresh after bulk deletion...")
                self?.refreshSessions()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Data Loading
    
    /// Loads sessions from storage with pagination
    func loadSessions() {
        Task {
            await loadInitialSessions()
        }
    }

    /// Loads initial sessions asynchronously with performance optimization
    @MainActor
    private func loadInitialSessions() async {
        isLoading = true
        error = nil
        errorMessage = nil

        let startTime = CFAbsoluteTimeGetCurrent()

        do {
            // Use pagination service for optimized loading
            await paginationService.loadInitialSessions(for: selectedTab)

            // Update pagination state (allSessions will be updated via binding)
            hasMoreData = paginationService.hasMoreData
            currentPage = paginationService.currentPage

            loadTime = CFAbsoluteTimeGetCurrent() - startTime
            // Performance metrics update removed

        } catch {
            handleError(error, context: .loading)
        }

        isLoading = false
    }

    /// Loads next page of sessions
    @MainActor
    func loadNextPage() async {
        guard hasMoreData && !isLoading else { return }

        await paginationService.loadNextPage(for: selectedTab)

        // Update pagination state (allSessions will be updated via binding)
        hasMoreData = paginationService.hasMoreData
        currentPage = paginationService.currentPage

        // Performance metrics update removed
    }

    /// Checks if we need to preload more data
    func checkForPreload(at index: Int) {
        Task {
            await paginationService.preloadIfNeeded(currentIndex: index, tab: selectedTab)
            // allSessions will be updated automatically via binding
        }
    }

    /// Refreshes the session list
    func refreshSessions() {
        Task {
            await paginationService.refresh(for: selectedTab)
            await MainActor.run {
                hasMoreData = paginationService.hasMoreData
                currentPage = paginationService.currentPage
                // allSessions will be updated automatically via binding
                // Performance metrics update removed
            }
        }
    }

    /// Retry loading after an error
    func retryLoading() {
        loadSessions()
    }

    /// Handle errors with appropriate user feedback
    private func handleError(_ error: Error, context: ErrorContext) {
        DispatchQueue.main.async {
            self.isLoading = false

            if let historyError = error as? HistoryError {
                self.error = historyError
                self.errorMessage = historyError.description
            } else {
                self.error = .unknown(error.localizedDescription)
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    // MARK: - Filtering
    
    private func filterSessions() {
        print("📱 HistoryListViewModel: filterSessions() called with \(allSessions.count) allSessions")
        print("📱   selectedTab: \(selectedTab), searchText: '\(searchText)'")

        var sessions = allSessions

        // Filter by tab
        switch selectedTab {
        case .recents:
            // Show all sessions in recents - let the user see everything by default
            // The sorting will put the most recent ones at the top
            break // Don't filter anything for recents
        case .saved:
            sessions = sessions.filter { $0.isSaved }
        case .favorites:
            sessions = sessions.filter { $0.isFavourite }
        }

        print("📱   After tab filtering (\(selectedTab)): \(sessions.count) sessions")

        // Filter by search text
        if !searchText.isEmpty {
            sessions = sessions.filter { session in
                // Search in title and content preview
                let titleMatch = session.title?.localizedCaseInsensitiveContains(searchText) ?? false
                let contentMatch = session.contentPreview?.localizedCaseInsensitiveContains(searchText) ?? false
                return titleMatch || contentMatch
            }
            print("📱   After search filtering: \(sessions.count) sessions")
        }

        // Sort by created time only (not updated time) to prevent items moving when favorited
        sessions.sort { session1, session2 in
            let date1 = session1.createdAt ?? Date.distantPast
            let date2 = session2.createdAt ?? Date.distantPast
            return date1 > date2
        }

        print("📱   Final filteredSessions count: \(sessions.count)")
        if let firstSession = sessions.first {
            let title = firstSession.title ?? "Untitled"
            let updatedAt = firstSession.updatedAt?.description ?? "No date"
            print("📱   First filtered session: '\(title)' updated at \(updatedAt)")
        }

        filteredSessions = sessions
        print("📱 HistoryListViewModel: filteredSessions updated to \(filteredSessions.count) sessions")
    }
    
    // MARK: - Tab Management

    /// Changes the selected tab
    func selectTab(_ tab: HistoryTab) {
        selectedTab = tab
    }


    
    // MARK: - Session Actions
    
    /// Toggles favorite status for a session
    func toggleFavorite(for session: HistorySession) {
        do {
            historyStorageService.markSessionAsFavorite(session, isFavorite: !session.isFavourite)
        } catch {
            handleError(error, context: .favoriting)
        }
    }

    /// Toggles saved status for a session
    func toggleSaved(for session: HistorySession) {
        do {
            historyStorageService.markSessionAsSaved(session, isSaved: !session.isSaved)
        } catch {
            handleError(error, context: .saving)
        }
    }

    /// Updates session title
    func updateTitle(for session: HistorySession, newTitle: String) {
        do {
            historyStorageService.updateSessionTitle(session, title: newTitle)
        } catch {
            handleError(error, context: .saving)
        }
    }

    /// Deletes a session
    func deleteSession(_ session: HistorySession) {
        print("📱 HistoryListViewModel: Deleting session '\(session.displayTitle)'")
        do {
            historyStorageService.deleteSession(session)
            // Note: The UI refresh will be triggered by the .sessionDeleted notification
            // that is posted by HistoryStorageService after successful deletion
        } catch {
            print("📱 HistoryListViewModel: Failed to delete session - \(error)")
            handleError(error, context: .deleting)
        }
    }

    /// Deletes multiple sessions
    func deleteSessions(_ sessions: [HistorySession]) {
        print("📱 HistoryListViewModel: Deleting \(sessions.count) sessions")
        do {
            historyStorageService.deleteSessions(sessions)
            // Note: The UI refresh will be triggered by the .sessionsDeleted notification
            // that is posted by HistoryStorageService after successful deletion
        } catch {
            print("📱 HistoryListViewModel: Failed to delete sessions - \(error)")
            handleError(error, context: .deleting)
        }
    }
    
    // MARK: - Search
    
    /// Starts search mode
    func startSearch() {
        isSearching = true
    }
    
    /// Ends search mode
    func endSearch() {
        isSearching = false
        searchText = ""
    }



    // MARK: - Performance Monitoring

    // Performance monitoring and memory management removed for simplicity

    // MARK: - Error Handling

    /// Clears the current error message
    func clearError() {
        errorMessage = nil
    }
}

// MARK: - History Tab Enum

enum HistoryTab: String, CaseIterable {
    case recents = "Recents"
    case saved = "Saved"
    case favorites = "Favorites"
    
    var displayName: String {
        return rawValue
    }
    
    var systemImage: String {
        switch self {
        case .recents:
            return "clock"
        case .saved:
            return "bookmark"
        case .favorites:
            return "heart"
        }
    }
}
