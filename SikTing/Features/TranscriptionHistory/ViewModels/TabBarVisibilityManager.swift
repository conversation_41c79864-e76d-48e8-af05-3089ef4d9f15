//
//  TabBarVisibilityManager.swift
//  SikTing
//
//  Created by Augment Agent on 2025/1/25.
//

import SwiftUI
import UIKit

/// Manages intelligent show/hide logic for the floating tab bar
/// Handles scroll direction detection, timeout functionality, and accessibility settings
class TabBarVisibilityManager: ObservableObject {
    
    // MARK: - Published Properties
    
    /// Controls whether the floating tab bar is visible
    @Published var isVisible: Bool = true
    
    // MARK: - Private Properties

    private var lastScrollData: ScrollOffsetData?
    private var hideTimer: Timer?
    private var currentVelocity: CGFloat = 0
    private var safeAreaTop: CGFloat = 0
    
    // MARK: - Configuration Constants
    
    /// Minimum scroll distance to trigger hide/show behavior
    private let hideThreshold: CGFloat = 50
    
    /// Minimum scroll velocity to trigger immediate response
    private let velocityThreshold: CGFloat = 5
    
    /// Time interval after which tab bar auto-shows when scrolling stops
    private let showTimeout: TimeInterval = 2.0
    
    /// Debounce interval to prevent excessive updates
    private let debounceInterval: TimeInterval = 0.1
    
    // MARK: - Public Methods
    
    /// Handles scroll position changes and determines tab bar visibility
    /// - Parameter scrollData: Current scroll data including offset, safe area, and timestamp
    func handleScrollChange(_ scrollData: ScrollOffsetData) {
        // Update safe area information
        safeAreaTop = scrollData.safeAreaTop

        // Calculate velocity if we have previous data
        if let lastData = lastScrollData {
            currentVelocity = scrollData.velocity(from: lastData)
        }

        lastScrollData = scrollData

        // Always show at top of content (accounting for safe area)
        if scrollData.offset <= safeAreaTop {
            showTabBar()
            return
        }

        // Check if scroll movement is significant enough
        guard abs(currentVelocity) > velocityThreshold else {
            resetHideTimer()
            return
        }

        // Determine visibility based on scroll direction
        if currentVelocity > 0 && isVisible {
            // Scrolling down - hide tab bar
            hideTabBar()
        } else if currentVelocity < 0 && !isVisible {
            // Scrolling up - show tab bar
            showTabBar()
        }

        // Reset the auto-show timer
        resetHideTimer()
    }

    /// Legacy method for backward compatibility
    /// - Parameter scrollOffset: Current scroll offset from the top
    func handleScrollChange(_ scrollOffset: CGFloat) {
        let scrollData = ScrollOffsetData(
            offset: scrollOffset,
            safeAreaTop: safeAreaTop,
            timestamp: Date()
        )
        handleScrollChange(scrollData)
    }
    
    /// Handles tap gestures to show hidden tab bar
    func handleTapGesture() {
        if !isVisible {
            showTabBar()
        }
    }
    
    /// Forces the tab bar to be visible (useful for navigation events)
    func forceShow() {
        showTabBar()
        hideTimer?.invalidate()
    }
    
    /// Forces the tab bar to be hidden (useful for modal presentations)
    func forceHide() {
        hideTimer?.invalidate()
        hideTabBar()
    }
    
    /// Resets the visibility manager to initial state
    func reset() {
        hideTimer?.invalidate()
        lastScrollData = nil
        currentVelocity = 0
        safeAreaTop = 0
        showTabBar()
    }
    
    // MARK: - Private Methods
    
    /// Shows the floating tab bar with animation
    private func showTabBar() {
        guard !isVisible else { return }
        
        withAnimation(DesignSystem.animations.standard) {
            isVisible = true
        }
        
        resetHideTimer()
    }
    
    /// Hides the floating tab bar with animation
    /// Respects accessibility settings for reduced motion
    private func hideTabBar() {
        guard isVisible else { return }
        
        // Respect reduced motion accessibility setting
        if UIAccessibility.isReduceMotionEnabled {
            return
        }
        
        withAnimation(DesignSystem.animations.standard) {
            isVisible = false
        }
    }
    
    /// Resets the auto-show timer
    /// Tab bar will automatically show after timeout period when scrolling stops
    private func resetHideTimer() {
        hideTimer?.invalidate()
        hideTimer = Timer.scheduledTimer(withTimeInterval: showTimeout, repeats: false) { [weak self] _ in
            DispatchQueue.main.async {
                self?.showTabBar()
            }
        }
    }
    
    // MARK: - Cleanup
    
    deinit {
        hideTimer?.invalidate()
    }
}

// MARK: - Scroll Detection Helper

/// Enhanced scroll offset reader with velocity tracking and safe area handling
struct ScrollOffsetReader: View {
    let onOffsetChange: (ScrollOffsetData) -> Void

    var body: some View {
        GeometryReader { geometry in
            Color.clear
                .preference(
                    key: ScrollOffsetPreferenceKey.self,
                    value: ScrollOffsetData(
                        offset: -geometry.frame(in: .named("scroll")).minY,
                        safeAreaTop: geometry.safeAreaInsets.top,
                        timestamp: Date()
                    )
                )
        }
        .onPreferenceChange(ScrollOffsetPreferenceKey.self) { data in
            onOffsetChange(data)
        }
    }
}

/// Data structure for scroll offset information
struct ScrollOffsetData: Equatable {
    let offset: CGFloat
    let safeAreaTop: CGFloat
    let timestamp: Date

    /// Calculates velocity based on previous data point
    func velocity(from previous: ScrollOffsetData) -> CGFloat {
        let timeDelta = timestamp.timeIntervalSince(previous.timestamp)
        guard timeDelta > 0 else { return 0 }

        let offsetDelta = offset - previous.offset
        return offsetDelta / timeDelta
    }

    /// Equatable conformance - compare only offset and safeAreaTop for performance
    static func == (lhs: ScrollOffsetData, rhs: ScrollOffsetData) -> Bool {
        return abs(lhs.offset - rhs.offset) < 0.1 &&
               abs(lhs.safeAreaTop - rhs.safeAreaTop) < 0.1
    }
}

/// Preference key for tracking scroll offset data
private struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: ScrollOffsetData = ScrollOffsetData(
        offset: 0,
        safeAreaTop: 0,
        timestamp: Date()
    )

    static func reduce(value: inout ScrollOffsetData, nextValue: () -> ScrollOffsetData) {
        value = nextValue()
    }
}
