//
//  OptimizedFetchService.swift
//  SikTing
//
//  Created by Augment Agent on 2025-07-21.
//

import Foundation
import CoreData
import Combine
import UIKit

/// Service for optimized Core Data fetch operations
class OptimizedFetchService: NSObject, ObservableObject {
    
    // MARK: - Properties
    
    private let context: NSManagedObjectContext
    private var fetchedResultsController: NSFetchedResultsController<HistorySession>?
    private var cancellables = Set<AnyCancellable>()
    
    @Published var sessions: [HistorySession] = []
    @Published var isLoading = false
    @Published var error: HistoryError?
    
    // MARK: - Performance Tracking
    
    @Published var fetchTime: TimeInterval = 0
    @Published var cacheHitRate: Double = 0
    private var totalFetches = 0
    private var cacheHits = 0
    
    // MARK: - Initialization
    
    init(context: NSManagedObjectContext = CoreDataStack.shared.mainContext) {
        self.context = context
        super.init()
        setupNotifications()
    }
    
    // MARK: - Optimized Fetch Methods
    
    /// Fetches sessions with optimized predicates and sorting
    func fetchSessions(
        tab: HistoryTab,
        searchText: String = "",
        limit: Int = 50,
        offset: Int = 0
    ) async throws -> [HistorySession] {
        
        let startTime = CFAbsoluteTimeGetCurrent()
        totalFetches += 1
        
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    let request = self.createOptimizedFetchRequest(
                        tab: tab,
                        searchText: searchText,
                        limit: limit,
                        offset: offset
                    )
                    
                    let sessions = try self.context.fetch(request)
                    
                    self.fetchTime = CFAbsoluteTimeGetCurrent() - startTime
                    
                    DispatchQueue.main.async {
                        print("⚡ OptimizedFetchService: Fetched \(sessions.count) sessions in \(String(format: "%.3f", self.fetchTime))s")
                    }
                    
                    continuation.resume(returning: sessions)
                    
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    /// Creates optimized fetch request with proper predicates and performance settings
    private func createOptimizedFetchRequest(
        tab: HistoryTab,
        searchText: String,
        limit: Int,
        offset: Int
    ) -> NSFetchRequest<HistorySession> {
        
        let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
        
        // Performance optimizations
        request.fetchLimit = limit
        request.fetchOffset = offset
        request.returnsObjectsAsFaults = false
        request.includesSubentities = false
        request.includesPropertyValues = true
        request.returnsDistinctResults = true
        
        // Relationship prefetching for better performance
        request.relationshipKeyPathsForPrefetching = ["entries"]
        
        // Only fetch necessary properties
        request.propertiesToFetch = [
            "id", "title", "createdAt", "updatedAt",
            "isFavourite", "isSaved", "language", "duration"
        ]
        
        // Build compound predicate
        var predicates: [NSPredicate] = []
        
        // Tab-specific predicates
        switch tab {
        case .recents:
            // No additional predicate for recents
            break
        case .favorites:
            predicates.append(NSPredicate(format: "isFavourite == YES"))
        case .saved:
            predicates.append(NSPredicate(format: "isSaved == YES"))
        }
        
        // Search predicate
        if !searchText.isEmpty {
            let searchPredicate = NSPredicate(
                format: "title CONTAINS[cd] %@ OR ANY entries.text CONTAINS[cd] %@",
                searchText, searchText
            )
            predicates.append(searchPredicate)
        }
        
        // Combine predicates
        if !predicates.isEmpty {
            request.predicate = NSCompoundPredicate(andPredicateWithSubpredicates: predicates)
        }
        
        // Optimized sorting
        request.sortDescriptors = createOptimizedSortDescriptors(for: tab)
        
        return request
    }
    
    /// Creates optimized sort descriptors
    private func createOptimizedSortDescriptors(for tab: HistoryTab) -> [NSSortDescriptor] {
        switch tab {
        case .recents:
            // Sort by updatedAt so newly saved sessions appear at the top
            return [
                NSSortDescriptor(keyPath: \HistorySession.updatedAt, ascending: false)
            ]
        case .favorites:
            return [
                NSSortDescriptor(keyPath: \HistorySession.isFavourite, ascending: false),
                NSSortDescriptor(keyPath: \HistorySession.updatedAt, ascending: false)
            ]
        case .saved:
            return [
                NSSortDescriptor(keyPath: \HistorySession.isSaved, ascending: false),
                NSSortDescriptor(keyPath: \HistorySession.updatedAt, ascending: false)
            ]
        }
    }
    
    // MARK: - Fetched Results Controller
    
    /// Sets up NSFetchedResultsController for automatic updates
    func setupFetchedResultsController(for tab: HistoryTab, searchText: String = "") {
        let request = createOptimizedFetchRequest(
            tab: tab,
            searchText: searchText,
            limit: 0, // No limit for FRC
            offset: 0
        )
        
        fetchedResultsController = NSFetchedResultsController(
            fetchRequest: request,
            managedObjectContext: context,
            sectionNameKeyPath: nil,
            cacheName: "HistoryCache_\(tab.rawValue)"
        )
        
        fetchedResultsController?.delegate = self
        
        do {
            try fetchedResultsController?.performFetch()
            sessions = fetchedResultsController?.fetchedObjects ?? []
            cacheHits += 1
            updateCacheHitRate()
            
        } catch {
            self.error = .storageFailure("Failed to fetch optimized data")
            print("❌ OptimizedFetchService: Failed to setup FRC - \(error)")
        }
    }
    
    // MARK: - Cache Management
    
    /// Clears Core Data cache
    func clearCache() {
        NSFetchedResultsController<HistorySession>.deleteCache(withName: nil)
        cacheHits = 0
        totalFetches = 0
        cacheHitRate = 0
        
        print("🧹 OptimizedFetchService: Cache cleared")
    }
    
    private func updateCacheHitRate() {
        if totalFetches > 0 {
            cacheHitRate = Double(cacheHits) / Double(totalFetches)
        }
    }
    
    // MARK: - Batch Operations
    
    /// Performs batch fetch for multiple sessions
    func batchFetchSessions(objectIDs: [NSManagedObjectID]) async throws -> [HistorySession] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                let sessions = objectIDs.compactMap { objectID in
                    try? self.context.existingObject(with: objectID) as? HistorySession
                }

                continuation.resume(returning: sessions)
            }
        }
    }
    
    /// Batch delete sessions efficiently
    func batchDeleteSessions(predicate: NSPredicate) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            let backgroundContext = CoreDataStack.shared.backgroundContext
            
            backgroundContext.perform {
                do {
                    let batchDeleteRequest = NSBatchDeleteRequest(
                        fetchRequest: HistorySession.fetchRequest()
                    )
                    batchDeleteRequest.resultType = .resultTypeObjectIDs
                    batchDeleteRequest.affectedStores = backgroundContext.persistentStoreCoordinator?.persistentStores
                    
                    let result = try backgroundContext.execute(batchDeleteRequest) as? NSBatchDeleteResult
                    let objectIDs = result?.result as? [NSManagedObjectID] ?? []
                    
                    // Merge changes to main context
                    let changes = [NSDeletedObjectsKey: objectIDs]
                    NSManagedObjectContext.mergeChanges(fromRemoteContextSave: changes, into: [self.context])
                    
                    continuation.resume()
                    
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    // MARK: - Memory Management
    
    /// Refreshes objects to free memory
    func refreshObjects() {
        context.refreshAllObjects()
        print("🔄 OptimizedFetchService: Refreshed all objects")
    }
    
    /// Resets context to free memory
    func resetContext() {
        context.reset()
        sessions.removeAll()
        print("🔄 OptimizedFetchService: Context reset")
    }
    
    // MARK: - Notifications
    
    private func setupNotifications() {
        NotificationCenter.default.publisher(for: .NSManagedObjectContextDidSave)
            .sink { [weak self] notification in
                self?.handleContextDidSave(notification)
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)
            .sink { [weak self] _ in
                self?.handleMemoryWarning()
            }
            .store(in: &cancellables)
    }
    
    private func handleContextDidSave(_ notification: Notification) {
        guard let context = notification.object as? NSManagedObjectContext,
              context != self.context else { return }
        
        self.context.mergeChanges(fromContextDidSave: notification)
    }
    
    private func handleMemoryWarning() {
        refreshObjects()
        clearCache()
        print("⚠️ OptimizedFetchService: Handled memory warning")
    }
}

// MARK: - NSFetchedResultsControllerDelegate

extension OptimizedFetchService: NSFetchedResultsControllerDelegate {
    
    func controllerDidChangeContent(_ controller: NSFetchedResultsController<NSFetchRequestResult>) {
        sessions = fetchedResultsController?.fetchedObjects ?? []
    }
    
    func controller(
        _ controller: NSFetchedResultsController<NSFetchRequestResult>,
        didChange anObject: Any,
        at indexPath: IndexPath?,
        for type: NSFetchedResultsChangeType,
        newIndexPath: IndexPath?
    ) {
        // Handle individual object changes if needed
        DispatchQueue.main.async {
            self.sessions = self.fetchedResultsController?.fetchedObjects ?? []
        }
    }
}
