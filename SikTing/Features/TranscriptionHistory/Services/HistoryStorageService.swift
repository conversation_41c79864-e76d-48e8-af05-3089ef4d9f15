//
//  HistoryStorageService.swift
//  SikTing
//
//  Created by Augment Agent on 2025-07-21.
//

import Foundation
import CoreData
import Combine

/// Service responsible for managing transcription history data persistence
/// Handles Core Data operations for HistorySession and HistoryEntry entities
class HistoryStorageService: ObservableObject {

    // MARK: - Properties

    private let coreDataStack: CoreDataStack

    /// Public accessor for Core Data stack (needed for search operations)
    var mainContext: NSManagedObjectContext {
        return coreDataStack.mainContext
    }

    /// Published property to notify UI of data changes
    @Published var sessions: [HistorySession] = []

    /// Published property for error handling
    @Published var lastError: Error?

    // MARK: - Initialization

    init(coreDataStack: CoreDataStack = CoreDataStack.shared) {
        self.coreDataStack = coreDataStack
        loadSessions()
    }

    // MARK: - Session Management

    /// Creates a new history session from transcription entries
    /// - Parameters:
    ///   - transcriptionEntries: Array of TranscriptionEntry objects to save
    ///   - startTime: When the recording session started
    /// - Returns: The created HistorySession or nil if creation failed
    @discardableResult
    func createSession(from transcriptionEntries: [TranscriptionEntry], startTime: Date) -> HistorySession? {
        let context = coreDataStack.backgroundContext

        return context.performAndWait {
            do {
                // Create new session
                let session = HistorySession.create(in: context)
                session.createdAt = startTime
                session.updatedAt = Date()

                // Create history entries from transcription entries
                var historyEntries: [HistoryEntry] = []
                for transcriptionEntry in transcriptionEntries {
                    let historyEntry = HistoryEntry.create(
                        from: transcriptionEntry,
                        in: context,
                        sessionId: session.id ?? UUID()
                    )
                    historyEntries.append(historyEntry)
                }

                // Update session metadata
                session.entryCount = Int32(historyEntries.count)
                session.updateMetadata()

                // Auto-generate title if empty
                if session.title?.isEmpty ?? true {
                    session.title = session.generateTitle()
                }

                // Save context
                try context.save()

                // Ensure proper Core Data synchronization
                DispatchQueue.main.async {
                    // First, save main context to ensure changes are persisted to disk
                    self.coreDataStack.saveMainContext()

                    // Reload our own sessions to verify the save worked
                    self.loadSessions()

                    // Use a more robust synchronization approach
                    self.ensureContextSynchronization(for: session) {
                        print("📤 HistoryStorageService: Core Data synchronized, posting notification")

                        // Notify that a new session was saved
                        NotificationCenter.default.post(
                            name: .newSessionSaved,
                            object: session,
                            userInfo: ["sessionId": session.id ?? UUID()]
                        )
                    }
                }

                print("✅ HistoryStorageService: Created session with \(historyEntries.count) entries")
                return session

            } catch {
                print("❌ HistoryStorageService: Failed to create session: \(error)")
                DispatchQueue.main.async {
                    self.lastError = error
                }
                return nil
            }
        }
    }

    /// Saves a transcription session automatically when recording stops
    /// - Parameters:
    ///   - transcriptionEntries: Current transcription entries
    ///   - startTime: When the recording started
    ///   - shouldAutoSave: Whether to automatically mark as saved
    func saveCurrentSession(
        transcriptionEntries: [TranscriptionEntry],
        startTime: Date,
        shouldAutoSave: Bool = false
    ) {
        // Save all provided entries (ViewModel has already filtered them)
        guard !transcriptionEntries.isEmpty else {
            print("⚠️ HistoryStorageService: No entries to save")
            return
        }

        if let session = createSession(from: transcriptionEntries, startTime: startTime) {
            if shouldAutoSave {
                markSessionAsSaved(session)
            }
            print("✅ HistoryStorageService: Automatically saved session with \(transcriptionEntries.count) entries")
        }
    }

    // MARK: - Data Fetching

    /// Loads all sessions from Core Data
    private func loadSessions() {
        let context = coreDataStack.mainContext
        let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
        request.sortDescriptors = [
            NSSortDescriptor(keyPath: \HistorySession.updatedAt, ascending: false)
        ]

        do {
            sessions = try context.fetch(request)
        } catch {
            print("❌ HistoryStorageService: Failed to load sessions: \(error)")
            lastError = error
            sessions = []
        }
    }

    /// Fetches sessions with specific criteria
    /// - Parameters:
    ///   - limit: Maximum number of sessions to return
    ///   - onlyFavorites: Whether to return only favorite sessions
    ///   - onlySaved: Whether to return only saved sessions
    /// - Returns: Array of matching sessions
    func fetchSessions(limit: Int? = nil, onlyFavorites: Bool = false, onlySaved: Bool = false) -> [HistorySession] {
        let context = coreDataStack.mainContext
        let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()

        // Build predicate
        var predicates: [NSPredicate] = []
        if onlyFavorites {
            predicates.append(NSPredicate(format: "isFavourite == YES"))
        }
        if onlySaved {
            predicates.append(NSPredicate(format: "isSaved == YES"))
        }

        if !predicates.isEmpty {
            request.predicate = NSCompoundPredicate(andPredicateWithSubpredicates: predicates)
        }

        // Sort by most recent
        request.sortDescriptors = [
            NSSortDescriptor(keyPath: \HistorySession.updatedAt, ascending: false)
        ]

        if let limit = limit {
            request.fetchLimit = limit
        }

        do {
            return try context.fetch(request)
        } catch {
            print("❌ HistoryStorageService: Failed to fetch sessions: \(error)")
            lastError = error
            return []
        }
    }

    /// Fetches recent sessions (last 30 days)
    func fetchRecentSessions(limit: Int = 50) -> [HistorySession] {
        let context = coreDataStack.mainContext
        let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()

        // Filter for last 30 days
        let thirtyDaysAgo = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        request.predicate = NSPredicate(format: "createdAt >= %@", thirtyDaysAgo as NSDate)

        request.sortDescriptors = [
            NSSortDescriptor(keyPath: \HistorySession.updatedAt, ascending: false)
        ]
        request.fetchLimit = limit

        do {
            return try context.fetch(request)
        } catch {
            print("❌ HistoryStorageService: Failed to fetch recent sessions: \(error)")
            lastError = error
            return []
        }
    }

    // MARK: - Session Operations

    /// Marks a session as favorite
    func markSessionAsFavorite(_ session: HistorySession, isFavorite: Bool = true) {
        let context = coreDataStack.mainContext
        session.isFavourite = isFavorite
        session.updatedAt = Date()

        coreDataStack.saveMainContext()
        loadSessions()

        print("✅ HistoryStorageService: Session \(isFavorite ? "added to" : "removed from") favorites")
    }

    /// Marks a session as saved
    func markSessionAsSaved(_ session: HistorySession, isSaved: Bool = true) {
        let context = coreDataStack.mainContext
        session.isSaved = isSaved
        session.updatedAt = Date()

        coreDataStack.saveMainContext()
        loadSessions()

        print("✅ HistoryStorageService: Session marked as \(isSaved ? "saved" : "unsaved")")
    }

    /// Updates session title
    func updateSessionTitle(_ session: HistorySession, title: String) {
        let context = coreDataStack.mainContext
        session.title = title
        session.updatedAt = Date()

        coreDataStack.saveMainContext()
        loadSessions()

        print("✅ HistoryStorageService: Updated session title to: \(title)")
    }

    // MARK: - Deletion Operations

    /// Deletes a session and all its entries
    func deleteSession(_ session: HistorySession) {
        let context = coreDataStack.mainContext
        let sessionId = session.id ?? UUID()
        let sessionTitle = session.displayTitle

        // Delete all associated entries first
        let entries = session.sortedEntries
        for entry in entries {
            context.delete(entry)
        }

        // Delete the session
        context.delete(session)

        coreDataStack.saveMainContext()
        loadSessions()

        // Post notification that a session was deleted
        DispatchQueue.main.async {
            NotificationCenter.default.post(
                name: .sessionDeleted,
                object: nil,
                userInfo: [
                    "sessionId": sessionId,
                    "sessionTitle": sessionTitle
                ]
            )
        }

        print("✅ HistoryStorageService: Deleted session '\(sessionTitle)' and \(entries.count) entries")
    }

    /// Deletes multiple sessions
    func deleteSessions(_ sessions: [HistorySession]) {
        let context = coreDataStack.mainContext
        let sessionIds = sessions.compactMap { $0.id }
        let sessionTitles = sessions.map { $0.displayTitle }

        var totalEntriesDeleted = 0
        for session in sessions {
            let entries = session.sortedEntries
            totalEntriesDeleted += entries.count

            for entry in entries {
                context.delete(entry)
            }
            context.delete(session)
        }

        coreDataStack.saveMainContext()
        loadSessions()

        // Post notification that sessions were deleted
        DispatchQueue.main.async {
            NotificationCenter.default.post(
                name: .sessionsDeleted,
                object: nil,
                userInfo: [
                    "sessionIds": sessionIds,
                    "sessionTitles": sessionTitles,
                    "deletedCount": sessions.count
                ]
            )
        }

        print("✅ HistoryStorageService: Deleted \(sessions.count) sessions and \(totalEntriesDeleted) entries")
    }

    /// Get count of new sessions (created in last 24 hours and not viewed)
    func getNewSessionsCount() -> Int {
        let calendar = Calendar.current
        let yesterday = calendar.date(byAdding: .day, value: -1, to: Date()) ?? Date()

        return sessions.filter { session in
            guard let createdAt = session.createdAt else { return false }
            return createdAt > yesterday && !session.hasBeenViewed
        }.count
    }

    /// Mark session as viewed
    func markSessionAsViewed(_ session: HistorySession) {
        session.hasBeenViewed = true
        session.updatedAt = Date()
        coreDataStack.saveMainContext()
    }

    /// Deletes old sessions (older than specified days)
    func deleteOldSessions(olderThanDays days: Int) {
        let context = coreDataStack.mainContext
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()

        let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
        request.predicate = NSPredicate(format: "createdAt < %@ AND isSaved == NO AND isFavourite == NO", cutoffDate as NSDate)

        do {
            let oldSessions = try context.fetch(request)
            deleteSessions(oldSessions)
            print("✅ HistoryStorageService: Deleted \(oldSessions.count) old sessions")
        } catch {
            print("❌ HistoryStorageService: Failed to delete old sessions: \(error)")
            lastError = error
        }
    }

    // MARK: - Data Validation

    /// Validates session data before saving
    private func validateSession(_ session: HistorySession) -> Bool {
        guard let sessionId = session.id else {
            print("❌ HistoryStorageService: Session missing ID")
            return false
        }

        guard let createdAt = session.createdAt else {
            print("❌ HistoryStorageService: Session missing creation date")
            return false
        }

        guard session.entryCount > 0 else {
            print("❌ HistoryStorageService: Session has no entries")
            return false
        }

        return true
    }

    /// Validates entry data before saving
    private func validateEntry(_ entry: HistoryEntry) -> Bool {
        guard let entryId = entry.id else {
            print("❌ HistoryStorageService: Entry missing ID")
            return false
        }

        guard let sessionId = entry.sessionId else {
            print("❌ HistoryStorageService: Entry missing session ID")
            return false
        }

        guard let originalText = entry.originalText, !originalText.isEmpty else {
            print("❌ HistoryStorageService: Entry missing original text")
            return false
        }

        guard let timestamp = entry.timestamp else {
            print("❌ HistoryStorageService: Entry missing timestamp")
            return false
        }

        return true
    }

    // MARK: - Statistics

    /// Returns storage statistics
    func getStorageStatistics() -> (sessionCount: Int, entryCount: Int, totalSize: String) {
        let context = coreDataStack.mainContext

        // Count sessions
        let sessionRequest: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
        let sessionCount = (try? context.count(for: sessionRequest)) ?? 0

        // Count entries
        let entryRequest: NSFetchRequest<HistoryEntry> = HistoryEntry.fetchRequest()
        let entryCount = (try? context.count(for: entryRequest)) ?? 0

        // Estimate size (rough calculation)
        let estimatedSize = (sessionCount * 1024) + (entryCount * 512) // Rough estimate in bytes
        let sizeString = ByteCountFormatter.string(fromByteCount: Int64(estimatedSize), countStyle: .file)

        return (sessionCount: sessionCount, entryCount: entryCount, totalSize: sizeString)
    }

    // MARK: - Core Data Synchronization

    /// Ensures Core Data context synchronization before posting notifications
    /// This method verifies that the saved session is actually visible in the main context
    private func ensureContextSynchronization(for session: HistorySession, completion: @escaping () -> Void) {
        guard let sessionId = session.id else {
            print("⚠️ HistoryStorageService: Session has no ID, posting notification immediately")
            completion()
            return
        }

        // Attempt to verify the session exists in main context with retries
        verifySessionExists(sessionId: sessionId, attempt: 1, maxAttempts: 5, completion: completion)
    }

    /// Recursively verifies that a session exists in the main context
    private func verifySessionExists(sessionId: UUID, attempt: Int, maxAttempts: Int, completion: @escaping () -> Void) {
        let context = coreDataStack.mainContext

        context.perform {
            let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
            request.predicate = NSPredicate(format: "id == %@", sessionId as CVarArg)
            request.fetchLimit = 1

            do {
                let results = try context.fetch(request)

                if !results.isEmpty {
                    // Session found in main context, safe to post notification
                    DispatchQueue.main.async {
                        print("✅ HistoryStorageService: Session verified in main context after \(attempt) attempt(s)")
                        completion()
                    }
                } else if attempt < maxAttempts {
                    // Session not found, retry after a delay
                    let delay = Double(attempt) * 0.1 // Increasing delay: 0.1s, 0.2s, 0.3s, etc.
                    print("🔄 HistoryStorageService: Session not found in main context, retrying in \(delay)s (attempt \(attempt)/\(maxAttempts))")

                    DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                        self.verifySessionExists(sessionId: sessionId, attempt: attempt + 1, maxAttempts: maxAttempts, completion: completion)
                    }
                } else {
                    // Max attempts reached, post notification anyway
                    DispatchQueue.main.async {
                        print("⚠️ HistoryStorageService: Session not found after \(maxAttempts) attempts, posting notification anyway")
                        completion()
                    }
                }
            } catch {
                // Error occurred, post notification anyway
                DispatchQueue.main.async {
                    print("❌ HistoryStorageService: Error verifying session: \(error), posting notification anyway")
                    completion()
                }
            }
        }
    }
}

// MARK: - Error Handling

extension HistoryStorageService {

    /// Handles Core Data errors gracefully
    private func handleError(_ error: Error, operation: String) {
        print("❌ HistoryStorageService: \(operation) failed: \(error)")

        DispatchQueue.main.async {
            self.lastError = error
        }

        // Post notification for global error handling
        NotificationCenter.default.post(
            name: .historyStorageError,
            object: nil,
            userInfo: ["error": error, "operation": operation]
        )
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let historyStorageError = Notification.Name("HistoryStorageError")
    static let newSessionSaved = Notification.Name("NewSessionSaved")
    static let sessionDeleted = Notification.Name("SessionDeleted")
    static let sessionsDeleted = Notification.Name("SessionsDeleted")
}