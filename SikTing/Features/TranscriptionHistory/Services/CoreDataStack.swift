//
//  CoreDataStack.swift
//  SikTing
//
//  Created by <PERSON><PERSON> on 7/19/25.
//

import Foundation
import CoreData

/// Core Data stack manager for TranscriptionHistory feature
class CoreDataStack {
    
    // MARK: - Singleton
    
    static let shared = CoreDataStack()
    
    private init() {}
    
    // MARK: - Core Data Stack
    
    lazy var persistentContainer: NSPersistentContainer = {
        let container = NSPersistentContainer(name: "TranscriptionHistory")
        
        // Configure store description for better performance and security
        let storeDescription = container.persistentStoreDescriptions.first
        storeDescription?.shouldInferMappingModelAutomatically = true
        storeDescription?.shouldMigrateStoreAutomatically = true
        storeDescription?.setOption(FileProtectionType.complete as NSObject, forKey: NSPersistentStoreFileProtectionKey)
        
        container.loadPersistentStores { [weak self] _, error in
            if let error = error as NSError? {
                // In production, you should handle this error appropriately
                print("Core Data error: \(error), \(error.userInfo)")
                
                // For development, we can try to recover by removing the store
                self?.handleCoreDataError(error, container: container)
            }
        }
        
        // Configure merge policy for concurrent access
        container.viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        container.viewContext.automaticallyMergesChangesFromParent = true
        
        return container
    }()
    
    /// Main context for UI operations (main queue)
    var mainContext: NSManagedObjectContext {
        return persistentContainer.viewContext
    }
    
    /// Background context for heavy operations
    var backgroundContext: NSManagedObjectContext {
        return persistentContainer.newBackgroundContext()
    }
    
    // MARK: - Core Data Operations
    
    /// Saves the main context
    func saveMainContext() {
        saveContext(mainContext)
    }
    
    /// Saves a specific context
    func saveContext(_ context: NSManagedObjectContext) {
        guard context.hasChanges else { return }
        
        do {
            try context.save()
        } catch {
            print("Failed to save context: \(error)")
            
            // Rollback changes on error
            context.rollback()
            
            // Post notification for error handling
            NotificationCenter.default.post(
                name: .coreDataSaveError,
                object: nil,
                userInfo: ["error": error]
            )
        }
    }
    
    /// Performs a background task with automatic saving
    func performBackgroundTask(_ block: @escaping (NSManagedObjectContext) -> Void) {
        let context = backgroundContext
        context.perform {
            block(context)
            self.saveContext(context)
        }
    }
    
    /// Performs a background task and calls completion on main queue
    func performBackgroundTask<T>(
        _ block: @escaping (NSManagedObjectContext) -> T,
        completion: @escaping (T) -> Void
    ) {
        let context = backgroundContext
        context.perform {
            let result = block(context)
            self.saveContext(context)
            
            DispatchQueue.main.async {
                completion(result)
            }
        }
    }
    
    // MARK: - Error Handling
    
    private func handleCoreDataError(_ error: NSError, container: NSPersistentContainer) {
        // This is a simplified error recovery mechanism
        // In production, you might want more sophisticated handling
        
        guard let storeURL = container.persistentStoreDescriptions.first?.url else {
            return
        }
        
        do {
            // Remove the problematic store
            try FileManager.default.removeItem(at: storeURL)
            
            // Try to reload the store
            container.loadPersistentStores { _, reloadError in
                if let reloadError = reloadError {
                    print("Failed to reload store after recovery: \(reloadError)")
                } else {
                    print("Successfully recovered Core Data store")
                }
            }
        } catch {
            print("Failed to remove corrupted store: \(error)")
        }
    }
    
    // MARK: - Cleanup
    
    /// Cleans up old data based on retention policy
    func cleanupOldData(olderThan days: Int = 90) {
        performBackgroundTask { context in
            let cutoffDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()
            
            let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
            request.predicate = NSPredicate(format: "createdAt < %@ AND isSaved == NO AND isFavourite == NO", cutoffDate as NSDate)
            
            do {
                let oldSessions = try context.fetch(request)
                for session in oldSessions {
                    context.delete(session)
                }
                
                print("Cleaned up \(oldSessions.count) old sessions")
            } catch {
                print("Failed to cleanup old data: \(error)")
            }
        }
    }
    
    /// Returns the size of the Core Data store in bytes
    func getStoreSize() -> Int64 {
        guard let storeURL = persistentContainer.persistentStoreDescriptions.first?.url else {
            return 0
        }
        
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: storeURL.path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            print("Failed to get store size: \(error)")
            return 0
        }
    }
    
    /// Validates the Core Data model integrity
    func validateModelIntegrity() -> Bool {
        do {
            // Try to fetch a small number of sessions to validate the model
            let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
            request.fetchLimit = 1
            _ = try mainContext.fetch(request)
            return true
        } catch {
            print("Core Data model validation failed: \(error)")
            return false
        }
    }
    
    /// Exports all data for backup purposes
    func exportAllData() -> [String: Any] {
        var exportData: [String: Any] = [:]
        
        do {
            // Export sessions
            let sessionRequest: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
            let sessions = try mainContext.fetch(sessionRequest)
            
            let sessionData = sessions.map { session in
                return [
                    "id": session.id?.uuidString ?? "",
                    "title": session.title ?? "",
                    "createdAt": session.createdAt?.timeIntervalSince1970 ?? 0,
                    "updatedAt": session.updatedAt?.timeIntervalSince1970 ?? 0,
                    "duration": session.duration,
                    "contentPreview": session.contentPreview ?? "",
                    "isFavourite": session.isFavourite,
                    "isSaved": session.isSaved,
                    "hasTranslations": session.hasTranslations,
                    "detectedLanguages": session.detectedLanguages ?? [],
                    "translationLanguages": session.translationLanguages ?? []
                ]
            }
            
            exportData["sessions"] = sessionData
            exportData["exportDate"] = Date().timeIntervalSince1970
            exportData["version"] = "1.0"
            
        } catch {
            print("Failed to export data: \(error)")
        }
        
        return exportData
    }
}

// MARK: - Notifications

extension Notification.Name {
    static let coreDataSaveError = Notification.Name("CoreDataSaveError")
}

// MARK: - Fetch Request Extensions

extension HistorySession {
    
    /// Fetch request for recent sessions (last 30 days)
    static func recentSessionsFetchRequest() -> NSFetchRequest<HistorySession> {
        let request = fetchRequest()
        let thirtyDaysAgo = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        request.predicate = NSPredicate(format: "createdAt >= %@", thirtyDaysAgo as NSDate)
        request.sortDescriptors = [NSSortDescriptor(keyPath: \HistorySession.createdAt, ascending: false)]
        return request
    }
    
    /// Fetch request for favourite sessions
    static func favouriteSessionsFetchRequest() -> NSFetchRequest<HistorySession> {
        let request = fetchRequest()
        request.predicate = NSPredicate(format: "isFavourite == YES")
        request.sortDescriptors = [NSSortDescriptor(keyPath: \HistorySession.updatedAt, ascending: false)]
        return request
    }
    
    /// Fetch request for saved sessions
    static func savedSessionsFetchRequest() -> NSFetchRequest<HistorySession> {
        let request = fetchRequest()
        request.predicate = NSPredicate(format: "isSaved == YES")
        request.sortDescriptors = [NSSortDescriptor(keyPath: \HistorySession.updatedAt, ascending: false)]
        return request
    }
    
    /// Fetch request for searching sessions
    static func searchFetchRequest(query: String) -> NSFetchRequest<HistorySession> {
        let request = fetchRequest()
        request.predicate = NSPredicate(
            format: "title CONTAINS[cd] %@ OR contentPreview CONTAINS[cd] %@ OR ANY entries.originalText CONTAINS[cd] %@ OR ANY entries.translatedText CONTAINS[cd] %@",
            query, query, query, query
        )
        request.sortDescriptors = [NSSortDescriptor(keyPath: \HistorySession.updatedAt, ascending: false)]
        return request
    }
}

// HistoryEntry fetch request is defined in HistoryEntry+CoreDataProperties.swift