//
//  HistorySessionCard.swift
//  SikTing
//
//  Created by Augment Agent on 2025-07-21.
//

import SwiftUI

/// Card component for displaying individual history sessions in the list
struct HistorySessionCard: View {
    
    // MARK: - Properties
    
    let session: HistorySession
    let onTap: () -> Void
    let onFavoriteToggle: () -> Void
    let onSaveToggle: () -> Void
    let onDelete: () -> Void
    
    @State private var showingDeleteAlert = false
    @State private var isPressed = false
    @State private var showingActionSheet = false
    @State private var showingDetailView = false
    @State private var isDeleting = false
    @State private var deleteScale: CGFloat = 1.0
    
    // Design constants - Updated with brand colors
    private let cardBackgroundColor = DesignSystem.brandColors.alabaster
    private let shadowColor = DesignSystem.brandColors.persianPurple.opacity(0.1)
    private let favoriteColor = DesignSystem.brandColors.amber
    private let saveColor = DesignSystem.brandColors.orchid
    private let cornerRadius: CGFloat = CornerRadius.card
    private let shadowRadius: CGFloat = ShadowStyles.card.radius
    private let cardPadding: CGFloat = DesignSystem.spacing.cardPadding

    // Computed properties for improved layout logic
    private var hasTagsOrCategory: Bool {
        let category = session.category
        let tags = session.tags?.components(separatedBy: ",").filter { !$0.isEmpty } ?? []
        return category != nil || !tags.isEmpty
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Main content area with simplified layout
            VStack(alignment: .leading, spacing: DesignSystem.spacing.small) {
                // Header with action buttons only (no title, no time)
                HStack(alignment: .top) {
                    Spacer()

                    // Action buttons in top-right corner
                    HStack(spacing: DesignSystem.spacing.xSmall) {
                        // Favorite button
                        Button(action: onFavoriteToggle) {
                            Image(systemName: session.isFavourite ? "heart.fill" : "heart")
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(session.isFavourite ? favoriteColor : DesignSystem.brandColors.brandTextTertiary)
                                .frame(width: 32, height: 32)
                                .background(
                                    Circle()
                                        .fill(session.isFavourite ? favoriteColor.opacity(0.1) : Color.clear)
                                )
                        }
                        .buttonStyle(PlainButtonStyle())
                        .accessibilityIdentifier(AccessibilityHelper.Identifier.favoriteButton)
                        .accessibilityLabel(AccessibilityHelper.Label.favoriteButton(isFavorite: session.isFavourite))
                        .accessibilityHint(AccessibilityHelper.Hint.favoriteButton)
                        .accessibilityAddTraits(AccessibilityHelper.Trait.button)

                        // Save button
                        Button(action: onSaveToggle) {
                            Image(systemName: session.isSaved ? "bookmark.fill" : "bookmark")
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(session.isSaved ? saveColor : DesignSystem.brandColors.brandTextTertiary)
                                .frame(width: 32, height: 32)
                                .background(
                                    Circle()
                                        .fill(session.isSaved ? saveColor.opacity(0.1) : Color.clear)
                                )
                        }
                        .buttonStyle(PlainButtonStyle())
                        .accessibilityIdentifier(AccessibilityHelper.Identifier.saveButton)
                        .accessibilityLabel(AccessibilityHelper.Label.saveButton(isSaved: session.isSaved))
                        .accessibilityHint(AccessibilityHelper.Hint.saveButton)
                        .accessibilityAddTraits(AccessibilityHelper.Trait.button)
                    }
                }

                // Content preview - now the main focus of the card
                if let contentPreview = session.contentPreview, !contentPreview.isEmpty {
                    Text(contentPreview)
                        .font(DesignSystem.typography.bodyPrimary)
                        .foregroundColor(DesignSystem.brandColors.brandTextPrimary)
                        .lineLimit(5) // Increased to 5 lines since no title
                        .lineSpacing(3) // Increased line spacing for better readability
                        .multilineTextAlignment(.leading)
                        .fixedSize(horizontal: false, vertical: true)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }

                // Tags and Category with improved spacing
                if hasTagsOrCategory {
                    VStack(alignment: .leading, spacing: DesignSystem.spacing.micro) {
                        // Category
                        if let category = session.category {
                            HStack(spacing: DesignSystem.spacing.micro) {
                                Image(systemName: "folder")
                                    .font(.caption2)
                                    .foregroundColor(.blue)

                                Text(category)
                                    .font(.caption2)
                                    .fontWeight(.medium)
                                    .foregroundColor(.blue)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(Color.blue.opacity(0.1))
                                    .cornerRadius(4)
                            }
                        }

                        // Tags
                        let tags = session.tags?.components(separatedBy: ",").filter { !$0.isEmpty } ?? []
                        if !tags.isEmpty {
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: DesignSystem.spacing.micro) {
                                    ForEach(tags.prefix(3), id: \.self) { tag in
                                        Text(tag)
                                            .font(.caption2)
                                            .fontWeight(.medium)
                                            .foregroundColor(.orange)
                                            .padding(.horizontal, 6)
                                            .padding(.vertical, 2)
                                            .background(Color.orange.opacity(0.1))
                                            .cornerRadius(4)
                                    }

                                    if tags.count > 3 {
                                        Text("+\(tags.count - 3)")
                                            .font(.caption2)
                                            .foregroundColor(.secondary)
                                    }
                                }
                                .padding(.horizontal, 1)
                            }
                        }
                    }
                    .padding(.top, DesignSystem.spacing.xSmall)
                }
            }
            .padding(cardPadding)

            // Simplified footer with duration only
            if let duration = session.formattedDuration {
                HStack {
                    // Duration display
                    HStack(spacing: DesignSystem.spacing.micro) {
                        Image(systemName: "clock")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(DesignSystem.brandColors.brandTextTertiary)

                        Text(duration)
                            .font(DesignSystem.typography.captionSecondary)
                            .foregroundColor(DesignSystem.brandColors.brandTextTertiary)
                    }

                    Spacer()
                }
                .padding(.horizontal, cardPadding)
                .padding(.bottom, DesignSystem.spacing.cardFooterSpacing)
                .padding(.top, DesignSystem.spacing.xSmall)
                .background(
                    Rectangle()
                        .fill(DesignSystem.brandColors.brandSurface.opacity(0.2))
                        .frame(height: 1)
                        .padding(.horizontal, cardPadding),
                    alignment: .top
                )
            }
        }
        .background(cardBackgroundColor)
        .overlay(
            RoundedRectangle(cornerRadius: cornerRadius)
                .stroke(DesignSystem.brandColors.frenchLilac, lineWidth: 1)
        )
        .cornerRadius(cornerRadius)
        .shadow(color: shadowColor, radius: shadowRadius, x: 0, y: 2)
        .scaleEffect(isPressed ? 0.98 : deleteScale)
        .opacity(isDeleting ? 0.0 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .animation(.easeInOut(duration: 0.3), value: isDeleting)
        .animation(.interpolatingSpring(mass: 1.0, stiffness: 170, damping: 26), value: deleteScale)
        .onTapGesture {
            showingDetailView = true
        }
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {
            // Long press action could be added here if needed
            showingDeleteAlert = true
        }
        .accessibilityIdentifier(AccessibilityHelper.Identifier.historySessionCard)
        .accessibilityLabel(AccessibilityHelper.Label.sessionCard(
            title: session.displayTitle,
            date: session.formattedDate,
            isFavorite: session.isFavourite,
            isSaved: session.isSaved
        ))
        .accessibilityHint(AccessibilityHelper.Hint.sessionCard)
        .accessibilityAddTraits(AccessibilityHelper.Trait.button)
        .alert("Delete Note", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                // Animate deletion before calling onDelete
                withAnimation(.interpolatingSpring(mass: 1.0, stiffness: 170, damping: 26)) {
                    isDeleting = true
                    deleteScale = 0.8
                }

                // Provide haptic feedback
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()

                // Call onDelete after a brief delay to allow animation
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    onDelete()
                }
            }
        } message: {
            Text("Are you sure you want to delete this note? This action cannot be undone.")
        }
        .sheet(isPresented: $showingDetailView) {
            HistoryDetailView(session: session)
        }
    }
}

// MARK: - HistorySession Extensions

extension HistorySession {
    
    /// Display title with fallback
    var displayTitle: String {
        if let title = title, !title.isEmpty {
            return title
        }
        return "Untitled Note"
    }
    
    /// Formatted creation date
    var formattedDate: String {
        guard let createdAt = createdAt else {
            return "Unknown date"
        }

        let formatter = DateFormatter()
        let calendar = Calendar.current

        if calendar.isDateInToday(createdAt) {
            formatter.dateFormat = "h:mm a"
            return "Today at \(formatter.string(from: createdAt))"
        } else if calendar.isDateInYesterday(createdAt) {
            formatter.dateFormat = "h:mm a"
            return "Yesterday at \(formatter.string(from: createdAt))"
        } else if calendar.dateInterval(of: .weekOfYear, for: Date())?.contains(createdAt) == true {
            formatter.dateFormat = "EEEE 'at' h:mm a"
            return formatter.string(from: createdAt)
        } else {
            formatter.dateFormat = "MMM d, yyyy 'at' h:mm a"
            return formatter.string(from: createdAt)
        }
    }

    /// Formatted creation time for improved header display
    var formattedCreationTime: String {
        guard let createdAt = createdAt else {
            return "Unknown time"
        }

        let formatter = DateFormatter()
        let calendar = Calendar.current

        if calendar.isDateInToday(createdAt) {
            formatter.dateFormat = "h:mm a"
            return formatter.string(from: createdAt)
        } else if calendar.isDateInYesterday(createdAt) {
            return "Yesterday"
        } else if calendar.dateInterval(of: .weekOfYear, for: Date())?.contains(createdAt) == true {
            formatter.dateFormat = "EEEE"
            return formatter.string(from: createdAt)
        } else {
            formatter.dateFormat = "MMM d"
            return formatter.string(from: createdAt)
        }
    }
    
    /// Formatted duration if available
    var formattedDuration: String? {
        guard let createdAt = createdAt,
              let updatedAt = updatedAt else {
            return nil
        }
        
        let duration = updatedAt.timeIntervalSince(createdAt)
        
        if duration < 60 {
            return "\(Int(duration))s"
        } else if duration < 3600 {
            let minutes = Int(duration / 60)
            return "\(minutes)m"
        } else {
            let hours = Int(duration / 3600)
            let minutes = Int((duration.truncatingRemainder(dividingBy: 3600)) / 60)
            return "\(hours)h \(minutes)m"
        }
    }
    

}

// MARK: - Preview

#Preview {
    struct PreviewWrapper: View {
        @State private var sampleSession: HistorySession = {
            let session = HistorySession()
            session.id = UUID()
            session.title = "" // No title displayed
            session.contentPreview = "We discussed the Q2 roadmap and key product initiatives. The team reviewed user feedback from the latest release and identified priority features for the next sprint. Important decisions were made about resource allocation and timeline adjustments to ensure we meet our quarterly goals and deliver value to our users."
            session.createdAt = Date().addingTimeInterval(-7200) // 2 hours ago
            session.updatedAt = Date().addingTimeInterval(-6900) // 1 hour 55 minutes ago
            session.entryCount = 23
            session.isFavourite = true
            session.isSaved = true
            return session
        }()

        @State private var sampleSession2: HistorySession = {
            let session = HistorySession()
            session.id = UUID()
            session.title = "" // No title displayed
            session.contentPreview = "Remember to pick up groceries on the way home. Need milk, bread, and eggs for tomorrow's breakfast. Also don't forget to call mom about the weekend plans."
            session.createdAt = Date().addingTimeInterval(-300) // 5 minutes ago
            session.updatedAt = Date().addingTimeInterval(-240) // 4 minutes ago
            session.entryCount = 3
            session.isFavourite = false
            session.isSaved = false
            return session
        }()

        @State private var sampleSession3: HistorySession = {
            let session = HistorySession()
            session.id = UUID()
            session.title = "" // No title displayed
            session.contentPreview = "今天的会议讨论了新产品的设计方案。团队提出了几个创新的想法，特别是在用户体验方面有很大的改进。我们需要在下周之前完成原型设计。"
            session.createdAt = Date().addingTimeInterval(-1800) // 30 minutes ago
            session.updatedAt = Date().addingTimeInterval(-1680) // 28 minutes ago
            session.entryCount = 8
            session.isFavourite = true
            session.isSaved = false
            return session
        }()

        var body: some View {
            ScrollView {
                VStack(spacing: 16) {
                    HistorySessionCard(
                        session: sampleSession,
                        onTap: {
                            print("Session 1 tapped")
                        },
                        onFavoriteToggle: {
                            sampleSession.isFavourite.toggle()
                        },
                        onSaveToggle: {
                            sampleSession.isSaved.toggle()
                        },
                        onDelete: {
                            print("Delete session 1")
                        }
                    )

                    HistorySessionCard(
                        session: sampleSession2,
                        onTap: {
                            print("Session 2 tapped")
                        },
                        onFavoriteToggle: {
                            sampleSession2.isFavourite.toggle()
                        },
                        onSaveToggle: {
                            sampleSession2.isSaved.toggle()
                        },
                        onDelete: {
                            print("Delete session 2")
                        }
                    )

                    HistorySessionCard(
                        session: sampleSession3,
                        onTap: {
                            print("Session 3 tapped")
                        },
                        onFavoriteToggle: {
                            sampleSession3.isFavourite.toggle()
                        },
                        onSaveToggle: {
                            sampleSession3.isSaved.toggle()
                        },
                        onDelete: {
                            print("Delete session 3")
                        }
                    )
                }
                .padding()
            }
            .background(Color(.systemGroupedBackground))
        }
    }
    
    return PreviewWrapper()
}
