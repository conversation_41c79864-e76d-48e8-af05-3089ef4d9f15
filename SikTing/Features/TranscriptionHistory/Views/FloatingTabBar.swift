//
//  FloatingTabBar.swift
//  SikTing
//
//  Created by Augment Agent on 2025/1/25.
//

import SwiftUI

/// Floating tab bar component with intelligent show/hide behavior
/// Provides modern floating design with Persian Purple active states and Orchid inactive states
struct FloatingTabBar: View {
    
    // MARK: - Properties

    @Binding var selectedTab: HistoryTab
    @Binding var isVisible: Bool
    let onTabSelected: (HistoryTab) -> Void
    let animationManager: FloatingHistoryAnimationManager
    
    // MARK: - Design Constants

    private let tabs: [HistoryTab] = [.recents, .favorites, .saved]
    private let cornerRadius: CGFloat = 32
    private let horizontalPadding: CGFloat = 16
    private let verticalPadding: CGFloat = 8
    private let shadowRadius: CGFloat = 8
    private let shadowOffset: CGFloat = 4
    private let hideOffset: CGFloat = 100

    // iPhone 16 specific safe area handling
    private let minimumBottomMargin: CGFloat = 24
    private let maximumBottomMargin: CGFloat = 34 // For devices with home indicator
    
    // MARK: - Body
    
    var body: some View {
        GeometryReader { geometry in
            HStack(spacing: 0) {
                ForEach(tabs, id: \.self) { tab in
                    FloatingTabButton(
                        tab: tab,
                        isSelected: selectedTab == tab,
                        onTap: {
                            // Prevent same-tab selection to avoid unnecessary filtering and animations
                            guard selectedTab != tab else { return }
                            withAnimation(DesignSystem.animations.standard) {
                                selectedTab = tab
                                onTabSelected(tab)
                            }
                        },
                        animationManager: animationManager
                    )
                }
            }
            .padding(.horizontal, horizontalPadding)
            .padding(.vertical, verticalPadding)
            .background(floatingBackground)
            .cornerRadius(cornerRadius)
            .shadow(
                color: DesignSystem.brandColors.persianPurple.opacity(0.15),
                radius: shadowRadius,
                x: 0,
                y: shadowOffset
            )
            .offset(y: isVisible ? 0 : hideOffset)
            .opacity(isVisible ? 1 : 0)
            .animation(DesignSystem.animations.standard, value: isVisible)
            .position(
                x: geometry.size.width / 2,
                y: geometry.size.height - safeBottomMargin(for: geometry)
            )
            .accessibilityElement(children: .contain)
            .accessibilityLabel("History tab bar")
            .accessibilityHint("Swipe to navigate between Recent, Favourite, and Saved transcriptions")
        }
    }

    // MARK: - Helper Methods

    /// Calculates appropriate bottom margin based on safe area
    private func safeBottomMargin(for geometry: GeometryProxy) -> CGFloat {
        let safeAreaBottom = geometry.safeAreaInsets.bottom
        return max(minimumBottomMargin, safeAreaBottom + 8)
    }
    
    // MARK: - Helper Views
    
    private var floatingBackground: some View {
        DesignSystem.brandColors.adaptiveBackground
            .opacity(0.95)
            .background(.ultraThinMaterial)
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 40) {
        // Visible state
        FloatingTabBar(
            selectedTab: .constant(.recents),
            isVisible: .constant(true),
            onTabSelected: { _ in },
            animationManager: FloatingHistoryAnimationManager()
        )

        // Hidden state (for testing)
        FloatingTabBar(
            selectedTab: .constant(.favorites),
            isVisible: .constant(false),
            onTabSelected: { _ in },
            animationManager: FloatingHistoryAnimationManager()
        )
        
        // Interactive demo
        FloatingTabBarDemo()
    }
    .padding()
    .background(DesignSystem.brandColors.adaptiveBackgroundGradient)
}

/// Demo component for testing tab interactions
private struct FloatingTabBarDemo: View {
    @State private var selectedTab: HistoryTab = .recents
    @State private var isVisible: Bool = true
    
    var body: some View {
        VStack(spacing: 20) {
            Text("Selected: \(selectedTab.displayName)")
                .font(DesignSystem.typography.body)
                .foregroundColor(DesignSystem.brandColors.adaptiveTextPrimary)
            
            Button("Toggle Visibility") {
                withAnimation {
                    isVisible.toggle()
                }
            }
            .buttonStyle(.borderedProminent)
            .tint(DesignSystem.brandColors.persianPurple)
            
            FloatingTabBar(
                selectedTab: $selectedTab,
                isVisible: $isVisible,
                onTabSelected: { tab in
                    print("Tab selected: \(tab.displayName)")
                },
                animationManager: FloatingHistoryAnimationManager()
            )
        }
    }
}
