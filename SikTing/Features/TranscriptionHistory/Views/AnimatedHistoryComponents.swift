//
//  AnimatedHistoryComponents.swift
//  SikTing
//
//  Created by Augment Agent on 2025-07-21.
//

import SwiftUI

// MARK: - Animated Session Card

/// Enhanced session card with smooth animations and haptic feedback
struct AnimatedSessionCard: View {
    let session: HistorySession
    let onTap: () -> Void
    let onFavoriteToggle: () -> Void
    let onSaveToggle: () -> Void
    let onDelete: () -> Void
    
    @State private var isPressed = false
    @State private var isFavoritePressed = false
    @State private var scale: CGFloat = 1.0
    @Environment(\.hapticFeedback) private var hapticFeedback
    
    var body: some View {
        HistorySessionCard(
            session: session,
            onTap: onTap,
            onFavoriteToggle: {
                withAnimation(AnimationManager.favoriteToggle) {
                    isFavoritePressed = true
                }

                if session.isFavourite {
                    hapticFeedback.sessionUnfavorited()
                } else {
                    hapticFeedback.sessionFavorited()
                }
                onFavoriteToggle()

                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    isFavoritePressed = false
                }
            },
            onSaveToggle: {
                hapticFeedback.buttonPressed()
                onSaveToggle()
            },
            onDelete: {
                withAnimation(AnimationManager.error) {
                    scale = 0.95
                }

                hapticFeedback.sessionDeleted()
                onDelete()
            }
        )
        .scaleEffect(scale)
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(AnimationManager.accessibleButtonPress, value: isPressed)
        .animation(AnimationManager.accessibleCardEntrance, value: scale)
        .onTapGesture {
            withAnimation(AnimationManager.accessibleButtonPress) {
                isPressed = true
            }
            
            hapticFeedback.sessionSelected()
            onTap()
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(AnimationManager.accessibleButtonPress) {
                    isPressed = false
                }
            }
        }
        .onAppear {
            withAnimation(AnimationManager.cardEntranceSpring.delay(0.1)) {
                scale = 1.0
            }
        }
    }
}

// MARK: - Animated Loading View

/// Loading view with smooth animations
struct AnimatedLoadingView: View {
    @State private var isAnimating = false
    @State private var rotation: Double = 0
    
    var body: some View {
        VStack(spacing: 20) {
            // Rotating loading indicator
            Circle()
                .trim(from: 0, to: 0.7)
                .stroke(Color.blue, lineWidth: 4)
                .frame(width: 50, height: 50)
                .rotationEffect(.degrees(rotation))
                .animation(AnimationManager.loading, value: rotation)
            
            // Pulsing text
            Text("Loading...")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .scaleEffect(isAnimating ? 1.1 : 1.0)
                .animation(AnimationManager.pulse(), value: isAnimating)
        }
        .onAppear {
            rotation = 360
            isAnimating = true
        }
    }
}

// MARK: - Animated Search Bar

/// Search bar with smooth expand/collapse animations
struct AnimatedSearchBar: View {
    @Binding var searchText: String
    @Binding var isSearching: Bool
    
    @State private var searchBarWidth: CGFloat = 0
    @Environment(\.hapticFeedback) private var hapticFeedback
    
    var body: some View {
        HStack {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                    .scaleEffect(isSearching ? 1.1 : 1.0)
                    .animation(AnimationManager.searchBarAppear, value: isSearching)
                
                TextField("Search sessions...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                    .font(.scaledFont(.body))
                    .dynamicTypeSize(...DynamicTypeSize.accessibility3)
                    .onSubmit {
                        if !searchText.isEmpty {
                            hapticFeedback.searchResultsFound()
                        }
                    }
                    .accessibilityIdentifier(AccessibilityHelper.Identifier.historySearchBar)
                    .accessibilityLabel(AccessibilityHelper.Label.searchBar(hasText: !searchText.isEmpty))
                    .accessibilityHint(AccessibilityHelper.Hint.searchBar)
                    .accessibilityAddTraits(AccessibilityHelper.Trait.searchField)
                
                if isSearching && !searchText.isEmpty {
                    Button(action: {
                        withAnimation(AnimationManager.searchFilter) {
                            searchText = ""
                        }
                        hapticFeedback.buttonPressed()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                    .transition(.scale.combined(with: .opacity))
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
            .cornerRadius(10)
            .frame(width: searchBarWidth)
            .animation(AnimationManager.searchBarAppear, value: searchBarWidth)
            
            if isSearching {
                Button("Cancel") {
                    withAnimation(AnimationManager.searchBarAppear) {
                        isSearching = false
                        searchText = ""
                        searchBarWidth = 0
                    }
                    hapticFeedback.buttonPressed()
                }
                .font(.scaledFont(.body))
                .dynamicTypeSize(...DynamicTypeSize.accessibility2)
                .minimumTouchTarget()
                .accessibilityLabel("Cancel search")
                .accessibilityAddTraits(AccessibilityHelper.Trait.button)
                .transition(.move(edge: .trailing).combined(with: .opacity))
            }
        }
        .onAppear {
            searchBarWidth = UIScreen.main.bounds.width - 32
        }
        .onChange(of: isSearching) { _, newValue in
            withAnimation(AnimationManager.searchBarAppear) {
                if newValue {
                    searchBarWidth = UIScreen.main.bounds.width - 100
                } else {
                    searchBarWidth = UIScreen.main.bounds.width - 32
                }
            }
        }
    }
}

// MARK: - Animated Empty State

/// Empty state view with engaging animations
struct AnimatedEmptyStateView: View {
    let type: HistoryEmptyStateType
    let onAction: (() -> Void)?
    
    @State private var iconScale: CGFloat = 0.8
    @State private var textOpacity: Double = 0
    @State private var buttonScale: CGFloat = 0.9
    
    var body: some View {
        HistoryEmptyStateView(type: type, onAction: onAction)
            .scaleEffect(iconScale)
            .opacity(textOpacity)
            .accessibilityIdentifier(AccessibilityHelper.Identifier.historyEmptyState)
            .accessibilityLabel(AccessibilityHelper.Label.emptyState)
            .onAppear {
                withAnimation(AnimationManager.bounce().delay(0.2)) {
                    iconScale = 1.0
                }
                
                withAnimation(AnimationManager.Easing.easeOut.delay(0.4)) {
                    textOpacity = 1.0
                }
                
                withAnimation(AnimationManager.Spring.gentle.delay(0.6)) {
                    buttonScale = 1.0
                }
            }
    }
}

// MARK: - Animated Tag View

/// Tag view with smooth add/remove animations
struct AnimatedTagView: View {
    let tag: String
    let isSelected: Bool
    let onTap: () -> Void
    let onRemove: (() -> Void)?
    
    @State private var scale: CGFloat = 1.0
    @State private var rotation: Double = 0
    @Environment(\.hapticFeedback) private var hapticFeedback
    
    var body: some View {
        HStack(spacing: 6) {
            Text(tag)
                .font(.caption)
                .fontWeight(.medium)
            
            if let onRemove = onRemove {
                Button(action: {
                    withAnimation(AnimationManager.tagChange) {
                        scale = 0.8
                        rotation = 180
                    }
                    
                    hapticFeedback.tagRemoved()
                    
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                        onRemove()
                    }
                }) {
                    Image(systemName: "xmark")
                        .font(.caption2)
                        .fontWeight(.bold)
                }
                .transition(.scale.combined(with: .opacity))
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(isSelected ? Color.blue : Color(.systemGray5))
        .foregroundColor(isSelected ? .white : .primary)
        .cornerRadius(16)
        .scaleEffect(scale)
        .rotationEffect(.degrees(rotation))
        .animation(AnimationManager.tagChange, value: scale)
        .animation(AnimationManager.tagChange, value: rotation)
        .onTapGesture {
            withAnimation(AnimationManager.selectionToggle) {
                scale = 0.95
            }
            
            hapticFeedback.tagAdded()
            onTap()
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(AnimationManager.selectionToggle) {
                    scale = 1.0
                }
            }
        }
        .onAppear {
            withAnimation(AnimationManager.listItemAppear.delay(Double.random(in: 0...0.3))) {
                scale = 1.0
            }
        }
    }
}

// MARK: - Animated Progress View

/// Progress view with smooth animations
struct AnimatedProgressView: View {
    let progress: Double
    let label: String
    
    @State private var animatedProgress: Double = 0
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(label)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("\(Int(animatedProgress * 100))%")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.blue)
            }
            
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .frame(height: 4)
                        .cornerRadius(2)
                    
                    Rectangle()
                        .fill(Color.blue)
                        .frame(width: geometry.size.width * animatedProgress, height: 4)
                        .cornerRadius(2)
                        .animation(AnimationManager.progress, value: animatedProgress)
                }
            }
            .frame(height: 4)
        }
        .onAppear {
            withAnimation(AnimationManager.progress.delay(0.2)) {
                animatedProgress = progress
            }
        }
        .onChange(of: progress) { _, newValue in
            withAnimation(AnimationManager.progress) {
                animatedProgress = newValue
            }
        }
    }
}

// MARK: - Animated Button

/// Button with haptic feedback and animations
struct AnimatedActionButton: View {
    let title: String
    let icon: String
    let action: () -> Void
    let style: ButtonStyle
    
    @State private var isPressed = false
    @Environment(\.hapticFeedback) private var hapticFeedback
    
    enum ButtonStyle {
        case primary, secondary, destructive
        
        var backgroundColor: Color {
            switch self {
            case .primary: return .blue
            case .secondary: return Color(.systemGray5)
            case .destructive: return .red
            }
        }
        
        var foregroundColor: Color {
            switch self {
            case .primary: return .white
            case .secondary: return .primary
            case .destructive: return .white
            }
        }
    }
    
    var body: some View {
        Button(action: {
            withAnimation(AnimationManager.cardPress) {
                isPressed = true
            }
            
            hapticFeedback.buttonPressed()
            action()
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(AnimationManager.cardPress) {
                    isPressed = false
                }
            }
        }) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                
                Text(title)
                    .font(.system(size: 16, weight: .medium))
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(style.backgroundColor)
            .foregroundColor(style.foregroundColor)
            .cornerRadius(10)
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(AnimationManager.cardPress, value: isPressed)
        }
    }
}
