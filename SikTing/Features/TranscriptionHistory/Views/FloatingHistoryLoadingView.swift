//
//  FloatingHistoryLoadingView.swift
//  SikTing
//
//  Created by Augment Agent on 2025/1/25.
//

import SwiftUI

/// Loading state view with skeleton UI for floating history
/// Provides smooth transitions and accessibility support
struct FloatingHistoryLoadingView: View {
    
    // MARK: - Properties
    
    @EnvironmentObject private var animationManager: FloatingHistoryAnimationManager
    @State private var animationPhase: CGFloat = 0
    
    // MARK: - Design Constants
    
    private let skeletonColor = DesignSystem.brandColors.orchid.opacity(0.1)
    private let skeletonHighlightColor = DesignSystem.brandColors.orchid.opacity(0.2)
    private let cornerRadius: CGFloat = 8
    private let itemSpacing: CGFloat = 16
    private let gridColumns = 2
    private let itemsPerPage = 6
    
    // MARK: - Body
    
    var body: some View {
        LazyVGrid(columns: gridColumnsLayout, spacing: itemSpacing) {
            ForEach(0..<itemsPerPage, id: \.self) { index in
                SkeletonCardView(
                    animationDelay: Double(index) * 0.1,
                    animationPhase: animationPhase
                )
            }
        }
        .padding(.horizontal, 20)
        .onAppear {
            startSkeletonAnimation()
        }
        .onDisappear {
            stopSkeletonAnimation()
        }
        .accessibilityLabel("Loading history sessions")
        .accessibilityHint("Please wait while sessions are being loaded")
    }
    
    // MARK: - Computed Properties
    
    private var gridColumnsLayout: [GridItem] {
        Array(repeating: GridItem(.flexible(), spacing: itemSpacing), count: gridColumns)
    }
    
    // MARK: - Animation Methods
    
    private func startSkeletonAnimation() {
        animationManager.startLoadingStateAnimation()
        
        withAnimation(
            .easeInOut(duration: 1.5)
            .repeatForever(autoreverses: true)
        ) {
            animationPhase = 1.0
        }
    }
    
    private func stopSkeletonAnimation() {
        animationManager.stopLoadingStateAnimation()
        animationPhase = 0
    }
}

// MARK: - Skeleton Card View

private struct SkeletonCardView: View {
    let animationDelay: Double
    let animationPhase: CGFloat
    
    @State private var localAnimationPhase: CGFloat = 0
    
    private let cardHeight: CGFloat = 120
    private let cornerRadius: CGFloat = 12
    private let contentPadding: CGFloat = 16
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Title skeleton
            RoundedRectangle(cornerRadius: 4)
                .fill(skeletonGradient)
                .frame(height: 16)
                .frame(maxWidth: .infinity)
            
            // Subtitle skeleton
            RoundedRectangle(cornerRadius: 4)
                .fill(skeletonGradient)
                .frame(height: 12)
                .frame(maxWidth: .infinity)
                .scaleEffect(x: 0.7, anchor: .leading)
            
            Spacer()
            
            // Bottom row with date and actions
            HStack {
                // Date skeleton
                RoundedRectangle(cornerRadius: 4)
                    .fill(skeletonGradient)
                    .frame(width: 60, height: 10)
                
                Spacer()
                
                // Action buttons skeleton
                HStack(spacing: 8) {
                    Circle()
                        .fill(skeletonGradient)
                        .frame(width: 20, height: 20)
                    
                    Circle()
                        .fill(skeletonGradient)
                        .frame(width: 20, height: 20)
                }
            }
        }
        .padding(contentPadding)
        .frame(height: cardHeight)
        .background(
            RoundedRectangle(cornerRadius: cornerRadius)
                .fill(DesignSystem.brandColors.adaptiveBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .stroke(DesignSystem.brandColors.orchid.opacity(0.1), lineWidth: 1)
                )
        )
        .onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + animationDelay) {
                withAnimation(
                    .easeInOut(duration: 1.5)
                    .repeatForever(autoreverses: true)
                ) {
                    localAnimationPhase = 1.0
                }
            }
        }
    }
    
    private var skeletonGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(stops: [
                .init(color: DesignSystem.brandColors.orchid.opacity(0.1), location: 0),
                .init(color: DesignSystem.brandColors.orchid.opacity(0.2), location: localAnimationPhase),
                .init(color: DesignSystem.brandColors.orchid.opacity(0.1), location: 1)
            ]),
            startPoint: .leading,
            endPoint: .trailing
        )
    }
}

// MARK: - Enhanced Empty State View

struct FloatingHistoryEnhancedEmptyStateView: View {
    let selectedTab: HistoryTab
    let onAction: () -> Void
    
    @EnvironmentObject private var animationManager: FloatingHistoryAnimationManager
    @State private var isAnimating: Bool = false
    
    var body: some View {
        VStack(spacing: 24) {
            // Animated icon
            Image(systemName: selectedTab.emptyStateIcon)
                .font(.system(size: 64, weight: .light))
                .foregroundColor(DesignSystem.brandColors.orchid.opacity(0.6))
                .scaleEffect(isAnimating ? 1.1 : 1.0)
                .opacity(isAnimating ? 0.8 : 1.0)
            
            VStack(spacing: 12) {
                Text(selectedTab.emptyStateTitle)
                    .font(DesignSystem.typography.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(DesignSystem.brandColors.orchid)
                    .multilineTextAlignment(.center)
                
                Text(selectedTab.emptyStateMessage)
                    .font(DesignSystem.typography.body)
                    .foregroundColor(DesignSystem.brandColors.orchid.opacity(0.8))
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }
            
            // Action button with enhanced styling
            Button(action: {
                HapticFeedbackManager.shared.buttonPressed()
                onAction()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: selectedTab.emptyStateActionIcon)
                        .font(.system(size: 16, weight: .medium))
                    
                    Text(selectedTab.emptyStateActionTitle)
                        .font(DesignSystem.typography.buttonLabel)
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(DesignSystem.brandColors.persianPurple)
                        .shadow(
                            color: DesignSystem.brandColors.persianPurple.opacity(0.3),
                            radius: 8,
                            x: 0,
                            y: 4
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
            .scaleEffect(isAnimating ? 0.98 : 1.0)
        }
        .padding(.horizontal, 40)
        .onAppear {
            withAnimation(animationManager.getEmptyStateTransitionAnimation()) {
                isAnimating = true
            }
            
            // Subtle breathing animation
            withAnimation(
                .easeInOut(duration: 2.0)
                .repeatForever(autoreverses: true)
            ) {
                isAnimating = true
            }
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(selectedTab.emptyStateTitle). \(selectedTab.emptyStateMessage)")
        .accessibilityHint("Double tap \(selectedTab.emptyStateActionTitle)")
    }
}

// MARK: - History Tab Extensions

private extension HistoryTab {
    var emptyStateIcon: String {
        switch self {
        case .recents:
            return "clock.arrow.circlepath"
        case .favorites:
            return "heart"
        case .saved:
            return "bookmark"
        }
    }
    
    var emptyStateTitle: String {
        switch self {
        case .recents:
            return "No Recent Sessions"
        case .favorites:
            return "No Favorite Sessions"
        case .saved:
            return "No Saved Sessions"
        }
    }
    
    var emptyStateMessage: String {
        switch self {
        case .recents:
            return "Start recording to see your transcription sessions here"
        case .favorites:
            return "Mark sessions as favorites to find them easily"
        case .saved:
            return "Save important sessions for quick access"
        }
    }
    
    var emptyStateActionIcon: String {
        switch self {
        case .recents:
            return "mic.fill"
        case .favorites:
            return "heart.fill"
        case .saved:
            return "bookmark.fill"
        }
    }
    
    var emptyStateActionTitle: String {
        switch self {
        case .recents:
            return "Start Recording"
        case .favorites:
            return "Browse Sessions"
        case .saved:
            return "Browse Sessions"
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 40) {
        // Loading state
        FloatingHistoryLoadingView()
            .environmentObject(FloatingHistoryAnimationManager())
        
        Divider()
        
        // Empty state
        FloatingHistoryEnhancedEmptyStateView(
            selectedTab: .favorites,
            onAction: {}
        )
        .environmentObject(FloatingHistoryAnimationManager())
    }
    .padding()
    .background(DesignSystem.brandColors.adaptiveBackgroundGradient)
}
