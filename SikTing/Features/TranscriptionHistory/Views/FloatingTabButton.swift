//
//  FloatingTabButton.swift
//  SikTing
//
//  Created by Augment Agent on 2025/1/25.
//

import SwiftUI

/// Individual tab button component for the floating tab bar
/// Provides 44x44pt minimum touch targets optimized for iPhone 16
struct FloatingTabButton: View {

    // MARK: - Properties

    let tab: HistoryTab
    let isSelected: Bool
    let onTap: () -> Void
    let animationManager: FloatingHistoryAnimationManager

    // MARK: - State

    @State private var isPressed: Bool = false
    @State private var isHovered: Bool = false

    // MARK: - Design Constants

    private let activeBackgroundColor = DesignSystem.brandColors.persianPurple
    private let activeTextColor = DesignSystem.brandColors.alabaster
    private let inactiveTextColor = DesignSystem.brandColors.orchid.opacity(0.7)
    private let hoverTextColor = DesignSystem.brandColors.persianPurple
    private let hoverBackgroundColor = DesignSystem.brandColors.persianPurple.opacity(0.1)
    private let cornerRadius: CGFloat = 32
    private let horizontalPadding: CGFloat = 16
    private let verticalPadding: CGFloat = 8
    private let minimumTouchTarget: CGFloat = 44
    
    // MARK: - Body
    
    var body: some View {
        Button(action: {
            // Ensure UI updates happen on main thread with immediate feedback
            Task { @MainActor in
                HapticFeedbackManager.shared.selectionChanged()
                onTap()
            }
        }) {
            Text(tab.displayName)
                .font(DesignSystem.typography.buttonLabel)
                .fontWeight(isSelected ? .semibold : .medium)
                .foregroundColor(textColor)
                .padding(.horizontal, horizontalPadding)
                .padding(.vertical, verticalPadding)
                .background(
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(backgroundColor)
                        .overlay(
                            RoundedRectangle(cornerRadius: cornerRadius)
                                .stroke(borderColor, lineWidth: borderWidth)
                        )
                        .shadow(
                            color: shadowColor,
                            radius: shadowRadius,
                            x: 0,
                            y: shadowOffset
                        )
                )
                .scaleEffect(buttonScale)
        }
        .buttonStyle(PlainButtonStyle())
        .frame(minWidth: minimumTouchTarget, minHeight: minimumTouchTarget)
        .contentShape(Rectangle()) // Ensure entire frame is tappable
        .onHover { hovering in
            withAnimation(animationManager.getHoverFeedbackAnimation()) {
                isHovered = hovering
            }
        }
        .animation(animationManager.getSelectionFeedbackAnimation(), value: isSelected)
        .animation(animationManager.getHoverFeedbackAnimation(), value: isHovered)
        .accessibilityLabel(tab.displayName)
        .accessibilityHint("Double tap to switch to \(tab.displayName) tab")
        .accessibilityAddTraits(isSelected ? .isSelected : [])
    }

    // MARK: - Computed Properties

    private var textColor: Color {
        if isSelected {
            return activeTextColor
        } else if isHovered {
            return hoverTextColor
        } else {
            return inactiveTextColor
        }
    }

    private var backgroundColor: Color {
        if isSelected {
            return activeBackgroundColor
        } else if isHovered {
            return hoverBackgroundColor
        } else {
            return Color.clear
        }
    }

    private var borderColor: Color {
        if isSelected {
            return activeBackgroundColor
        } else if isHovered {
            return DesignSystem.brandColors.persianPurple.opacity(0.3)
        } else {
            return Color.clear
        }
    }

    private var borderWidth: CGFloat {
        return (isHovered && !isSelected) ? 1.0 : 0.0
    }

    private var shadowColor: Color {
        if isSelected {
            return DesignSystem.brandColors.persianPurple.opacity(0.25)
        } else {
            return Color.clear
        }
    }

    private var shadowRadius: CGFloat {
        return isSelected ? 3.0 : 0.0
    }

    private var shadowOffset: CGFloat {
        return isSelected ? 1.5 : 0.0
    }

    private var buttonScale: CGFloat {
        if isPressed {
            return 0.95
        } else if isHovered && !isSelected {
            return 1.02
        } else {
            return 1.0
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        // Selected state
        FloatingTabButton(
            tab: .recents,
            isSelected: true,
            onTap: {},
            animationManager: FloatingHistoryAnimationManager()
        )

        // Unselected state
        FloatingTabButton(
            tab: .favorites,
            isSelected: false,
            onTap: {},
            animationManager: FloatingHistoryAnimationManager()
        )
        
        // All tabs in a row
        HStack(spacing: 0) {
            ForEach([HistoryTab.recents, .favorites, .saved], id: \.self) { tab in
                FloatingTabButton(
                    tab: tab,
                    isSelected: tab == .recents,
                    onTap: {},
                    animationManager: FloatingHistoryAnimationManager()
                )
            }
        }
        .padding()
        .background(DesignSystem.brandColors.adaptiveBackground.opacity(0.95))
        .background(.ultraThinMaterial)
        .cornerRadius(12)
        .shadow(
            color: DesignSystem.brandColors.persianPurple.opacity(0.15),
            radius: 8,
            x: 0,
            y: 4
        )
    }
    .padding()
    .background(DesignSystem.brandColors.adaptiveBackgroundGradient)
}
