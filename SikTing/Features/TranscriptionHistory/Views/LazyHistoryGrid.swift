//
//  LazyHistoryGrid.swift
//  SikTing
//
//  Created by Augment Agent on 2025/1/25.
//

import SwiftUI

/// Performance-optimized lazy loading grid for history sessions
/// Implements viewport-based loading and memory management
struct LazyHistoryGrid: View {
    
    // MARK: - Properties
    
    let sessions: [HistorySession]
    let selectedTab: HistoryTab
    let onSessionTap: (HistorySession) -> Void
    let onFavoriteToggle: (HistorySession) -> Void
    let onSaveToggle: (HistorySession) -> Void
    let onDelete: (HistorySession) -> Void
    let onVisibleRangeChanged: (Int, Int) -> Void
    
    // MARK: - State

    // Performance optimizer removed for simplicity
    @State private var visibleItems: Set<Int> = []
    @State private var loadedSessions: [Int: HistorySession] = [:]
    @State private var geometryUpdateTimer: Timer?
    @State private var cleanupTimers: [Int: Timer] = [:]

    // Animation state
    @State private var previousSessionCount: Int = 0
    @State private var previousTab: HistoryTab = .recents
    @StateObject private var animationManager = FloatingHistoryAnimationManager()
    
    // MARK: - Design Constants
    
    private let columns = [
        GridItem(.flexible(), spacing: DesignSystem.spacing.medium),
        GridItem(.flexible(), spacing: DesignSystem.spacing.medium)
    ]
    
    private let itemSpacing: CGFloat = DesignSystem.spacing.medium
    private let bottomPadding: CGFloat = 120 // Account for floating tab bar
    
    var body: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVGrid(columns: columns, spacing: itemSpacing) {
                    ForEach(Array(sessions.enumerated()), id: \.element.id) { index, session in
                        HistorySessionCard(
                            session: session,
                            onTap: { onSessionTap(session) },
                            onFavoriteToggle: { onFavoriteToggle(session) },
                            onSaveToggle: { onSaveToggle(session) },
                            onDelete: { onDelete(session) }
                        )
                        .transition(.asymmetric(
                            insertion: .scale(scale: 0.8).combined(with: .opacity).combined(with: .move(edge: .bottom)),
                            removal: .scale(scale: 0.8).combined(with: .opacity).combined(with: .move(edge: .top))
                        ))
                        .animation(
                            DesignSystem.animations.cardEntrance.delay(Double(index) * 0.05),
                            value: sessions.count
                        )
                        .animation(
                            DesignSystem.animations.tabSwitch,
                            value: selectedTab
                        )
                        .onAppear {
                            handleItemAppear(index: index, session: session)
                        }
                        .onDisappear {
                            handleItemDisappear(index: index)
                        }
                    }
                }
                .padding(.horizontal, DesignSystem.spacing.medium)
                .padding(.bottom, bottomPadding)
            }
            .background(
                GeometryReader { geometry in
                    Color.clear
                        .onAppear {
                            updateVisibleRange(geometry: geometry)
                        }
                        .onChange(of: geometry.frame(in: .global)) {
                            // Debounce geometry updates to improve scrolling performance
                            geometryUpdateTimer?.invalidate()
                            geometryUpdateTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: false) { _ in
                                updateVisibleRange(geometry: geometry)
                            }
                        }
                }
            )
        }
        .onAppear {
            previousSessionCount = sessions.count
            previousTab = selectedTab
        }
        .onChange(of: sessions.count) { oldCount, newCount in
            handleSessionCountChange(from: oldCount, to: newCount)
        }
        .onChange(of: selectedTab) { oldTab, newTab in
            handleTabChange(from: oldTab, to: newTab)
        }
        // Performance optimization monitoring removed
    }
    
    // MARK: - Helper Methods
    
    /// Handles when an item appears in the viewport
    private func handleItemAppear(index: Int, session: HistorySession) {
        visibleItems.insert(index)
        loadedSessions[index] = session
        if session.id != nil {
            // Performance optimization removed
        }
        
        // Update visible range
        updateVisibleRangeFromItems()
    }
    
    /// Handles when an item disappears from the viewport
    private func handleItemDisappear(index: Int) {
        visibleItems.remove(index)

        // Cancel any existing cleanup timer for this index
        cleanupTimers[index]?.invalidate()

        // Keep session loaded for a while to avoid reload flicker
        cleanupTimers[index] = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: false) { _ in
            if !visibleItems.contains(index) {
                loadedSessions.removeValue(forKey: index)
            }
            cleanupTimers.removeValue(forKey: index)
        }

        updateVisibleRangeFromItems()
    }

    // MARK: - Animation Methods

    /// Handles session count changes with smooth animations
    private func handleSessionCountChange(from oldCount: Int, to newCount: Int) {
        guard oldCount != newCount else { return }

        if newCount > oldCount {
            // Items were added
            animationManager.animateContentEntrance(
                sessionCount: newCount,
                animationKey: "item_insertion"
            )
        } else {
            // Items were removed
            animationManager.animateContentRemoval(
                sessionCount: oldCount,
                animationKey: "item_removal"
            )
        }

        previousSessionCount = newCount
    }

    /// Handles tab changes with smooth transitions
    private func handleTabChange(from oldTab: HistoryTab, to newTab: HistoryTab) {
        guard oldTab != newTab else { return }

        animationManager.animateTabSwitch(
            from: oldTab,
            to: newTab,
            sessionCount: sessions.count
        )

        previousTab = newTab
    }


    /// Updates visible range based on currently visible items
    private func updateVisibleRangeFromItems() {
        guard !visibleItems.isEmpty else { return }

        let minIndex = visibleItems.min() ?? 0
        let maxIndex = visibleItems.max() ?? 0

        // Ensure valid range (should always be true, but adding safety check)
        guard minIndex <= maxIndex else { return }

        onVisibleRangeChanged(minIndex, maxIndex)
    }
    
    /// Updates visible range based on scroll geometry
    private func updateVisibleRange(geometry: GeometryProxy) {
        let frame = geometry.frame(in: .global)
        let itemHeight: CGFloat = 200 // Approximate session card height
        let itemsPerRow = 2

        let visibleStartRow = max(0, Int(frame.minY / itemHeight))
        let visibleEndRow = Int(frame.maxY / itemHeight) + 1

        let startIndex = visibleStartRow * itemsPerRow
        let endIndex = min(sessions.count, visibleEndRow * itemsPerRow)

        // Ensure valid range: startIndex <= endIndex
        let validStartIndex = min(startIndex, sessions.count)
        let validEndIndex = max(validStartIndex, endIndex)

        onVisibleRangeChanged(validStartIndex, validEndIndex)
    }
    
    // Performance optimization functions removed for simplicity
}



// MARK: - Preview

#Preview {
    LazyHistoryGrid(
        sessions: [],
        selectedTab: .recents,
        onSessionTap: { _ in },
        onFavoriteToggle: { _ in },
        onSaveToggle: { _ in },
        onDelete: { _ in },
        onVisibleRangeChanged: { _, _ in }
    )
}
