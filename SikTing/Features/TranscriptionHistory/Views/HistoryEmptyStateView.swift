//
//  HistoryEmptyStateView.swift
//  SikTing
//
//  Created by Augment Agent on 2025-07-21.
//

import SwiftUI

/// View for displaying empty states in the transcription history
struct HistoryEmptyStateView: View {

    // MARK: - Properties

    let type: HistoryEmptyStateType
    let onAction: (() -> Void)?

    // Design constants
    private let iconSize: CGFloat = 80
    private let spacing: CGFloat = 24
    private let buttonHeight: CGFloat = 50

    // MARK: - Initialization

    init(type: HistoryEmptyStateType, onAction: (() -> Void)? = nil) {
        self.type = type
        self.onAction = onAction
    }
    
    var body: some View {
        VStack(spacing: spacing) {
            Spacer()
            
            // Icon
            Image(systemName: type.iconName)
                .font(.system(size: iconSize, weight: .light))
                .foregroundColor(.secondary)
                .padding(.bottom, 8)
            
            // Title
            Text(type.title)
                .font(.scaledFont(.title2))
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
                .dynamicTypeSize(...DynamicTypeSize.accessibility3)
                .accessibilityAddTraits(AccessibilityHelper.Trait.header)

            // Description
            Text(type.description)
                .font(.scaledFont(.body))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 32)
                .lineLimit(nil)
                .dynamicTypeSize(...DynamicTypeSize.accessibility3)
            
            // Action button (if available)
            if let actionTitle = type.actionTitle, let onAction = onAction {
                Button(action: onAction) {
                    HStack {
                        if let actionIcon = type.actionIcon {
                            Image(systemName: actionIcon)
                                .font(.body)
                        }
                        
                        Text(actionTitle)
                            .font(.scaledFont(.body))
                            .fontWeight(.medium)
                            .dynamicTypeSize(...DynamicTypeSize.accessibility2)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: buttonHeight)
                    .background(Color.brandPrimary)
                    .cornerRadius(12)
                }
                .padding(.horizontal, 48)
                .padding(.top, 16)
                .minimumTouchTarget()
                .accessibilityLabel(actionTitle)
                .accessibilityAddTraits(AccessibilityHelper.Trait.button)
            }
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

// MARK: - History Empty State Types (using types from HistoryStateTypes.swift)

// MARK: - Loading State View

struct HistoryLoadingStateView: View {
    @State private var isAnimating = false
    
    var body: some View {
        VStack(spacing: 24) {
            // Animated loading icon
            Image(systemName: "waveform.circle")
                .font(.system(size: 60, weight: .light))
                .foregroundColor(.blue)
                .rotationEffect(.degrees(isAnimating ? 360 : 0))
                .animation(.linear(duration: 2).repeatForever(autoreverses: false), value: isAnimating)
                .onAppear {
                    isAnimating = true
                }
            
            Text("Loading Transcriptions...")
                .font(.title3)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            Text("Please wait while we fetch your data")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

// MARK: - Error State View

struct HistoryErrorStateView: View {
    let error: HistoryError
    let onRetry: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            // Error icon
            Image(systemName: error.iconName)
                .font(.system(size: 60, weight: .light))
                .foregroundColor(.red)
            
            // Error title
            Text(error.title)
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
            
            // Error description
            Text(error.description)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 32)
            
            // Retry button
            Button(action: onRetry) {
                HStack {
                    Image(systemName: "arrow.clockwise")
                        .font(.body)
                    
                    Text("Try Again")
                        .font(.body)
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(Color.orange)
                .cornerRadius(12)
            }
            .padding(.horizontal, 48)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

// MARK: - History Error Types (using types from HistoryStateTypes.swift)

// MARK: - Preview

#Preview("No History") {
    HistoryEmptyStateView(type: .noHistory) {
        print("Start recording tapped")
    }
}

#Preview("No Search Results") {
    HistoryEmptyStateView(type: .noSearchResults("meeting")) {
        print("Clear search tapped")
    }
}

#Preview("Loading State") {
    HistoryLoadingStateView()
}

#Preview("Error State") {
    HistoryErrorStateView(error: .networkUnavailable) {
        print("Retry tapped")
    }
}
