//
//  HistoryDetailView.swift
//  SikTing
//
//  Created by Augment Agent on 2025-07-21.
//

import SwiftUI

/// Detailed view for displaying a complete transcription session
struct HistoryDetailView: View {
    
    // MARK: - Properties
    
    let session: HistorySession
    @StateObject private var actionService = HistoryActionService()
    @StateObject private var storageService = HistoryStorageService()
    @Environment(\.dismiss) private var dismiss
    @Environment(\.hapticFeedback) private var hapticFeedback

    @State private var showingShareSheet = false
    @State private var showingDeleteAlert = false
    @State private var showingExportOptions = false
    @State private var showingTaggingView = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: DesignSystem.spacing.medium) {
                    // Session Header
                    SessionHeaderView(session: session)
                        .padding(.horizontal, DesignSystem.spacing.medium)

                    // Transcription Entries (without subtitle)
                    TranscriptionEntriesView(session: session)
                        .padding(.horizontal, DesignSystem.spacing.medium)
                }
                .padding(.top, 8)
                .padding(.bottom, 100) // Extra bottom padding for safe area
            }
            .navigationTitle("Note Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    // Menu button (only visible toolbar item)
                    Menu {
                        Button(action: {
                            hapticFeedback.buttonPressed()
                            actionService.copyToClipboard(session: session)
                        }) {
                            Label("Copy All Text", systemImage: "doc.on.clipboard")
                        }

                        Button(action: {
                            hapticFeedback.exportStarted()
                            showingExportOptions = true
                        }) {
                            Label("Export Note", systemImage: "square.and.arrow.down")
                        }

                        Button(action: {
                            hapticFeedback.buttonPressed()
                            showingShareSheet = true
                        }) {
                            Label("Share", systemImage: "square.and.arrow.up")
                        }

                        Button("Delete Note", role: .destructive) {
                            showingDeleteAlert = true
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                            .foregroundColor(.brandPersianPurple)
                    }
                }
            }
            .alert("Delete Note", isPresented: $showingDeleteAlert) {
                Button("Cancel", role: .cancel) { }
                Button("Delete", role: .destructive) {
                    do {
                        try actionService.deleteSession(session)
                        dismiss()
                    } catch {
                        print("Failed to delete note: \(error)")
                    }
                }
            } message: {
                Text("Are you sure you want to delete this note? This action cannot be undone.")
            }
            .sheet(isPresented: $showingShareSheet) {
                if let textToShare = session.fullTranscriptionText {
                    ShareSheet(activityItems: [textToShare])
                }
            }
            .sheet(isPresented: $showingExportOptions) {
                ExportOptionsView(sessions: [session])
            }
            .sheet(isPresented: $showingTaggingView) {
                TaggingView(session: session)
            }
            .onAppear {
                // Mark session as viewed when detail view appears
                storageService.markSessionAsViewed(session)
            }
        }
    }
}

// MARK: - Session Header View

private struct SessionHeaderView: View {
    let session: HistorySession
    @StateObject private var taggingService = HistoryTaggingService()

    var body: some View {
        VStack {
            // Date and status indicators (no title)
            VStack(alignment: .leading, spacing: DesignSystem.spacing.small) {
                Text(session.formattedDate)
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                HStack(spacing: 8) {
                    if session.isFavourite {
                        Label("Favorite", systemImage: "heart.fill")
                            .font(.caption)
                            .foregroundColor(.red)
                    }

                    if session.isSaved {
                        Label("Saved", systemImage: "bookmark.fill")
                            .font(.caption)
                            .foregroundColor(.blue)
                    }

//                    if session.hasTranslations {
//                        Label("Translated", systemImage: "globe")
//                            .font(.caption)
//                            .foregroundColor(.green)
//                    }
                }
            }

            // Category and Tags
            VStack(alignment: .leading, spacing: 8) {
                // Category
                if let category = taggingService.getCategory(for: session) {
                    HStack {
                        Image(systemName: "folder")
                            .font(.caption)
                            .foregroundColor(.blue)

                        Text(category)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(4)
                    }
                }

                // Tags
                let tags = taggingService.getTags(for: session)
                if !tags.isEmpty {
                    HStack {
                        Image(systemName: "tag")
                            .font(.caption)
                            .foregroundColor(.orange)

                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 6) {
                                ForEach(tags, id: \.self) { tag in
                                    Text(tag)
                                        .font(.caption2)
                                        .fontWeight(.medium)
                                        .foregroundColor(.orange)
                                        .padding(.horizontal, 6)
                                        .padding(.vertical, 2)
                                        .background(Color.orange.opacity(0.1))
                                        .cornerRadius(4)
                                }
                            }
                            .padding(.horizontal, 1)
                        }
                    }
                }
            }
        }
    }
}

// MARK: - Session Metadata View

private struct SessionMetadataView: View {
    let session: HistorySession
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Session Information")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                MetadataCard(
                    title: "Duration",
                    value: session.formattedDuration ?? "Unknown",
                    icon: "clock"
                )
                
                MetadataCard(
                    title: "Entries",
                    value: "\(session.entryCount)",
                    icon: "text.bubble"
                )
                
                MetadataCard(
                    title: "Word Count",
                    value: "\(session.wordCount)",
                    icon: "textformat"
                )
                
                MetadataCard(
                    title: "Languages",
                    value: "\(session.detectedLanguages.count)",
                    icon: "globe"
                )
            }
        }
        .padding(.vertical, 8)
    }
}

// MARK: - Metadata Card

private struct MetadataCard: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
            
            Text(value)
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Session Action Buttons

private struct SessionActionButtons: View {
    let session: HistorySession
    let actionService: HistoryActionService
    let onShare: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Actions")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 12) {
                // Speak button
                ActionButton(
                    title: actionService.currentSpeakingSessionId == session.id && actionService.isSpeaking ? "Stop" : "Speak",
                    icon: actionService.currentSpeakingSessionId == session.id && actionService.isSpeaking ? "speaker.slash" : "speaker.wave.2",
                    color: actionService.currentSpeakingSessionId == session.id && actionService.isSpeaking ? .red : .blue
                ) {
                    if actionService.currentSpeakingSessionId == session.id && actionService.isSpeaking {
                        actionService.stopSpeaking()
                    } else {
                        actionService.speakSession(session)
                    }
                }
                
                // Copy button
                ActionButton(
                    title: "Copy",
                    icon: "doc.on.clipboard",
                    color: .green
                ) {
                    actionService.copyToClipboard(session: session)
                }
                
                // Share button
                ActionButton(
                    title: "Share",
                    icon: "square.and.arrow.up",
                    color: .orange
                ) {
                    onShare()
                }
            }
        }
        .padding(.vertical, 8)
    }
}

// MARK: - Action Button

private struct ActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(Color(.systemGray6))
            .cornerRadius(12)
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {
            // Long press action
        }
    }
}

// MARK: - Transcription Entries View

private struct TranscriptionEntriesView: View {
    let session: HistorySession

    var body: some View {
        VStack {
            if session.sortedEntries.isEmpty {
                EmptyEntriesView()
            } else {
                LazyVStack(spacing: DesignSystem.spacing.xLarge) {
                    ForEach(session.sortedEntries, id: \.id) { entry in
                        TranscriptionEntryCard(entry: entry)
                    }
                }
            }
        }
    }
}

// MARK: - Transcription Entry Card

private struct TranscriptionEntryCard: View {
    let entry: HistoryEntry

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with timestamp and metadata
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(entry.formattedTimestamp)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)

//                    if let language = entry.detectedLanguage {
//                        Text(language.uppercased())
//                            .font(.caption2)
//                            .fontWeight(.semibold)
//                            .foregroundColor(.white)
//                            .padding(.horizontal, 8)
//                            .padding(.vertical, 2)
//                            .background(Color.blue)
//                            .cornerRadius(4)
//                    }
                }

                Spacer()

//                VStack(alignment: .trailing, spacing: 4) {
//                    if entry.isFinal {
//                        Text("FINAL")
//                            .font(.caption2)
//                            .fontWeight(.bold)
//                            .foregroundColor(.green)
//                    } else {
//                        Text("PARTIAL")
//                            .font(.caption2)
//                            .fontWeight(.bold)
//                            .foregroundColor(.orange)
//                    }
//
//                    if let emotion = entry.emotion, !emotion.isEmpty {
//                        Text(emotionEmoji(for: emotion))
//                            .font(.caption)
//                    }
//                }
            }

            // Always show original text first
            if let originalText = entry.originalText, !originalText.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    // Original text section
                    VStack(alignment: .leading, spacing: 8) {
                        // Language indicator for original text
//                        if let detectedLanguage = entry.detectedLanguage {
//                            HStack {
//                                Image(systemName: "textformat")
//                                    .font(.caption)
//                                    .foregroundColor(.blue)
//
//                                Text("Original")
//                                    .font(.caption)
//                                    .fontWeight(.medium)
//                                    .foregroundColor(.blue)
//
//                                Text("(\(detectedLanguage.uppercased()))")
//                                    .font(.caption2)
//                                    .foregroundColor(.secondary)
//
//                                Spacer()
//                            }
//                        }

                        Text(originalText)
                            .font(DesignSystem.typography.bodyPrimary)
                            .foregroundColor(.primary)
                            .lineLimit(nil)
                            .multilineTextAlignment(.leading)
                            .lineSpacing(DesignSystem.spacing.micro)
                    }

                    // Translation section (always show if available)
                    if let translatedText = entry.translatedText, !translatedText.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            // Beautiful separator
                            HStack {
                                Rectangle()
                                    .fill(Color.secondary.opacity(0.3))
                                    .frame(height: 1)
                                    .frame(maxWidth: 40)

                                Spacer()
                            }

                            // Translation header
                            HStack {
                                Image(systemName: "globe")
                                    .font(.caption)
                                    .foregroundColor(.green)

                                Text("Translation")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.green)

                                if let targetLanguage = entry.targetLanguage {
                                    Text("(\(targetLanguage.uppercased()))")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                }

                                Spacer()
                            }

                            // Translation text with beautiful styling
                            Text(translatedText)
                                .font(DesignSystem.typography.bodySecondary)
                                .foregroundColor(.secondary)
                                .lineLimit(nil)
                                .multilineTextAlignment(.leading)
                                .lineSpacing(DesignSystem.spacing.micro)
                                .padding(.leading, 12)
                                .overlay(
                                    Rectangle()
                                        .fill(Color.green.opacity(0.3))
                                        .frame(width: 3)
                                        .cornerRadius(1.5),
                                    alignment: .leading
                                )
                        }
                    }
                }
            }
        }
        .background(Color(.systemBackground))
//        .cornerRadius(12)
//        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }

    private func emotionEmoji(for emotion: String) -> String {
        switch emotion.lowercased() {
        case "happy", "joy": return "😊"
        case "sad", "sadness": return "😢"
        case "angry", "anger": return "😠"
        case "surprised", "surprise": return "😲"
        case "fear", "scared": return "😨"
        case "neutral": return "😐"
        default: return "🙂"
        }
    }
}

// MARK: - Empty Entries View

private struct EmptyEntriesView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "text.bubble")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(.secondary)

            VStack(spacing: 8) {
                Text("No Entries Found")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text("This session doesn't contain any transcription entries.")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
    }
}



// MARK: - Share Sheet

struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(activityItems: activityItems, applicationActivities: nil)
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {
        // No updates needed
    }
}

// MARK: - Preview

#Preview {
    struct PreviewWrapper: View {
        var body: some View {
            // Create a simple preview showing the translation layout
            NavigationView {
                ScrollView {
                    VStack(alignment: .leading, spacing: 24) {
                        // Sample entry card to show the beautiful translation display
                        VStack(alignment: .leading, spacing: 12) {
                            // Header with timestamp and language
                            HStack {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("2:34 PM")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .foregroundColor(.secondary)

                                    Text("EN")
                                        .font(.caption2)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.white)
                                        .padding(.horizontal, 8)
                                        .padding(.vertical, 2)
                                        .background(Color.blue)
                                        .cornerRadius(4)
                                }

                                Spacer()
                            }

                            // Original text section
                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Image(systemName: "textformat")
                                        .font(.caption)
                                        .foregroundColor(.blue)

                                    Text("Original")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .foregroundColor(.blue)

                                    Text("(EN)")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)

                                    Spacer()
                                }

                                Text("We discussed the Q2 roadmap and key product initiatives. The team reviewed user feedback from the latest release and identified priority features for the next sprint.")
                                    .font(DesignSystem.typography.bodyPrimary)
                                    .foregroundColor(.primary)
                                    .lineSpacing(DesignSystem.spacing.micro)
                            }

                            // Translation section
                            VStack(alignment: .leading, spacing: 8) {
                                // Beautiful separator
                                HStack {
                                    Rectangle()
                                        .fill(Color.secondary.opacity(0.3))
                                        .frame(height: 1)
                                        .frame(maxWidth: 40)

                                    Spacer()
                                }

                                // Translation header
                                HStack {
                                    Image(systemName: "globe")
                                        .font(.caption)
                                        .foregroundColor(.green)

                                    Text("Translation")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .foregroundColor(.green)

                                    Text("(ZH)")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)

                                    Spacer()
                                }

                                // Translation text with beautiful styling
                                Text("我们讨论了第二季度路线图和关键产品计划。团队审查了最新版本的用户反馈，并确定了下一个冲刺的优先功能。")
                                    .font(DesignSystem.typography.bodySecondary)
                                    .foregroundColor(.secondary)
                                    .lineSpacing(DesignSystem.spacing.micro)
                                    .padding(.leading, 12)
                                    .overlay(
                                        Rectangle()
                                            .fill(Color.green.opacity(0.3))
                                            .frame(width: 3)
                                            .cornerRadius(1.5),
                                        alignment: .leading
                                    )
                            }
                        }
                        .padding(16)
                        .background(Color(.systemBackground))
                        .cornerRadius(12)
                        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)

                        Spacer()
                    }
                    .padding()
                }
                .navigationTitle("Note Details")
                .navigationBarTitleDisplayMode(.inline)
            }
        }
    }

    return PreviewWrapper()
}
