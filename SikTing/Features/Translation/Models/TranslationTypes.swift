//
//  TranslationTypes.swift
//  SikTing
//
//  Created by Augment Agent on 2025-07-30.
//

import Foundation

// MARK: - Translation Language

/// Supported translation languages based on Google Translate API language codes
enum TranslationLanguage: String, CaseIterable, Identifiable {
    // Major World Languages
    case english = "en"
    case spanish = "es"
    case french = "fr"
    case german = "de"
    case portuguese = "pt"
    case russian = "ru"
    case japanese = "ja"
    case korean = "ko"
    case chineseSimplified = "zh-CN"
    case chineseTraditional = "zh-TW"
    case arabic = "ar"
    case hindi = "hi"
    case italian = "it"
    case dutch = "nl"
    case vietnamese = "vi"
    case thai = "th"

    // European Languages
    case polish = "pl"
    case czech = "cs"
    case hungarian = "hu"
    case romanian = "ro"
    case bulgarian = "bg"
    case croatian = "hr"
    case serbian = "sr"
    case slovak = "sk"
    case slovenian = "sl"
    case lithuanian = "lt"
    case latvian = "lv"
    case estonian = "et"
    case finnish = "fi"
    case danish = "da"
    case swedish = "sv"
    case norwegian = "no"
    case icelandic = "is"
    case greek = "el"
    case turkish = "tr"
    case ukrainian = "uk"
    case belarusian = "be"

    // Asian Languages
    case cantonese = "yue"
    case indonesian = "id"
    case malay = "ms"
    case tagalog = "tl"
    case bengali = "bn"
    case urdu = "ur"
    case punjabi = "pa"
    case gujarati = "gu"
    case tamil = "ta"
    case telugu = "te"
    case kannada = "kn"
    case malayalam = "ml"
    case marathi = "mr"
    case nepali = "ne"
    case sinhala = "si"
    case myanmar = "my"
    case khmer = "km"
    case lao = "lo"
    case mongolian = "mn"
    case kazakh = "kk"
    case uzbek = "uz"
    case kyrgyz = "ky"
    case tajik = "tg"
    case persian = "fa"
    case pashto = "ps"
    case hebrew = "he"

    // African Languages
    case swahili = "sw"
    case amharic = "am"
    case yoruba = "yo"
    case igbo = "ig"
    case hausa = "ha"
    case somali = "so"
    case afrikaans = "af"
    case zulu = "zu"
    case xhosa = "xh"

    // American Languages
    case portuguese_brazil = "pt-BR"
    case french_canada = "fr-CA"
    case quechua = "qu"
    case guarani = "gn"
    case haitian_creole = "ht"

    // Other Languages
    case esperanto = "eo"
    case latin = "la"
    case irish = "ga"
    case welsh = "cy"
    case basque = "eu"
    case catalan = "ca"
    case galician = "gl"
    case maltese = "mt"
    
    var id: String { rawValue }
    
    var displayName: String {
        switch self {
        // Major World Languages
        case .english: return "English"
        case .spanish: return "Español"
        case .french: return "Français"
        case .german: return "Deutsch"
        case .portuguese: return "Português"
        case .russian: return "Русский"
        case .japanese: return "日本語"
        case .korean: return "한국어"
        case .chineseSimplified: return "中文 (简体)"
        case .chineseTraditional: return "中文 (繁體)"
        case .arabic: return "العربية"
        case .hindi: return "हिन्दी"
        case .italian: return "Italiano"
        case .dutch: return "Nederlands"
        case .vietnamese: return "Tiếng Việt"
        case .thai: return "ไทย"

        // European Languages
        case .polish: return "Polski"
        case .czech: return "Čeština"
        case .hungarian: return "Magyar"
        case .romanian: return "Română"
        case .bulgarian: return "Български"
        case .croatian: return "Hrvatski"
        case .serbian: return "Српски"
        case .slovak: return "Slovenčina"
        case .slovenian: return "Slovenščina"
        case .lithuanian: return "Lietuvių"
        case .latvian: return "Latviešu"
        case .estonian: return "Eesti"
        case .finnish: return "Suomi"
        case .danish: return "Dansk"
        case .swedish: return "Svenska"
        case .norwegian: return "Norsk"
        case .icelandic: return "Íslenska"
        case .greek: return "Ελληνικά"
        case .turkish: return "Türkçe"
        case .ukrainian: return "Українська"
        case .belarusian: return "Беларуская"

        // Asian Languages
        case .cantonese: return "粵語"
        case .indonesian: return "Bahasa Indonesia"
        case .malay: return "Bahasa Melayu"
        case .tagalog: return "Filipino"
        case .bengali: return "বাংলা"
        case .urdu: return "اردو"
        case .punjabi: return "ਪੰਜਾਬੀ"
        case .gujarati: return "ગુજરાતી"
        case .tamil: return "தமிழ்"
        case .telugu: return "తెలుగు"
        case .kannada: return "ಕನ್ನಡ"
        case .malayalam: return "മലയാളം"
        case .marathi: return "मराठी"
        case .nepali: return "नेपाली"
        case .sinhala: return "සිංහල"
        case .myanmar: return "မြန်မာ"
        case .khmer: return "ខ្មែរ"
        case .lao: return "ລາວ"
        case .mongolian: return "Монгол"
        case .kazakh: return "Қазақ"
        case .uzbek: return "O'zbek"
        case .kyrgyz: return "Кыргыз"
        case .tajik: return "Тоҷикӣ"
        case .persian: return "فارسی"
        case .pashto: return "پښتو"
        case .hebrew: return "עברית"

        // African Languages
        case .swahili: return "Kiswahili"
        case .amharic: return "አማርኛ"
        case .yoruba: return "Yorùbá"
        case .igbo: return "Igbo"
        case .hausa: return "Hausa"
        case .somali: return "Soomaali"
        case .afrikaans: return "Afrikaans"
        case .zulu: return "isiZulu"
        case .xhosa: return "isiXhosa"

        // American Languages
        case .portuguese_brazil: return "Português (Brasil)"
        case .french_canada: return "Français (Canada)"
        case .quechua: return "Quechua"
        case .guarani: return "Guaraní"
        case .haitian_creole: return "Kreyòl Ayisyen"

        // Other Languages
        case .esperanto: return "Esperanto"
        case .latin: return "Latina"
        case .irish: return "Gaeilge"
        case .welsh: return "Cymraeg"
        case .basque: return "Euskera"
        case .catalan: return "Català"
        case .galician: return "Galego"
        case .maltese: return "Malti"
        }
    }
    
    var flag: String {
        switch self {
        // Major World Languages
        case .english: return "🇺🇸"
        case .spanish: return "🇪🇸"
        case .french: return "🇫🇷"
        case .german: return "🇩🇪"
        case .portuguese: return "🇵🇹"
        case .russian: return "🇷🇺"
        case .japanese: return "🇯🇵"
        case .korean: return "🇰🇷"
        case .chineseSimplified: return "🇨🇳"
        case .chineseTraditional: return "🇨🇳"
        case .arabic: return "🇸🇦"
        case .hindi: return "🇮🇳"
        case .italian: return "🇮🇹"
        case .dutch: return "🇳🇱"
        case .vietnamese: return "🇻🇳"
        case .thai: return "🇹🇭"

        // European Languages
        case .polish: return "🇵🇱"
        case .czech: return "🇨🇿"
        case .hungarian: return "🇭🇺"
        case .romanian: return "🇷🇴"
        case .bulgarian: return "🇧🇬"
        case .croatian: return "🇭🇷"
        case .serbian: return "🇷🇸"
        case .slovak: return "🇸🇰"
        case .slovenian: return "🇸🇮"
        case .lithuanian: return "🇱🇹"
        case .latvian: return "🇱🇻"
        case .estonian: return "🇪🇪"
        case .finnish: return "🇫🇮"
        case .danish: return "🇩🇰"
        case .swedish: return "🇸🇪"
        case .norwegian: return "🇳🇴"
        case .icelandic: return "🇮🇸"
        case .greek: return "🇬🇷"
        case .turkish: return "🇹🇷"
        case .ukrainian: return "🇺🇦"
        case .belarusian: return "🇧🇾"

        // Asian Languages
        case .cantonese: return "🇭🇰"
        case .indonesian: return "🇮🇩"
        case .malay: return "🇲🇾"
        case .tagalog: return "🇵🇭"
        case .bengali: return "🇧🇩"
        case .urdu: return "🇵🇰"
        case .punjabi: return "🇮🇳"
        case .gujarati: return "🇮🇳"
        case .tamil: return "🇮🇳"
        case .telugu: return "🇮🇳"
        case .kannada: return "🇮🇳"
        case .malayalam: return "🇮🇳"
        case .marathi: return "🇮🇳"
        case .nepali: return "🇳🇵"
        case .sinhala: return "🇱🇰"
        case .myanmar: return "🇲🇲"
        case .khmer: return "🇰🇭"
        case .lao: return "🇱🇦"
        case .mongolian: return "🇲🇳"
        case .kazakh: return "🇰🇿"
        case .uzbek: return "🇺🇿"
        case .kyrgyz: return "🇰🇬"
        case .tajik: return "🇹🇯"
        case .persian: return "🇮🇷"
        case .pashto: return "🇦🇫"
        case .hebrew: return "🇮🇱"

        // African Languages
        case .swahili: return "🇰🇪"
        case .amharic: return "🇪🇹"
        case .yoruba: return "🇳🇬"
        case .igbo: return "🇳🇬"
        case .hausa: return "🇳🇬"
        case .somali: return "🇸🇴"
        case .afrikaans: return "🇿🇦"
        case .zulu: return "🇿🇦"
        case .xhosa: return "🇿🇦"

        // American Languages
        case .portuguese_brazil: return "🇧🇷"
        case .french_canada: return "🇨🇦"
        case .quechua: return "🇵🇪"
        case .guarani: return "🇵🇾"
        case .haitian_creole: return "🇭🇹"

        // Other Languages
        case .esperanto: return "🌍"
        case .latin: return "🏛️"
        case .irish: return "🇮🇪"
        case .welsh: return "🏴󠁧󠁢󠁷󠁬󠁳󠁿"
        case .basque: return "🇪🇸"
        case .catalan: return "🇪🇸"
        case .galician: return "🇪🇸"
        case .maltese: return "🇲🇹"
        }
    }
}


// MARK: - Translation State

/// Translation state for tracking translation progress
enum TranslationState: Equatable {
    case idle
    case translating
    case completed(String)
    case failed(String)
    
    var isTranslating: Bool {
        if case .translating = self {
            return true
        }
        return false
    }
    
    var translatedText: String? {
        if case .completed(let text) = self {
            return text
        }
        return nil
    }
    
    var errorMessage: String? {
        if case .failed(let message) = self {
            return message
        }
        return nil
    }
}

// MARK: - Google Translate Response Models

/// Google Translate API response structure
struct GoogleTranslateResponse: Codable {
    let sentences: [GoogleTranslateSentence]?
    let src: String?
    let confidence: Double?
    let spell: GoogleTranslateSpell?
    let ld_result: GoogleTranslateLanguageDetection?
}

struct GoogleTranslateSentence: Codable {
    let trans: String?
    let orig: String?
    let backend: Int?
}

struct GoogleTranslateSpell: Codable {
    let spell_res: String?
}

struct GoogleTranslateLanguageDetection: Codable {
    let srclangs: [String]?
    let srclangs_confidences: [Double]?
}

// MARK: - Translation Error

/// Translation service errors
enum TranslationError: LocalizedError {
    case emptyText
    case invalidURL
    case noData
    case decodingError(Error)
    case networkError(Error)
    case invalidResponse
    case translationFailed(String)
    case languageNotSupported
    case unsupportedLanguage(String)
    case rateLimited
    case unauthorized
    case serverError(Int)
    
    var errorDescription: String? {
        switch self {
        case .emptyText:
            return "Text cannot be empty"
        case .invalidURL:
            return "Invalid translation service URL"
        case .noData:
            return "No data received from translation service"
        case .decodingError(let error):
            return "Failed to decode translation response: \(error.localizedDescription)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .invalidResponse:
            return "Invalid response from translation service"
        case .translationFailed(let message):
            return "Translation failed: \(message)"
        case .languageNotSupported:
            return "Language not supported by translation service"
        case .unsupportedLanguage(let message):
            return "Unsupported language: \(message)"
        case .rateLimited:
            return "Translation service rate limit exceeded"
        case .unauthorized:
            return "Unauthorized access to translation service"
        case .serverError(let code):
            return "Translation service error (code: \(code))"
        }
    }
}
