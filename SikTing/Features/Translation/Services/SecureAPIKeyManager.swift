//
//  SecureAPIKeyManager.swift
//  SikTing
//
//  Created by Augment Agent on 2025/7/26.
//

import Foundation
import Security

/// Secure API key management for translation services
/// Provides multiple secure methods to store and retrieve API keys
class SecureAPIKeyManager {
    
    // MARK: - Keychain Storage (Most Secure for Production)
    
    /// Store API key securely in iOS Keychain
    static func storeAPIKey(_ key: String, for service: String) -> Bool {
        let data = key.data(using: .utf8)!
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: "api_key",
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        // Delete existing item first
        SecItemDelete(query as CFDictionary)
        
        // Add new item
        let status = SecItemAdd(query as CFDictionary, nil)
        return status == errSecSuccess
    }
    
    /// Retrieve API key from iOS Keychain
    static func getAPIKey(for service: String) -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: "api_key",
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess,
              let data = result as? Data,
              let key = String(data: data, encoding: .utf8) else {
            return nil
        }
        
        return key
    }
    
    /// Delete API key from Keychain
    static func deleteAPIKey(for service: String) -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: "api_key"
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        return status == errSecSuccess
    }
    
    // MARK: - Environment Variables (Good for Development)
    
    /// Get API key from environment variable
    static func getAPIKeyFromEnvironment(_ variableName: String) -> String? {
        return ProcessInfo.processInfo.environment[variableName]
    }
    
    /// Get API key from Info.plist (configured via build settings)
    static func getAPIKeyFromInfoPlist(_ key: String) -> String? {
        return Bundle.main.object(forInfoDictionaryKey: key) as? String
    }
    
    // MARK: - Remote Configuration (Most Secure for Production)
    
    /// Fetch API key from your secure backend server
    static func fetchAPIKeyFromServer() async throws -> String {
        // This would connect to your backend server to get the API key
        // Your server would authenticate the user and return the key
        
        guard let url = URL(string: "https://your-backend.com/api/get-translation-key") else {
            throw APIKeyError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // Add user authentication (JWT token, etc.)
        // request.setValue("Bearer \(userToken)", forHTTPHeaderField: "Authorization")
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIKeyError.serverError
        }
        
        struct APIKeyResponse: Codable {
            let apiKey: String
        }
        
        let keyResponse = try JSONDecoder().decode(APIKeyResponse.self, from: data)
        return keyResponse.apiKey
    }
    
    // MARK: - Convenience Methods
    
    /// Get Google Cloud API key using the most secure available method
    static func getGoogleCloudAPIKey() -> String {
        // Priority order: Keychain → Environment → Info.plist → Empty (for security)
        
        // 1. Try Keychain (most secure)
        if let keychainKey = getAPIKey(for: "GoogleCloudTranslation") {
            print("🔐 Using API key from Keychain")
            return keychainKey
        }
        
        // 2. Try Environment Variable (good for development)
        if let envKey = getAPIKeyFromEnvironment("GOOGLE_CLOUD_API_KEY") {
            print("🌍 Using API key from Environment")
            return envKey
        }
        
        // 3. Try Info.plist (configured via build settings)
        if let plistKey = getAPIKeyFromInfoPlist("GoogleCloudAPIKey") {
            print("📋 Using API key from Info.plist")
            return plistKey
        }
        
        // 4. Return empty for security (don't hardcode)
        print("⚠️ No API key found - configure using secure method")
        return ""
    }
    
    /// Store Google Cloud API key securely
    static func setGoogleCloudAPIKey(_ key: String) -> Bool {
        return storeAPIKey(key, for: "GoogleCloudTranslation")
    }
    
    // MARK: - Setup Helper
    
    /// One-time setup to store API key securely
    static func setupAPIKey(_ key: String) {
        if setGoogleCloudAPIKey(key) {
            print("✅ API key stored securely in Keychain")
        } else {
            print("❌ Failed to store API key in Keychain")
        }
    }
}

// MARK: - Error Types

enum APIKeyError: LocalizedError {
    case invalidURL
    case serverError
    case noKeyFound
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid server URL for API key retrieval"
        case .serverError:
            return "Server error while fetching API key"
        case .noKeyFound:
            return "No API key found in any secure storage"
        }
    }
}

// MARK: - Usage Examples

/*
// MARK: - How to Use Secure API Key Manager

// 1. One-time setup (store key securely):
SecureAPIKeyManager.setupAPIKey("your_actual_api_key_here")

// 2. Get key securely in your app:
let apiKey = SecureAPIKeyManager.getGoogleCloudAPIKey()

// 3. Use with HybridTranslationService:
let translationService = HybridTranslationService(googleCloudAPIKey: apiKey)

// 4. For server-based approach (most secure):
Task {
    do {
        let serverKey = try await SecureAPIKeyManager.fetchAPIKeyFromServer()
        let service = HybridTranslationService(googleCloudAPIKey: serverKey)
    } catch {
        print("Failed to get API key from server: \(error)")
    }
}
*/
