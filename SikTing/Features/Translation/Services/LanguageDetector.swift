//
//  LanguageDetector.swift
//  SikTing
//
//  Created by <PERSON><PERSON> on 2025/7/18.
//

import Foundation
import NaturalLanguage

/// Language-aware processing component that determines when word merging should be applied
/// based on the detected or specified language of the transcription text.
class LanguageDetector {
    
    // MARK: - Public Methods
    
    /// Determines whether word merging should be applied for the given language
    /// - Parameter language: The recognized language from transcription
    /// - Returns: True if merging should be applied, false otherwise
    func shouldApplyMerging(for language: RecognizedLanguage) -> Bool {
        return isSpaceBasedLanguage(language)
    }
    
    /// Detects the language from the provided text using iOS NaturalLanguage framework
    /// - Parameter text: The text to analyze for language detection
    /// - Returns: The detected RecognizedLanguage, defaulting to .unknown if uncertain
    func detectLanguageFromText(_ text: String) -> RecognizedLanguage {
        // Skip detection for empty or very short text
        guard !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty,
              text.count > 2 else {
            return .unknown
        }
        
        // Use iOS NaturalLanguage framework for detection
        let recognizer = NLLanguageRecognizer()
        recognizer.processString(text)
        
        guard let dominantLanguage = recognizer.dominantLanguage else {
            return .unknown
        }
        
        // Map NLLanguage to RecognizedLanguage
        return mapNLLanguageToRecognizedLanguage(dominantLanguage)
    }
    
    // MARK: - Private Methods
    
    /// Determines if a language uses spaces between words and may suffer from word splitting
    /// - Parameter language: The language to check
    /// - Returns: True if the language uses spaces and may need word merging
    private func isSpaceBasedLanguage(_ language: RecognizedLanguage) -> Bool {
        switch language {
        case .english:
            // English uses spaces and commonly suffers from ASR word splitting
            return true
        case .japanese:
            // Japanese in romanized form uses spaces and may need merging
            return true
        case .chinese, .cantonese:
            // Chinese languages don't use spaces between words, no splitting issues
            return false
        case .unknown:
            // Default to applying merging for safety when language is uncertain
            return true
        }
    }
    
    /// Maps iOS NaturalLanguage framework language codes to our RecognizedLanguage enum
    /// - Parameter nlLanguage: The NLLanguage detected by the framework
    /// - Returns: Corresponding RecognizedLanguage value
    private func mapNLLanguageToRecognizedLanguage(_ nlLanguage: NLLanguage) -> RecognizedLanguage {
        switch nlLanguage {
        case .english:
            return .english
        case .simplifiedChinese, .traditionalChinese:
            return .chinese
        case .japanese:
            return .japanese
        default:
            // Check for Cantonese using language code
            if nlLanguage.rawValue == "yue" {
                return .cantonese
            }
            
            // For any other language, default to unknown
            return .unknown
        }
    }
}