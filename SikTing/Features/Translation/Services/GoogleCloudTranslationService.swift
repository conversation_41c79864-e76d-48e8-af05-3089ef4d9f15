//
//  GoogleCloudTranslationService.swift
//  SikTing
//
//  Created by Augment Agent on 2025/7/26.
//

import Foundation

// MARK: - Google Cloud Translation Models

struct GoogleCloudTranslateRequest: Codable {
    let q: [String]  // Array of text to translate
    let source: String?  // Source language (optional for auto-detection)
    let target: String   // Target language
    let format: String?  // "text" or "html"
    
    init(text: String, source: String? = nil, target: String, format: String = "text") {
        self.q = [text]
        self.source = source == "auto" ? nil : source  // Google Cloud uses nil for auto-detection
        self.target = target
        self.format = format
    }
}

struct GoogleCloudTranslateResponse: Codable {
    let data: GoogleCloudTranslateData
}

struct GoogleCloudTranslateData: Codable {
    let translations: [GoogleCloudTranslation]
}

struct GoogleCloudTranslation: Codable {
    let translatedText: String
    let detectedSourceLanguage: String?
}

struct GoogleCloudDetectRequest: Codable {
    let q: [String]  // Array of text to detect
}

struct GoogleCloudDetectResponse: Codable {
    let data: GoogleCloudDetectData
}

struct GoogleCloudDetectData: Codable {
    let detections: [[GoogleCloudDetection]]
}

struct GoogleCloudDetection: Codable {
    let language: String
    let confidence: Double
    let isReliable: Bool
}

// MARK: - Google Cloud Translation Service

/// Google Cloud Translation API service implementation
/// Provides official Google Cloud Translation API integration with API key authentication
@MainActor
class GoogleCloudTranslationService: ObservableObject {
    
    // MARK: - Configuration
    
    /// Google Cloud Translation API configuration
    struct Config {
        let apiKey: String
        let projectId: String?  // Optional, can be inferred from API key
        let baseURL: String
        
        static let `default` = Config(
            apiKey: "", // Set your API key here or load from environment/keychain
            projectId: nil,
            baseURL: "https://translation.googleapis.com/language/translate/v2"
        )
    }
    
    // MARK: - Properties
    
    @Published var isTranslating = false
    private let config: Config
    private let session: URLSession
    
    // MARK: - Initialization
    
    init(config: Config = .default) {
        self.config = config
        
        // Configure URLSession with timeout
        let sessionConfig = URLSessionConfiguration.default
        sessionConfig.timeoutIntervalForRequest = 30.0
        sessionConfig.timeoutIntervalForResource = 60.0
        self.session = URLSession(configuration: sessionConfig)
        
        print("🌐 GoogleCloudTranslationService: Initialized with API key: \(config.apiKey.isEmpty ? "❌ Missing" : "✅ Set")")
    }
    
    // MARK: - Translation Methods
    
    /// Translate text using Google Cloud Translation API
    func translate(
        text: String,
        from sourceLanguage: String,
        to targetLanguage: String
    ) async throws -> String {
        
        guard !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw GoogleCloudTranslationError.emptyText
        }
        
        guard !config.apiKey.isEmpty else {
            throw GoogleCloudTranslationError.missingAPIKey
        }
        
        print("🔄 GoogleCloudTranslation: Translating '\(text)' from \(sourceLanguage) to \(targetLanguage)")
        
        isTranslating = true
        defer { isTranslating = false }
        
        do {
            let response = try await performTranslation(
                text: text,
                source: sourceLanguage,
                target: targetLanguage
            )
            
            guard let translation = response.data.translations.first else {
                throw GoogleCloudTranslationError.noTranslationResult
            }
            
            let translatedText = translation.translatedText
            print("✅ GoogleCloudTranslation: Translation completed: '\(translatedText)'")
            
            return translatedText
            
        } catch {
            print("❌ GoogleCloudTranslation: Translation failed: \(error)")
            throw error
        }
    }
    
    /// Detect language using Google Cloud Translation API
    func detectLanguage(text: String) async throws -> (language: String, confidence: Double) {
        guard !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw GoogleCloudTranslationError.emptyText
        }
        
        guard !config.apiKey.isEmpty else {
            throw GoogleCloudTranslationError.missingAPIKey
        }
        
        print("🔍 GoogleCloudTranslation: Detecting language for '\(text)'")
        
        do {
            let response = try await performLanguageDetection(text: text)
            
            guard let detections = response.data.detections.first,
                  let detection = detections.first else {
                throw GoogleCloudTranslationError.noDetectionResult
            }
            
            print("✅ GoogleCloudTranslation: Detected language: \(detection.language) (confidence: \(detection.confidence))")
            return (language: detection.language, confidence: detection.confidence)
            
        } catch {
            print("❌ GoogleCloudTranslation: Language detection failed: \(error)")
            throw error
        }
    }
    
    // MARK: - Private Methods
    
    private func performTranslation(
        text: String,
        source: String,
        target: String
    ) async throws -> GoogleCloudTranslateResponse {
        
        let url = URL(string: config.baseURL)!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(config.apiKey, forHTTPHeaderField: "X-Goog-Api-Key")
        
        let translateRequest = GoogleCloudTranslateRequest(
            text: text,
            source: source,
            target: target
        )
        
        request.httpBody = try JSONEncoder().encode(translateRequest)
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw GoogleCloudTranslationError.invalidResponse
        }
        
        // Handle different HTTP status codes
        switch httpResponse.statusCode {
        case 200:
            break
        case 400:
            throw GoogleCloudTranslationError.badRequest("Invalid request parameters")
        case 401:
            throw GoogleCloudTranslationError.unauthorized("Invalid API key")
        case 403:
            throw GoogleCloudTranslationError.forbidden("API access forbidden - check billing/quotas")
        case 429:
            throw GoogleCloudTranslationError.rateLimited("Rate limit exceeded")
        default:
            let responseText = String(data: data, encoding: .utf8) ?? ""
            throw GoogleCloudTranslationError.serverError(httpResponse.statusCode, responseText)
        }
        
        do {
            let translationResponse = try JSONDecoder().decode(GoogleCloudTranslateResponse.self, from: data)
            return translationResponse
        } catch {
            print("❌ GoogleCloudTranslation: Failed to decode response: \(error)")
            print("Raw response: \(String(data: data, encoding: .utf8) ?? "Unable to decode")")
            throw GoogleCloudTranslationError.decodingError(error)
        }
    }
    
    private func performLanguageDetection(text: String) async throws -> GoogleCloudDetectResponse {
        let detectURL = URL(string: "\(config.baseURL.replacingOccurrences(of: "/translate/", with: "/detect/"))")!
        var request = URLRequest(url: detectURL)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(config.apiKey, forHTTPHeaderField: "X-Goog-Api-Key")
        
        let detectRequest = GoogleCloudDetectRequest(q: [text])
        request.httpBody = try JSONEncoder().encode(detectRequest)
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw GoogleCloudTranslationError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            let responseText = String(data: data, encoding: .utf8) ?? ""
            throw GoogleCloudTranslationError.serverError(httpResponse.statusCode, responseText)
        }
        
        do {
            let detectResponse = try JSONDecoder().decode(GoogleCloudDetectResponse.self, from: data)
            return detectResponse
        } catch {
            throw GoogleCloudTranslationError.decodingError(error)
        }
    }
}

// MARK: - Error Types

enum GoogleCloudTranslationError: LocalizedError {
    case emptyText
    case missingAPIKey
    case invalidResponse
    case badRequest(String)
    case unauthorized(String)
    case forbidden(String)
    case rateLimited(String)
    case serverError(Int, String)
    case decodingError(Error)
    case noTranslationResult
    case noDetectionResult
    
    var errorDescription: String? {
        switch self {
        case .emptyText:
            return "Text cannot be empty"
        case .missingAPIKey:
            return "Google Cloud API key is missing"
        case .invalidResponse:
            return "Invalid response from Google Cloud Translation API"
        case .badRequest(let message):
            return "Bad request: \(message)"
        case .unauthorized(let message):
            return "Unauthorized: \(message)"
        case .forbidden(let message):
            return "Forbidden: \(message)"
        case .rateLimited(let message):
            return "Rate limited: \(message)"
        case .serverError(let code, let message):
            return "Server error (\(code)): \(message)"
        case .decodingError(let error):
            return "Failed to decode response: \(error.localizedDescription)"
        case .noTranslationResult:
            return "No translation result received"
        case .noDetectionResult:
            return "No language detection result received"
        }
    }
}
