//
//  AudioEngineManager.swift
//  SikTing
//
//  Created by <PERSON><PERSON> on 2025/6/28.
//

import AVFoundation
import Foundation

protocol AudioEngineManagerDelegate: AnyObject {
    func audioEngineManager(_ manager: AudioEngineManager, didCaptureAudioData data: Data)
    func audioEngineManager(_ manager: AudioEngineManager, didEncounterError error: Error)
}

class AudioEngineManager: NSObject, ObservableObject {
    weak var delegate: AudioEngineManagerDelegate?
    
    private let audioEngine = AVAudioEngine()
    private let inputNode: AVAudioInputNode
    private var audioFormat: AVAudioFormat?
    
    @Published var isRecording = false
    @Published var permissionGranted = false
    
    // Audio configuration constants
    private let sampleRate: Double = 16000.0
    private let channels: UInt32 = 1
    private let bitDepth: UInt32 = 16
    private let bufferSize: UInt32 = 1024

    // FunASR chunking configuration
    private let chunkSize = 960 // 960 samples = 60ms at 16kHz
    private var sampleBuffer: [Int16] = []
    private let sampleBufferQueue = DispatchQueue(label: "com.SikTing.samplebuffer", qos: .userInitiated)
    
    override init() {
        self.inputNode = audioEngine.inputNode
        super.init()
        setupAudioFormat()
    }
    
    private func setupAudioFormat() {
        // Create PCM format: 16-bit, mono, 16kHz
        guard let format = AVAudioFormat(
            commonFormat: .pcmFormatInt16,
            sampleRate: sampleRate,
            channels: channels,
            interleaved: false
        ) else {
            print("Failed to create audio format")
            return
        }
        
        self.audioFormat = format
    }
    
    func requestMicrophonePermission() async -> Bool {
        await withCheckedContinuation { continuation in
            AVAudioSession.sharedInstance().requestRecordPermission { granted in
                DispatchQueue.main.async {
                    self.permissionGranted = granted
                    continuation.resume(returning: granted)
                }
            }
        }
    }
    
    func startRecording() throws {
        print("🎤 AudioEngineManager: Starting recording...")

        guard permissionGranted else {
            print("❌ AudioEngineManager: Permission denied")
            throw AudioEngineError.permissionDenied
        }

        guard let audioFormat = audioFormat else {
            print("❌ AudioEngineManager: Invalid audio format")
            throw AudioEngineError.invalidAudioFormat
        }

        print("✅ AudioEngineManager: Audio format - Sample Rate: \(audioFormat.sampleRate), Channels: \(audioFormat.channelCount)")

        // Configure audio session
        try configureAudioSession()

        // Remove any existing tap first
        if inputNode.numberOfInputs > 0 {
            inputNode.removeTap(onBus: 0)
            print("🔄 AudioEngineManager: Removed existing tap")
        }

        // Install tap on input node
        inputNode.installTap(onBus: 0, bufferSize: bufferSize, format: nil) { [weak self] buffer, time in
            guard let self = self else { return }
            self.processAudioBuffer(buffer)
        }
        print("✅ AudioEngineManager: Installed audio tap with buffer size: \(bufferSize)")

        // Start the audio engine
        try audioEngine.start()
        print("✅ AudioEngineManager: Audio engine started successfully")

        DispatchQueue.main.async {
            self.isRecording = true
        }
    }
    
    func stopRecording() {
        // Stop audio engine
        if audioEngine.isRunning {
            audioEngine.stop()
        }

        // Remove tap safely
        if inputNode.numberOfInputs > 0 {
            inputNode.removeTap(onBus: 0)
        }

        DispatchQueue.main.async {
            self.isRecording = false
        }
    }
    
    private func configureAudioSession() throws {
        let audioSession = AVAudioSession.sharedInstance()
        
        try audioSession.setCategory(.record, mode: .measurement, options: [])
        try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        
        // Set preferred sample rate and buffer duration
        try audioSession.setPreferredSampleRate(sampleRate)
        try audioSession.setPreferredIOBufferDuration(0.1) // 100ms buffer
    }
    
    private func processAudioBuffer(_ buffer: AVAudioPCMBuffer) {
        guard let audioFormat = audioFormat else {
            print("❌ AudioEngineManager: No audio format available")
            return
        }

        print("🎵 AudioEngineManager: Processing audio buffer - Frame Length: \(buffer.frameLength), Format: \(buffer.format)")

        // Convert to target format if needed
        let convertedBuffer = convertToTargetFormat(buffer: buffer, targetFormat: audioFormat)

        // Convert to Int16 samples and add to buffer
        if let int16Samples = convertBufferToInt16Samples(convertedBuffer) {
            print("🔄 AudioEngineManager: Converted \(int16Samples.count) samples to Int16")
            sampleBufferQueue.async { [weak self] in
                self?.processSamples(int16Samples)
            }
        } else {
            print("❌ AudioEngineManager: Failed to convert buffer to Int16 samples")
        }
    }

    private func processSamples(_ samples: [Int16]) {
        sampleBuffer.append(contentsOf: samples)
        print("📊 AudioEngineManager: Sample buffer size: \(sampleBuffer.count), Added: \(samples.count) samples")

        // Send chunks of 960 samples
        while sampleBuffer.count >= chunkSize {
            let chunk = Array(sampleBuffer.prefix(chunkSize))
            sampleBuffer.removeFirst(chunkSize)

            // Convert Int16 array to Data
            let audioData = Data(bytes: chunk, count: chunk.count * MemoryLayout<Int16>.size)
            print("📤 AudioEngineManager: Sending audio chunk - Size: \(audioData.count) bytes (\(chunk.count) samples)")

            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.delegate?.audioEngineManager(self, didCaptureAudioData: audioData)
            }
        }
    }
    
    private func convertToTargetFormat(buffer: AVAudioPCMBuffer, targetFormat: AVAudioFormat) -> AVAudioPCMBuffer {
        // If formats match, return original buffer
        if buffer.format.sampleRate == targetFormat.sampleRate &&
           buffer.format.channelCount == targetFormat.channelCount {
            return buffer
        }
        
        // Create converter
        guard let converter = AVAudioConverter(from: buffer.format, to: targetFormat) else {
            return buffer
        }
        
        // Calculate output frame count
        let outputFrameCount = AVAudioFrameCount(
            Double(buffer.frameLength) * targetFormat.sampleRate / buffer.format.sampleRate
        )
        
        guard let outputBuffer = AVAudioPCMBuffer(
            pcmFormat: targetFormat,
            frameCapacity: outputFrameCount
        ) else {
            return buffer
        }
        
        var error: NSError?
        let status = converter.convert(to: outputBuffer, error: &error) { _, outStatus in
            outStatus.pointee = .haveData
            return buffer
        }
        
        if status == .error {
            print("Audio conversion error: \(error?.localizedDescription ?? "Unknown")")
            return buffer
        }
        
        return outputBuffer
    }
    


    private func convertBufferToInt16Samples(_ buffer: AVAudioPCMBuffer) -> [Int16]? {
        guard let channelData = buffer.int16ChannelData else { return nil }

        let frameLength = Int(buffer.frameLength)
        let channelCount = Int(buffer.format.channelCount)

        var samples: [Int16] = []
        samples.reserveCapacity(frameLength * channelCount)

        // Convert to mono by taking first channel or averaging if stereo
        if channelCount == 1 {
            // Mono - direct copy
            for frame in 0..<frameLength {
                samples.append(channelData[0][frame])
            }
        } else {
            // Multi-channel - average to mono
            for frame in 0..<frameLength {
                var sum: Int32 = 0
                for channel in 0..<channelCount {
                    sum += Int32(channelData[channel][frame])
                }
                let average = Int16(sum / Int32(channelCount))
                samples.append(average)
            }
        }

        return samples
    }

    deinit {
        if isRecording {
            stopRecording()
        }
    }
}

// MARK: - Error Types
enum AudioEngineError: LocalizedError {
    case permissionDenied
    case invalidAudioFormat
    case engineStartFailed
    
    var errorDescription: String? {
        switch self {
        case .permissionDenied:
            return "Microphone permission denied"
        case .invalidAudioFormat:
            return "Invalid audio format configuration"
        case .engineStartFailed:
            return "Failed to start audio engine"
        }
    }
}
