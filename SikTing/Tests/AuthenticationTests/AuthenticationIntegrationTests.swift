//
//  AuthenticationIntegrationTests.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import XCTest
import Combine
@testable import SikTing

@MainActor
final class AuthenticationIntegrationTests: XCTestCase {
    
    // MARK: - Properties
    
    var authService: MockSecureAuthenticationService!
    var webSocketManager: MockWebSocketManager!
    var speechRecognitionViewModel: SpeechRecognitionViewModel!
    var sessionPersistence: MockSessionPersistenceService!
    var cancellables: Set<AnyCancellable>!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        authService = MockSecureAuthenticationService()
        webSocketManager = MockWebSocketManager()
        speechRecognitionViewModel = SpeechRecognitionViewModel()
        sessionPersistence = MockSessionPersistenceService()
        cancellables = Set<AnyCancellable>()
        
        // Setup integration
        speechRecognitionViewModel.setAuthenticationService(authService)
    }
    
    override func tearDown() {
        cancellables.removeAll()
        speechRecognitionViewModel = nil
        sessionPersistence = nil
        webSocketManager = nil
        authService = nil
        super.tearDown()
    }
    
    // MARK: - Complete Authentication Flow Tests
    
    func testCompleteSignInToWebSocketFlow() async {
        // Given
        authService.shouldSucceedSignIn = true
        authService.shouldSucceedTokenExchange = true
        webSocketManager.shouldSucceedConnection = true
        
        let authExpectation = XCTestExpectation(description: "Authentication completes")
        let connectionExpectation = XCTestExpectation(description: "WebSocket connects")
        
        // Monitor authentication state
        authService.$authState
            .sink { state in
                if state.isAuthenticated {
                    authExpectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // Monitor WebSocket connection
        webSocketManager.$connectionState
            .sink { state in
                if case .connected = state {
                    connectionExpectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        await authService.signInWithApple()
        
        // Simulate WebSocket connection with token
        if let accessToken = authService.authState.accessToken {
            webSocketManager.connectWithToken(
                accessToken: accessToken,
                to: "wss://test.example.com",
                with: nil,
                authService: authService
            )
        }
        
        // Then
        await fulfillment(of: [authExpectation, connectionExpectation], timeout: 3.0)
        
        XCTAssertTrue(authService.authState.isAuthenticated)
        XCTAssertNotNil(authService.userInfo)
        XCTAssertEqual(webSocketManager.connectionState, .connected)
        XCTAssertEqual(webSocketManager.connectWithTokenCallCount, 1)
        XCTAssertNotNil(webSocketManager.lastAccessToken)
    }
    
    func testAuthenticationFailureToWebSocketFlow() async {
        // Given
        authService.shouldSucceedSignIn = false
        authService.mockError = .appleSignInFailed("Test failure")
        
        let authFailureExpectation = XCTestExpectation(description: "Authentication fails")
        
        authService.$authState
            .sink { state in
                if case .failed = state {
                    authFailureExpectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        await authService.signInWithApple()
        
        // Then
        await fulfillment(of: [authFailureExpectation], timeout: 2.0)
        
        XCTAssertFalse(authService.authState.isAuthenticated)
        XCTAssertNil(authService.userInfo)
        XCTAssertEqual(webSocketManager.connectWithTokenCallCount, 0) // Should not attempt connection
    }
    
    // MARK: - Session Persistence Integration Tests
    
    func testSessionPersistenceFlow() async {
        // Given
        sessionPersistence.shouldSucceedSave = true
        sessionPersistence.shouldSucceedLoad = true
        authService.shouldSucceedSignIn = true
        
        // When - Sign in and save session
        await authService.signInWithApple()
        
        let userInfo = authService.userInfo!
        let sessionSaved = sessionPersistence.saveSession(userInfo)
        
        // Simulate app restart
        authService.reset()
        
        // Load session
        let loadedUserInfo = sessionPersistence.loadSession()
        
        // Then
        XCTAssertTrue(sessionSaved)
        XCTAssertNotNil(loadedUserInfo)
        XCTAssertEqual(loadedUserInfo?.userID, userInfo.userID)
        XCTAssertEqual(loadedUserInfo?.accessToken, userInfo.accessToken)
    }
    
    func testSessionExpirationFlow() async {
        // Given
        sessionPersistence.simulateExpiredSession()
        
        // When
        let loadedUserInfo = sessionPersistence.loadSession()
        
        // Then
        XCTAssertNotNil(loadedUserInfo)
        XCTAssertFalse(loadedUserInfo!.isValid)
        XCTAssertTrue(loadedUserInfo!.needsRefresh)
        
        // Should trigger re-authentication
        authService.userInfo = loadedUserInfo
        await authService.refreshTokenIfNeeded()
        
        XCTAssertEqual(authService.refreshTokenCallCount, 1)
    }
    
    // MARK: - Token Expiration and Refresh Tests
    
    func testTokenExpirationDuringWebSocketConnection() async {
        // Given
        authService.shouldSucceedSignIn = true
        webSocketManager.shouldSucceedConnection = true
        
        // Authenticate first
        await authService.signInWithApple()
        
        // Connect WebSocket
        if let accessToken = authService.authState.accessToken {
            webSocketManager.connectWithToken(
                accessToken: accessToken,
                to: "wss://test.example.com",
                with: nil,
                authService: authService
            )
        }
        
        let disconnectionExpectation = XCTestExpectation(description: "WebSocket disconnects on token expiration")
        
        webSocketManager.$connectionState
            .sink { state in
                if case .disconnected(let reason) = state, reason == .authenticationFailed {
                    disconnectionExpectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When - Simulate token expiration
        webSocketManager.simulateTokenExpiration()
        
        // Then
        await fulfillment(of: [disconnectionExpectation], timeout: 2.0)
        
        if case .disconnected(let reason) = webSocketManager.connectionState {
            XCTAssertEqual(reason, .authenticationFailed)
        } else {
            XCTFail("Expected disconnected state with authentication failure")
        }
    }
    
    // MARK: - Error Recovery Tests
    
    func testNetworkErrorRecoveryFlow() async {
        // Given
        authService.simulateNetworkError()
        
        let initialErrorExpectation = XCTestExpectation(description: "Initial network error")
        let recoveryExpectation = XCTestExpectation(description: "Recovery after network restored")
        
        var errorCount = 0
        
        authService.$lastError
            .compactMap { $0 }
            .sink { error in
                errorCount += 1
                if errorCount == 1 {
                    if case .networkError = error {
                        initialErrorExpectation.fulfill()
                    }
                }
            }
            .store(in: &cancellables)
        
        authService.$authState
            .sink { state in
                if state.isAuthenticated {
                    recoveryExpectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When - Initial failure
        await authService.signInWithApple()
        
        await fulfillment(of: [initialErrorExpectation], timeout: 2.0)
        
        // When - Network restored
        authService.reset()
        authService.shouldSucceedSignIn = true
        authService.shouldSucceedTokenExchange = true
        
        await authService.signInWithApple()
        
        // Then
        await fulfillment(of: [recoveryExpectation], timeout: 2.0)
        
        XCTAssertTrue(authService.authState.isAuthenticated)
        XCTAssertNotNil(authService.userInfo)
    }
    
    // MARK: - Background/Foreground Handling Tests
    
    func testBackgroundForegroundSessionHandling() {
        // Given
        sessionPersistence.simulateValidSession()
        
        // When - App goes to background
        sessionPersistence.handleAppDidEnterBackground()
        
        // When - App returns to foreground
        let sessionValid = sessionPersistence.handleAppWillEnterForeground()
        
        // Then
        XCTAssertTrue(sessionValid)
        XCTAssertTrue(sessionPersistence.validateSessionAfterBackground())
    }
    
    func testLongBackgroundSessionInvalidation() {
        // Given
        sessionPersistence.simulateExpiredSession()
        
        // When - App returns to foreground after long background time
        let sessionValid = sessionPersistence.handleAppWillEnterForeground()
        
        // Then
        XCTAssertFalse(sessionValid)
        XCTAssertFalse(sessionPersistence.validateSessionAfterBackground())
    }
    
    // MARK: - Onboarding Integration Tests
    
    func testOnboardingToAuthenticationFlow() async {
        // Given
        authService.isFirstLaunch = true
        authService.shouldSucceedSignIn = true
        
        let onboardingCompletionExpectation = XCTestExpectation(description: "Onboarding completes")
        
        authService.$isFirstLaunch
            .sink { isFirstLaunch in
                if !isFirstLaunch {
                    onboardingCompletionExpectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When - Complete onboarding with authentication
        await authService.signInWithApple()
        authService.completeOnboarding()
        
        // Then
        await fulfillment(of: [onboardingCompletionExpectation], timeout: 2.0)
        
        XCTAssertFalse(authService.isFirstLaunch)
        XCTAssertTrue(authService.authState.isAuthenticated)
        XCTAssertNotNil(authService.userInfo)
    }
    
    // MARK: - Sign Out Integration Tests
    
    func testCompleteSignOutFlow() async {
        // Given - User is authenticated
        authService.shouldSucceedSignIn = true
        await authService.signInWithApple()
        
        let userInfo = authService.userInfo!
        sessionPersistence.saveSession(userInfo)
        
        // Connect WebSocket
        webSocketManager.connectWithToken(
            accessToken: userInfo.accessToken,
            to: "wss://test.example.com",
            with: nil,
            authService: authService
        )
        
        let signOutExpectation = XCTestExpectation(description: "Complete sign out")
        
        authService.$authState
            .sink { state in
                if case .notAuthenticated = state {
                    signOutExpectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When - Sign out
        authService.signOut()
        sessionPersistence.clearSession()
        webSocketManager.disconnect(reason: .userInitiated)
        
        // Then
        await fulfillment(of: [signOutExpectation], timeout: 2.0)
        
        XCTAssertEqual(authService.authState, .notAuthenticated)
        XCTAssertNil(authService.userInfo)
        XCTAssertNil(sessionPersistence.loadSession())
        XCTAssertEqual(webSocketManager.disconnectCallCount, 1)
    }
}
