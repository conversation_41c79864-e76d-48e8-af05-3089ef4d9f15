//
//  SecureAuthenticationServiceTests.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import XCTest
import Combine
@testable import SikTing

@MainActor
final class SecureAuthenticationServiceTests: XCTestCase {
    
    // MARK: - Properties
    
    var authService: MockSecureAuthenticationService!
    var cancellables: Set<AnyCancellable>!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        authService = MockSecureAuthenticationService()
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        cancellables.removeAll()
        authService = nil
        super.tearDown()
    }
    
    // MARK: - Authentication State Tests
    
    func testInitialState() {
        XCTAssertEqual(authService.authState, .notAuthenticated)
        XCTAssertNil(authService.userInfo)
        XCTAssertFalse(authService.isLoading)
        XCTAssertNil(authService.lastError)
    }
    
    func testSuccessfulSignIn() async {
        // Given
        authService.shouldSucceedSignIn = true
        authService.shouldSucceedTokenExchange = true
        
        let expectation = XCTestExpectation(description: "Sign in completes")
        
        authService.$authState
            .dropFirst() // Skip initial state
            .sink { state in
                if case .authenticated = state {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        await authService.signInWithApple()
        
        // Then
        await fulfillment(of: [expectation], timeout: 2.0)
        
        XCTAssertTrue(authService.authState.isAuthenticated)
        XCTAssertNotNil(authService.userInfo)
        XCTAssertFalse(authService.isLoading)
        XCTAssertEqual(authService.signInCallCount, 1)
    }
    
    func testFailedSignIn() async {
        // Given
        authService.shouldSucceedSignIn = false
        authService.mockError = .appleSignInFailed("Test error")
        
        let expectation = XCTestExpectation(description: "Sign in fails")
        
        authService.$authState
            .dropFirst()
            .sink { state in
                if case .failed = state {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        await authService.signInWithApple()
        
        // Then
        await fulfillment(of: [expectation], timeout: 2.0)
        
        XCTAssertFalse(authService.authState.isAuthenticated)
        XCTAssertNil(authService.userInfo)
        XCTAssertNotNil(authService.lastError)
        XCTAssertEqual(authService.signInCallCount, 1)
    }
    
    func testSignOut() {
        // Given
        authService.userInfo = UserInfo(
            userID: "test_user",
            email: "<EMAIL>",
            accessToken: "test_token",
            expiresAt: Date().addingTimeInterval(3600)
        )
        authService.authState = .authenticated(accessToken: "test_token", expiresAt: Date().addingTimeInterval(3600))
        
        // When
        authService.signOut()
        
        // Then
        XCTAssertEqual(authService.authState, .notAuthenticated)
        XCTAssertNil(authService.userInfo)
        XCTAssertNil(authService.lastError)
        XCTAssertEqual(authService.signOutCallCount, 1)
    }
    
    // MARK: - Token Refresh Tests
    
    func testTokenRefreshWhenNeeded() async {
        // Given
        let expiredUser = UserInfo(
            userID: "test_user",
            email: "<EMAIL>",
            accessToken: "expired_token",
            expiresAt: Date().addingTimeInterval(-3600) // Expired
        )
        authService.userInfo = expiredUser
        authService.shouldSucceedTokenExchange = true
        
        // When
        await authService.refreshTokenIfNeeded()
        
        // Then
        XCTAssertEqual(authService.refreshTokenCallCount, 1)
        XCTAssertTrue(authService.authState.isAuthenticated)
        XCTAssertNotEqual(authService.userInfo?.accessToken, "expired_token")
    }
    
    func testTokenRefreshFailure() async {
        // Given
        let expiredUser = UserInfo(
            userID: "test_user",
            email: "<EMAIL>",
            accessToken: "expired_token",
            expiresAt: Date().addingTimeInterval(-3600)
        )
        authService.userInfo = expiredUser
        authService.shouldSucceedTokenExchange = false
        authService.mockError = .tokenExpired
        
        // When
        await authService.refreshTokenIfNeeded()
        
        // Then
        XCTAssertEqual(authService.refreshTokenCallCount, 1)
        XCTAssertFalse(authService.authState.isAuthenticated)
        XCTAssertNotNil(authService.lastError)
    }
    
    // MARK: - Error Handling Tests
    
    func testNetworkErrorHandling() async {
        // Given
        authService.simulateNetworkError()
        
        let expectation = XCTestExpectation(description: "Network error handled")
        
        authService.$lastError
            .compactMap { $0 }
            .sink { error in
                if case .networkError = error {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        await authService.signInWithApple()
        
        // Then
        await fulfillment(of: [expectation], timeout: 2.0)
        
        XCTAssertFalse(authService.authState.isAuthenticated)
        XCTAssertTrue(authService.errorHandler.isShowingError)
    }
    
    func testServerErrorHandling() async {
        // Given
        authService.simulateServerError()
        
        let expectation = XCTestExpectation(description: "Server error handled")
        
        authService.$lastError
            .compactMap { $0 }
            .sink { error in
                if case .serverUnavailable = error {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        await authService.signInWithApple()
        
        // Then
        await fulfillment(of: [expectation], timeout: 2.0)
        
        XCTAssertFalse(authService.authState.isAuthenticated)
        XCTAssertTrue(authService.errorHandler.isShowingError)
    }
    
    func testRateLimitErrorHandling() async {
        // Given
        authService.simulateRateLimitError()
        
        let expectation = XCTestExpectation(description: "Rate limit error handled")
        
        authService.$lastError
            .compactMap { $0 }
            .sink { error in
                if case .rateLimitExceeded = error {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        await authService.signInWithApple()
        
        // Then
        await fulfillment(of: [expectation], timeout: 2.0)
        
        XCTAssertFalse(authService.authState.isAuthenticated)
        XCTAssertTrue(authService.errorHandler.isShowingError)
    }
    
    // MARK: - State Validation Tests
    
    func testAuthStateAccessToken() {
        // Given
        let token = "test_access_token"
        let expiresAt = Date().addingTimeInterval(3600)
        
        // When
        authService.authState = .authenticated(accessToken: token, expiresAt: expiresAt)
        
        // Then
        XCTAssertEqual(authService.authState.accessToken, token)
        XCTAssertEqual(authService.authState.expiresAt, expiresAt)
        XCTAssertTrue(authService.authState.isAuthenticated)
    }
    
    func testAuthStateEquality() {
        // Given
        let token = "test_token"
        let expiresAt = Date().addingTimeInterval(3600)
        let state1 = AuthenticationState.authenticated(accessToken: token, expiresAt: expiresAt)
        let state2 = AuthenticationState.authenticated(accessToken: token, expiresAt: expiresAt)
        
        // Then
        XCTAssertEqual(state1, state2)
        XCTAssertNotEqual(state1, .notAuthenticated)
    }
    
    // MARK: - Loading State Tests
    
    func testLoadingStatesDuringSignIn() async {
        // Given
        authService.shouldSucceedSignIn = true
        
        var loadingStates: [Bool] = []
        
        authService.$isLoading
            .sink { isLoading in
                loadingStates.append(isLoading)
            }
            .store(in: &cancellables)
        
        // When
        await authService.signInWithApple()
        
        // Then
        XCTAssertTrue(loadingStates.contains(true)) // Should have been loading
        XCTAssertFalse(authService.isLoading) // Should not be loading at the end
    }
}
