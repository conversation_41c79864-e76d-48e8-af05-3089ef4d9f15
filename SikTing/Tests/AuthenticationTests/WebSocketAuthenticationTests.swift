//
//  WebSocketAuthenticationTests.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import XCTest
import Combine
@testable import SikTing

@MainActor
final class WebSocketAuthenticationTests: XCTestCase {
    
    // MARK: - Properties
    
    var mockWebSocketManager: MockWebSocketManager!
    var mockAuthService: MockSecureAuthenticationService!
    var speechRecognitionViewModel: SpeechRecognitionViewModel!
    var cancellables: Set<AnyCancellable>!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        mockWebSocketManager = MockWebSocketManager()
        mockAuthService = MockSecureAuthenticationService()
        speechRecognitionViewModel = SpeechRecognitionViewModel()
        cancellables = Set<AnyCancellable>()
        
        // Inject mock auth service
        speechRecognitionViewModel.setAuthenticationService(mockAuthService)
    }
    
    override func tearDown() {
        cancellables.removeAll()
        speechRecognitionViewModel = nil
        mockAuthService = nil
        mockWebSocketManager = nil
        super.tearDown()
    }
    
    // MARK: - Authenticated Connection Tests
    
    func testConnectWithValidToken() {
        // Given
        let testToken = "valid_access_token"
        let testURL = "wss://test.example.com"
        let testConfig = FunASRConfiguration(mode: "2pass", itn: true)
        
        // When
        mockWebSocketManager.connectWithToken(
            accessToken: testToken,
            to: testURL,
            with: testConfig,
            authService: mockAuthService
        )
        
        // Then
        XCTAssertEqual(mockWebSocketManager.connectWithTokenCallCount, 1)
        XCTAssertEqual(mockWebSocketManager.lastAccessToken, testToken)
        XCTAssertEqual(mockWebSocketManager.lastConfig?.mode, testConfig.mode)
        XCTAssertEqual(mockWebSocketManager.lastConfig?.itn, testConfig.itn)
    }
    
    func testConnectWithoutAuthentication() {
        // Given
        let testURL = "wss://test.example.com"
        let testConfig = FunASRConfiguration(mode: "2pass", itn: true)
        
        // When
        mockWebSocketManager.connect(to: testURL, with: testConfig)
        
        // Then
        XCTAssertEqual(mockWebSocketManager.connectCallCount, 1)
        XCTAssertEqual(mockWebSocketManager.connectWithTokenCallCount, 0)
        XCTAssertNil(mockWebSocketManager.lastAccessToken)
    }
    
    // MARK: - Authentication Failure Tests
    
    func testAuthenticationFailureHandling() {
        // Given
        let expectation = XCTestExpectation(description: "Authentication failure handled")
        
        mockWebSocketManager.$connectionState
            .sink { state in
                if case .disconnected(let reason) = state, reason == .authenticationFailed {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        mockWebSocketManager.simulateAuthenticationFailure()
        
        // Then
        wait(for: [expectation], timeout: 2.0)
        
        if case .disconnected(let reason) = mockWebSocketManager.connectionState {
            XCTAssertEqual(reason, .authenticationFailed)
        } else {
            XCTFail("Expected disconnected state with authentication failure")
        }
    }
    
    func testTokenExpirationHandling() {
        // Given
        let expectation = XCTestExpectation(description: "Token expiration handled")
        
        mockWebSocketManager.$connectionState
            .sink { state in
                if case .disconnected(let reason) = state, reason == .authenticationFailed {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        mockWebSocketManager.simulateTokenExpiration()
        
        // Then
        wait(for: [expectation], timeout: 2.0)
        
        if case .disconnected(let reason) = mockWebSocketManager.connectionState {
            XCTAssertEqual(reason, .authenticationFailed)
        } else {
            XCTFail("Expected disconnected state with authentication failure")
        }
    }
    
    // MARK: - URL Building Tests
    
    func testAuthenticatedURLBuilding() {
        // This test would need access to the private method
        // In a real implementation, we might make this method internal for testing
        // For now, we'll test the behavior through the public interface

        // Given
        let testToken = "test_token_123"

        // When
        mockWebSocketManager.connectWithToken(
            accessToken: testToken,
            to: "wss://test.example.com/ws",
            with: nil,
            authService: mockAuthService
        )

        // Then
        XCTAssertEqual(mockWebSocketManager.lastAccessToken, testToken)
    }
    
    func testInvalidURLHandling() {
        // Test invalid URL handling through connection failure
        // Given
        mockWebSocketManager.shouldSucceedConnection = false

        // When
        mockWebSocketManager.connectWithToken(
            accessToken: "test_token",
            to: "invalid-url",
            with: nil,
            authService: mockAuthService
        )

        // Then
        XCTAssertEqual(mockWebSocketManager.connectWithTokenCallCount, 1)
    }
    
    // MARK: - Connection State Tests
    
    func testSuccessfulAuthenticatedConnection() {
        // Given
        mockWebSocketManager.shouldSucceedConnection = true
        let expectation = XCTestExpectation(description: "Connection succeeds")
        
        mockWebSocketManager.$connectionState
            .sink { state in
                if case .connected = state {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        mockWebSocketManager.connectWithToken(
            accessToken: "valid_token",
            to: "wss://test.example.com",
            with: nil,
            authService: mockAuthService
        )
        
        // Then
        wait(for: [expectation], timeout: 2.0)
        XCTAssertEqual(mockWebSocketManager.connectionState, .connected)
    }
    
    func testFailedAuthenticatedConnection() {
        // Given
        mockWebSocketManager.shouldSucceedConnection = false
        let expectation = XCTestExpectation(description: "Connection fails")
        
        mockWebSocketManager.$connectionState
            .sink { state in
                if case .disconnected(let reason) = state, reason == .authenticationFailed {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        mockWebSocketManager.connectWithToken(
            accessToken: "invalid_token",
            to: "wss://test.example.com",
            with: nil,
            authService: mockAuthService
        )
        
        // Then
        wait(for: [expectation], timeout: 2.0)
        
        if case .disconnected(let reason) = mockWebSocketManager.connectionState {
            XCTAssertEqual(reason, .authenticationFailed)
        } else {
            XCTFail("Expected disconnected state with authentication failure")
        }
    }
    
    // MARK: - Error Classification Tests
    
    func testAuthenticationErrorClassification() {
        // Given
        let authError = SecureWebSocketError.authenticationRequired
        
        // When
        let reason = mockWebSocketManager.classifyError(authError)
        
        // Then
        XCTAssertEqual(reason, .authenticationFailed)
    }
    
    func testTokenExpiredErrorClassification() {
        // Given
        let tokenError = SecureWebSocketError.tokenExpired
        
        // When
        let reason = mockWebSocketManager.classifyError(tokenError)
        
        // Then
        XCTAssertEqual(reason, .authenticationFailed)
    }
    
    func testServerErrorClassification() {
        // Given
        let serverError = SecureWebSocketError.serverError(401, "Unauthorized")
        
        // When
        let reason = mockWebSocketManager.classifyError(serverError)
        
        // Then
        XCTAssertEqual(reason, .authenticationFailed)
    }
    
    func testNetworkErrorClassification() {
        // Given
        let networkError = URLError(.notConnectedToInternet)
        
        // When
        let reason = mockWebSocketManager.classifyError(networkError)
        
        // Then
        XCTAssertEqual(reason, .networkError)
    }
    
    // MARK: - Reconnection Logic Tests
    
    func testNoReconnectionForAuthFailure() {
        // Given
        let authFailureReason = DisconnectionReason.authenticationFailed
        
        // When
        let shouldReconnect = mockWebSocketManager.shouldAttemptReconnection(for: authFailureReason)
        
        // Then
        XCTAssertFalse(shouldReconnect)
    }
    
    func testReconnectionForNetworkError() {
        // Given
        let networkErrorReason = DisconnectionReason.networkError
        
        // When
        let shouldReconnect = mockWebSocketManager.shouldAttemptReconnection(for: networkErrorReason)
        
        // Then
        XCTAssertTrue(shouldReconnect)
    }
    
    func testReconnectionForServerError() {
        // Given
        let serverErrorReason = DisconnectionReason.serverError
        
        // When
        let shouldReconnect = mockWebSocketManager.shouldAttemptReconnection(for: serverErrorReason)
        
        // Then
        XCTAssertTrue(shouldReconnect)
    }
    
    // MARK: - Integration Tests
    
    func testAuthenticatedProperty() {
        // Given
        mockWebSocketManager.connectWithToken(
            accessToken: "test_token",
            to: "wss://test.example.com",
            with: nil,
            authService: mockAuthService
        )
        
        // Then
        XCTAssertTrue(mockWebSocketManager.isAuthenticated)
    }
    
    func testUnauthenticatedProperty() {
        // Given
        mockWebSocketManager.connect(to: "wss://test.example.com", with: nil)
        
        // Then
        XCTAssertFalse(mockWebSocketManager.isAuthenticated)
    }
}
