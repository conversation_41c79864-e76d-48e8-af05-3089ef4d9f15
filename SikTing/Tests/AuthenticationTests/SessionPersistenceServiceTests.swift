//
//  SessionPersistenceServiceTests.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import XCTest
@testable import SikTing

final class SessionPersistenceServiceTests: XCTestCase {
    
    // MARK: - Properties
    
    var mockPersistence: MockSessionPersistenceService!
    var testUserInfo: UserInfo!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        mockPersistence = MockSessionPersistenceService()
        testUserInfo = UserInfo(
            userID: "test_user_123",
            email: "<EMAIL>",
            accessToken: "test_access_token",
            expiresAt: Date().addingTimeInterval(3600)
        )
    }
    
    override func tearDown() {
        mockPersistence = nil
        testUserInfo = nil
        super.tearDown()
    }
    
    // MARK: - Session Save Tests
    
    func testSuccessfulSessionSave() {
        // Given
        mockPersistence.shouldSucceedSave = true
        
        // When
        let result = mockPersistence.saveSession(testUserInfo)
        
        // Then
        XCTAssertTrue(result)
        XCTAssertEqual(mockPersistence.saveCallCount, 1)
        XCTAssertEqual(mockPersistence.mockUserInfo?.userID, testUserInfo.userID)
        XCTAssertEqual(mockPersistence.mockUserInfo?.accessToken, testUserInfo.accessToken)
    }
    
    func testFailedSessionSave() {
        // Given
        mockPersistence.shouldSucceedSave = false
        
        // When
        let result = mockPersistence.saveSession(testUserInfo)
        
        // Then
        XCTAssertFalse(result)
        XCTAssertEqual(mockPersistence.saveCallCount, 1)
    }
    
    // MARK: - Session Load Tests
    
    func testSuccessfulSessionLoad() {
        // Given
        mockPersistence.shouldSucceedLoad = true
        mockPersistence.mockUserInfo = testUserInfo
        
        // When
        let loadedUserInfo = mockPersistence.loadSession()
        
        // Then
        XCTAssertNotNil(loadedUserInfo)
        XCTAssertEqual(loadedUserInfo?.userID, testUserInfo.userID)
        XCTAssertEqual(loadedUserInfo?.accessToken, testUserInfo.accessToken)
        XCTAssertEqual(mockPersistence.loadCallCount, 1)
    }
    
    func testFailedSessionLoad() {
        // Given
        mockPersistence.shouldSucceedLoad = false
        
        // When
        let loadedUserInfo = mockPersistence.loadSession()
        
        // Then
        XCTAssertNil(loadedUserInfo)
        XCTAssertEqual(mockPersistence.loadCallCount, 1)
    }
    
    func testLoadExpiredSession() {
        // Given
        mockPersistence.simulateExpiredSession()
        
        // When
        let loadedUserInfo = mockPersistence.loadSession()
        
        // Then
        XCTAssertNotNil(loadedUserInfo)
        XCTAssertFalse(loadedUserInfo!.isValid)
        XCTAssertTrue(loadedUserInfo!.needsRefresh)
    }
    
    // MARK: - Session Clear Tests
    
    func testSessionClear() {
        // Given
        mockPersistence.mockUserInfo = testUserInfo
        
        // When
        mockPersistence.clearSession()
        
        // Then
        XCTAssertNil(mockPersistence.mockUserInfo)
        XCTAssertEqual(mockPersistence.clearCallCount, 1)
    }
    
    // MARK: - First Launch Tests
    
    func testFirstLaunchDetection() {
        // Given
        mockPersistence.isFirstLaunchValue = true
        
        // When
        let isFirstLaunch = mockPersistence.isFirstLaunch()
        
        // Then
        XCTAssertTrue(isFirstLaunch)
    }
    
    func testSubsequentLaunch() {
        // Given
        mockPersistence.isFirstLaunchValue = false
        
        // When
        let isFirstLaunch = mockPersistence.isFirstLaunch()
        
        // Then
        XCTAssertFalse(isFirstLaunch)
    }
    
    // MARK: - Session Validation Tests
    
    func testSessionNeedsRefresh() {
        // Given - session that needs refresh
        let nearExpiryUser = UserInfo(
            userID: "test_user",
            email: "<EMAIL>",
            accessToken: "test_token",
            expiresAt: Date().addingTimeInterval(30) // Expires in 30 seconds
        )
        mockPersistence.mockUserInfo = nearExpiryUser
        
        // When
        let needsRefresh = mockPersistence.sessionNeedsRefresh()
        
        // Then
        XCTAssertTrue(needsRefresh)
    }
    
    func testSessionDoesNotNeedRefresh() {
        // Given - session with plenty of time left
        let validUser = UserInfo(
            userID: "test_user",
            email: "<EMAIL>",
            accessToken: "test_token",
            expiresAt: Date().addingTimeInterval(7200) // Expires in 2 hours
        )
        mockPersistence.mockUserInfo = validUser
        
        // When
        let needsRefresh = mockPersistence.sessionNeedsRefresh()
        
        // Then
        XCTAssertFalse(needsRefresh)
    }
    
    // MARK: - Background/Foreground Tests
    
    func testValidSessionAfterBackground() {
        // Given
        mockPersistence.simulateValidSession()
        
        // When
        let isValid = mockPersistence.validateSessionAfterBackground()
        
        // Then
        XCTAssertTrue(isValid)
    }
    
    func testExpiredSessionAfterBackground() {
        // Given
        mockPersistence.simulateExpiredSession()
        
        // When
        let isValid = mockPersistence.validateSessionAfterBackground()
        
        // Then
        XCTAssertFalse(isValid)
    }
    
    func testAppWillEnterForegroundWithValidSession() {
        // Given
        mockPersistence.shouldSucceedLoad = true
        
        // When
        let sessionValid = mockPersistence.handleAppWillEnterForeground()
        
        // Then
        XCTAssertTrue(sessionValid)
    }
    
    func testAppWillEnterForegroundWithInvalidSession() {
        // Given
        mockPersistence.shouldSucceedLoad = false
        
        // When
        let sessionValid = mockPersistence.handleAppWillEnterForeground()
        
        // Then
        XCTAssertFalse(sessionValid)
    }
    
    // MARK: - UserInfo Model Tests
    
    func testUserInfoValidation() {
        // Given
        let validUser = UserInfo(
            userID: "test_user",
            email: "<EMAIL>",
            accessToken: "test_token",
            expiresAt: Date().addingTimeInterval(3600)
        )
        
        // Then
        XCTAssertTrue(validUser.isValid)
        XCTAssertFalse(validUser.needsRefresh)
        XCTAssertGreaterThan(validUser.timeUntilExpiration, 0)
    }
    
    func testExpiredUserInfo() {
        // Given
        let expiredUser = UserInfo(
            userID: "test_user",
            email: "<EMAIL>",
            accessToken: "test_token",
            expiresAt: Date().addingTimeInterval(-3600)
        )
        
        // Then
        XCTAssertFalse(expiredUser.isValid)
        XCTAssertTrue(expiredUser.needsRefresh)
        XCTAssertLessThan(expiredUser.timeUntilExpiration, 0)
    }
    
    func testUserInfoDisplayName() {
        // Given
        let userWithEmail = UserInfo(
            userID: "test_user",
            email: "<EMAIL>",
            accessToken: "test_token",
            expiresAt: Date().addingTimeInterval(3600)
        )
        
        let userWithoutEmail = UserInfo(
            userID: "test_user",
            email: nil,
            accessToken: "test_token",
            expiresAt: Date().addingTimeInterval(3600)
        )
        
        // Then
        XCTAssertEqual(userWithEmail.displayName, "John")
        XCTAssertEqual(userWithoutEmail.displayName, "User")
    }
    
    func testUserInfoEquality() {
        // Given
        let user1 = UserInfo(
            userID: "test_user",
            email: "<EMAIL>",
            accessToken: "test_token",
            expiresAt: Date().addingTimeInterval(3600)
        )
        
        let user2 = UserInfo(
            userID: "test_user",
            email: "<EMAIL>",
            accessToken: "test_token",
            expiresAt: user1.expiresAt
        )
        
        // Then
        XCTAssertEqual(user1, user2)
    }
}
