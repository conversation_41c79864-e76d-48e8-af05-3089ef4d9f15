//
//  MockAuthenticationService.swift
//  SikTing
//
//  Created by Augment Agent on 2025-08-02.
//

import Foundation
import AuthenticationServices
@testable import SikTing

/// Mock authentication service for testing
@MainActor
class MockSecureAuthenticationService: SecureAuthenticationService {
    
    // MARK: - Test Configuration
    
    var shouldSucceedSignIn = true
    var shouldSucceedTokenExchange = true
    var mockError: SecureAuthenticationError?
    var mockUserInfo: UserInfo?
    var mockTokenResponse: TokenResponse?
    var signInCallCount = 0
    var signOutCallCount = 0
    var refreshTokenCallCount = 0
    
    // MARK: - Override Methods
    
    override func signInWithApple() async {
        signInCallCount += 1
        isLoading = true
        authState = .authenticating
        lastError = nil
        
        // Simulate network delay
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        if shouldSucceedSignIn && shouldSucceedTokenExchange {
            // Simulate successful authentication
            let mockUser = mockUserInfo ?? UserInfo(
                userID: "test_user_123",
                email: "<EMAIL>",
                accessToken: "mock_access_token",
                expiresAt: Date().addingTimeInterval(3600)
            )
            
            userInfo = mockUser
            authState = .authenticated(accessToken: mockUser.accessToken, expiresAt: mockUser.expiresAt)
            errorHandler.clearError()
        } else {
            // Simulate failure
            let error = mockError ?? .appleSignInFailed("Mock sign in failed")
            lastError = error
            authState = .failed(error)
            errorHandler.handleAuthenticationError(error, context: "test") {}
        }
        
        isLoading = false
    }
    
    override func signOut() {
        signOutCallCount += 1
        super.signOut()
    }
    
    override func refreshTokenIfNeeded() async {
        refreshTokenCallCount += 1
        
        if shouldSucceedTokenExchange {
            // Simulate successful refresh
            if let currentUser = userInfo {
                let refreshedUser = UserInfo(
                    userID: currentUser.userID,
                    email: currentUser.email,
                    accessToken: "refreshed_token",
                    expiresAt: Date().addingTimeInterval(3600)
                )
                userInfo = refreshedUser
                authState = .authenticated(accessToken: refreshedUser.accessToken, expiresAt: refreshedUser.expiresAt)
            }
        } else {
            // Simulate refresh failure
            let error = mockError ?? .tokenExpired
            lastError = error
            authState = .failed(error)
        }
    }
    
    // MARK: - Test Helpers
    
    func reset() {
        shouldSucceedSignIn = true
        shouldSucceedTokenExchange = true
        mockError = nil
        mockUserInfo = nil
        mockTokenResponse = nil
        signInCallCount = 0
        signOutCallCount = 0
        refreshTokenCallCount = 0
        userInfo = nil
        authState = .notAuthenticated
        lastError = nil
        isLoading = false
        errorHandler.clearError()
    }
    
    func simulateTokenExpiration() {
        if let currentUser = userInfo {
            let expiredUser = UserInfo(
                userID: currentUser.userID,
                email: currentUser.email,
                accessToken: currentUser.accessToken,
                expiresAt: Date().addingTimeInterval(-3600) // Expired 1 hour ago
            )
            userInfo = expiredUser
            authState = .authenticated(accessToken: expiredUser.accessToken, expiresAt: expiredUser.expiresAt)
        }
    }
    
    func simulateNetworkError() {
        mockError = .networkError(URLError(.notConnectedToInternet))
        shouldSucceedSignIn = false
        shouldSucceedTokenExchange = false
    }
    
    func simulateServerError() {
        mockError = .serverUnavailable
        shouldSucceedSignIn = false
        shouldSucceedTokenExchange = false
    }
    
    func simulateRateLimitError() {
        mockError = .rateLimitExceeded
        shouldSucceedSignIn = false
        shouldSucceedTokenExchange = false
    }
}

/// Mock session persistence service for testing
class MockSessionPersistenceService: SessionPersistenceService {
    
    // MARK: - Test Configuration
    
    var shouldSucceedSave = true
    var shouldSucceedLoad = true
    var mockUserInfo: UserInfo?
    var saveCallCount = 0
    var loadCallCount = 0
    var clearCallCount = 0
    var isFirstLaunchValue = true
    
    // MARK: - Override Methods
    
    override func saveSession(_ userInfo: UserInfo) -> Bool {
        saveCallCount += 1
        if shouldSucceedSave {
            mockUserInfo = userInfo
            return true
        }
        return false
    }
    
    override func loadSession() -> UserInfo? {
        loadCallCount += 1
        if shouldSucceedLoad {
            return mockUserInfo
        }
        return nil
    }
    
    override func clearSession() {
        clearCallCount += 1
        mockUserInfo = nil
    }
    
    override func isFirstLaunch() -> Bool {
        return isFirstLaunchValue
    }
    
    override func sessionNeedsRefresh() -> Bool {
        guard let userInfo = mockUserInfo else { return true }
        return userInfo.needsRefresh
    }
    
    override func handleAppWillEnterForeground() -> Bool {
        return shouldSucceedLoad
    }
    
    override func validateSessionAfterBackground() -> Bool {
        guard let userInfo = mockUserInfo else { return false }
        return userInfo.isValid
    }
    
    // MARK: - Test Helpers
    
    func reset() {
        shouldSucceedSave = true
        shouldSucceedLoad = true
        mockUserInfo = nil
        saveCallCount = 0
        loadCallCount = 0
        clearCallCount = 0
        isFirstLaunchValue = true
    }
    
    func simulateExpiredSession() {
        mockUserInfo = UserInfo(
            userID: "test_user",
            email: "<EMAIL>",
            accessToken: "expired_token",
            expiresAt: Date().addingTimeInterval(-3600)
        )
    }
    
    func simulateValidSession() {
        mockUserInfo = UserInfo(
            userID: "test_user",
            email: "<EMAIL>",
            accessToken: "valid_token",
            expiresAt: Date().addingTimeInterval(3600)
        )
    }
}

/// Mock WebSocket manager for testing
class MockWebSocketManager: WebSocketManager {
    
    // MARK: - Test Configuration
    
    var shouldSucceedConnection = true
    var mockConnectionState: WebSocketConnectionState = .disconnected(reason: nil)
    var connectCallCount = 0
    var connectWithTokenCallCount = 0
    var disconnectCallCount = 0
    var sendAudioDataCallCount = 0
    var lastAccessToken: String?
    var lastConfig: FunASRConfiguration?
    
    // MARK: - Override Methods
    
    override func connect(to urlString: String?, with config: FunASRConfiguration?) {
        connectCallCount += 1
        lastConfig = config
        
        DispatchQueue.main.async {
            if self.shouldSucceedConnection {
                self.connectionState = .connected
                self.delegate?.webSocketManager(self, didChangeConnectionState: .connected)
            } else {
                self.connectionState = .disconnected(reason: .connectionFailed)
                self.delegate?.webSocketManager(self, didChangeConnectionState: .disconnected(reason: .connectionFailed))
            }
        }
    }
    
    override func connectWithToken(accessToken: String, to urlString: String?, with config: FunASRConfiguration?, authService: SecureAuthenticationService?) {
        connectWithTokenCallCount += 1
        lastAccessToken = accessToken
        lastConfig = config
        
        DispatchQueue.main.async {
            if self.shouldSucceedConnection {
                self.connectionState = .connected
                self.delegate?.webSocketManager(self, didChangeConnectionState: .connected)
            } else {
                self.connectionState = .disconnected(reason: .authenticationFailed)
                self.delegate?.webSocketManager(self, didChangeConnectionState: .disconnected(reason: .authenticationFailed))
            }
        }
    }
    
    override func disconnect(reason: DisconnectionReason) {
        disconnectCallCount += 1
        
        DispatchQueue.main.async {
            self.connectionState = .disconnected(reason: reason)
            self.delegate?.webSocketManager(self, didChangeConnectionState: .disconnected(reason: reason))
        }
    }
    
    override func sendAudioData(_ data: Data) {
        sendAudioDataCallCount += 1
    }
    
    // MARK: - Test Helpers
    
    func reset() {
        shouldSucceedConnection = true
        mockConnectionState = .disconnected(reason: nil)
        connectCallCount = 0
        connectWithTokenCallCount = 0
        disconnectCallCount = 0
        sendAudioDataCallCount = 0
        lastAccessToken = nil
        lastConfig = nil
        connectionState = .disconnected(reason: nil)
    }
    
    func simulateConnectionFailure() {
        shouldSucceedConnection = false
    }
    
    func simulateAuthenticationFailure() {
        DispatchQueue.main.async {
            self.connectionState = .disconnected(reason: .authenticationFailed)
            self.delegate?.webSocketManager(self, didChangeConnectionState: .disconnected(reason: .authenticationFailed))
            
            let error = SecureWebSocketError.authenticationRequired
            self.delegate?.webSocketManager(self, didEncounterError: error)
        }
    }
    
    func simulateTokenExpiration() {
        DispatchQueue.main.async {
            self.connectionState = .disconnected(reason: .authenticationFailed)
            self.delegate?.webSocketManager(self, didChangeConnectionState: .disconnected(reason: .authenticationFailed))
            
            let error = SecureWebSocketError.tokenExpired
            self.delegate?.webSocketManager(self, didEncounterError: error)
        }
    }
}
