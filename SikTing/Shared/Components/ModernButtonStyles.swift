//
//  ModernButtonStyles.swift
//  SikTing
//
//  Created by <PERSON><PERSON> on 2025/1/19.
//

import SwiftUI

// MARK: - Modern Button Styles with Enhanced Effects

/// Glassmorphism button style with modern effects
struct GlassmorphismButtonStyle: ButtonStyle {
    let isProminent: Bool
    
    init(prominent: Bool = false) {
        self.isProminent = prominent
    }
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, DesignSystem.spacing.medium)
            .padding(.vertical, DesignSystem.spacing.small)
            .background(
                isProminent 
                    ? DesignSystem.ModernEffects.glassmorphismModal
                    : DesignSystem.ModernEffects.glassmorphismCard
            )
            .cornerRadius(CornerRadius.button)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.button)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.3),
                                Color.clear,
                                DesignSystem.brandColors.adaptivePrimary.opacity(0.2)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
            .modernShadow(elevation: configuration.isPressed ? .subtle : .medium)
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

/// Elevated button style with dynamic shadows
struct ElevatedButtonStyle: ButtonStyle {
    let elevation: ShadowElevation
    let backgroundColor: Color
    
    init(elevation: ShadowElevation = .medium, backgroundColor: Color = .brandPrimary) {
        self.elevation = elevation
        self.backgroundColor = backgroundColor
    }
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, DesignSystem.spacing.medium)
            .padding(.vertical, DesignSystem.spacing.small)
            .background(backgroundColor)
            .foregroundColor(.white)
            .cornerRadius(CornerRadius.button)
            .modernShadow(elevation: configuration.isPressed ? .subtle : elevation)
            .scaleEffect(configuration.isPressed ? 0.96 : 1.0)
            .animation(.easeInOut(duration: 0.15), value: configuration.isPressed)
            .overlay(
                // Subtle highlight on press
                RoundedRectangle(cornerRadius: CornerRadius.button)
                    .fill(Color.white.opacity(configuration.isPressed ? 0.2 : 0))
                    .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
            )
    }
}

/// Gradient button style with modern effects
struct GradientButtonStyle: ButtonStyle {
    let gradient: LinearGradient
    let hasGlow: Bool
    
    init(gradient: LinearGradient = DesignSystem.brandColors.adaptivePrimaryGradient, glow: Bool = false) {
        self.gradient = gradient
        self.hasGlow = glow
    }
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, DesignSystem.spacing.medium)
            .padding(.vertical, DesignSystem.spacing.small)
            .background(gradient)
            .foregroundColor(.white)
            .cornerRadius(CornerRadius.button)
            .modernShadow(elevation: configuration.isPressed ? .subtle : .medium)
            .scaleEffect(configuration.isPressed ? 0.97 : 1.0)
            .animation(.easeInOut(duration: 0.12), value: configuration.isPressed)
            .overlay(
                // Glow effect when enabled
                Group {
                    if hasGlow {
                        RoundedRectangle(cornerRadius: CornerRadius.button)
                            .stroke(
                                DesignSystem.brandColors.adaptivePrimary.opacity(0.6),
                                lineWidth: 2
                            )
                            .blur(radius: 4)
                            .opacity(configuration.isPressed ? 0.8 : 1.0)
                    }
                }
            )
    }
}

/// Floating action button style with enhanced effects
struct FloatingActionButtonStyle: ButtonStyle {
    let size: CGFloat
    let hasRipple: Bool
    
    init(size: CGFloat = 56, ripple: Bool = true) {
        self.size = size
        self.hasRipple = ripple
    }
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .frame(width: size, height: size)
            .background(
                Circle()
                    .fill(DesignSystem.brandColors.adaptivePrimaryGradient)
            )
            .foregroundColor(.white)
            .modernShadow(elevation: configuration.isPressed ? .medium : .strong)
            .scaleEffect(configuration.isPressed ? 0.94 : 1.0)
            .animation(.easeInOut(duration: 0.15), value: configuration.isPressed)
            .overlay(
                // Ripple effect
                Group {
                    if hasRipple && configuration.isPressed {
                        Circle()
                            .stroke(Color.white.opacity(0.3), lineWidth: 2)
                            .scaleEffect(1.2)
                            .opacity(0)
                            .animation(.easeOut(duration: 0.3), value: configuration.isPressed)
                    }
                }
            )
    }
}

/// Neumorphism-inspired button style
struct NeumorphismButtonStyle: ButtonStyle {
    let isInset: Bool
    
    init(inset: Bool = false) {
        self.isInset = inset
    }
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, DesignSystem.spacing.medium)
            .padding(.vertical, DesignSystem.spacing.small)
            .background(Color.brandSurface)
            .cornerRadius(CornerRadius.button)
            .shadow(
                color: DesignSystem.brandColors.adaptivePrimary.opacity(0.1),
                radius: isInset ? 2 : 8,
                x: isInset ? -2 : 4,
                y: isInset ? -2 : 4
            )
            .shadow(
                color: Color.white.opacity(0.8),
                radius: isInset ? 2 : 8,
                x: isInset ? 2 : -4,
                y: isInset ? 2 : -4
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Button Style Extensions

extension View {
    
    /// Apply glassmorphism button style
    func glassmorphismButtonStyle(prominent: Bool = false) -> some View {
        self.buttonStyle(GlassmorphismButtonStyle(prominent: prominent))
    }
    
    /// Apply elevated button style
    func elevatedButtonStyle(elevation: ShadowElevation = .medium, backgroundColor: Color = .brandPrimary) -> some View {
        self.buttonStyle(ElevatedButtonStyle(elevation: elevation, backgroundColor: backgroundColor))
    }
    
    /// Apply gradient button style
    func gradientButtonStyle(gradient: LinearGradient = DesignSystem.brandColors.adaptivePrimaryGradient, glow: Bool = false) -> some View {
        self.buttonStyle(GradientButtonStyle(gradient: gradient, glow: glow))
    }
    
    /// Apply floating action button style
    func floatingActionButtonStyle(size: CGFloat = 56, ripple: Bool = true) -> some View {
        self.buttonStyle(FloatingActionButtonStyle(size: size, ripple: ripple))
    }
    
    /// Apply neumorphism button style
    func neumorphismButtonStyle(inset: Bool = false) -> some View {
        self.buttonStyle(NeumorphismButtonStyle(inset: inset))
    }
}

// MARK: - Modern Card Components

struct ModernCard<Content: View>: View {
    let content: Content
    let style: ModernCardVariant
    
    init(style: ModernCardVariant = .standard, @ViewBuilder content: () -> Content) {
        self.style = style
        self.content = content()
    }
    
    var body: some View {
        content
            .padding()
            .background(backgroundForStyle)
            .cornerRadius(CornerRadius.large)
            .modernShadow(elevation: elevationForStyle)
            .overlay(borderForStyle)
    }
    
    private var backgroundForStyle: some View {
        Group {
            switch style {
            case .standard:
                Color.brandCardBackground
            case .glassmorphism:
                Rectangle()
                    .fill(DesignSystem.ModernEffects.glassmorphismCard)
            case .elevated:
                Color.brandSurface
            case .gradient:
                Rectangle()
                    .fill(DesignSystem.brandColors.adaptiveBackgroundGradient)
            }
        }
    }
    
    private var elevationForStyle: ShadowElevation {
        switch style {
        case .standard:
            return .medium
        case .glassmorphism:
            return .subtle
        case .elevated:
            return .strong
        case .gradient:
            return .medium
        }
    }
    
    private var borderForStyle: some View {
        Group {
            switch style {
            case .standard, .elevated:
                RoundedRectangle(cornerRadius: CornerRadius.large)
                    .stroke(Color.brandCardBorder, lineWidth: 0.5)
            case .glassmorphism:
                RoundedRectangle(cornerRadius: CornerRadius.large)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.2),
                                Color.clear,
                                DesignSystem.brandColors.adaptivePrimary.opacity(0.1)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            case .gradient:
                RoundedRectangle(cornerRadius: CornerRadius.large)
                    .stroke(DesignSystem.brandColors.adaptivePrimary.opacity(0.3), lineWidth: 1)
            }
        }
    }
}

enum ModernCardVariant {
    case standard
    case glassmorphism
    case elevated
    case gradient
}
