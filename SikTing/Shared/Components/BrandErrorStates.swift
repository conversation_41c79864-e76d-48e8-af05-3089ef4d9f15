//
//  BrandErrorStates.swift
//  SikTing
//
//  Created by <PERSON><PERSON> on 2025/1/19.
//

import SwiftUI

// MARK: - Error State Types

enum BrandErrorType: Equatable {
    case networkError(String)
    case permissionError(String)
    case serverError(String)
    case validationError(String)
    case unknownError(String)
    
    var title: String {
        switch self {
        case .networkError:
            return "Connection Error"
        case .permissionError:
            return "Permission Required"
        case .serverError:
            return "Server Error"
        case .validationError:
            return "Invalid Input"
        case .unknownError:
            return "Something Went Wrong"
        }
    }
    
    var icon: String {
        switch self {
        case .networkError:
            return "wifi.exclamationmark"
        case .permissionError:
            return "lock.shield"
        case .serverError:
            return "server.rack"
        case .validationError:
            return "exclamationmark.triangle"
        case .unknownError:
            return "questionmark.circle"
        }
    }
    
    var backgroundColor: Color {
        switch self {
        case .networkError, .serverError, .unknownError:
            return .brandAccent.opacity(0.1)
        case .permissionError:
            return .brandSurface
        case .validationError:
            return .brandAccent.opacity(0.15)
        }
    }
    
    var iconColor: Color {
        switch self {
        case .networkError, .serverError, .validationError, .unknownError:
            return .brandAccent
        case .permissionError:
            return .brandSecondary
        }
    }
    
    var primaryActionTitle: String {
        switch self {
        case .networkError, .serverError, .unknownError:
            return "Retry"
        case .permissionError:
            return "Open Settings"
        case .validationError:
            return "Try Again"
        }
    }
}

// MARK: - Brand Error State View

struct BrandErrorStateView: View {
    let errorType: BrandErrorType
    let message: String
    let primaryAction: (() -> Void)?
    let secondaryAction: (() -> Void)?
    let secondaryActionTitle: String?
    
    @State private var animationOffset: CGFloat = 0
    @State private var iconScale: CGFloat = 0.8
    @State private var contentOpacity: Double = 0
    
    init(
        errorType: BrandErrorType,
        message: String,
        primaryAction: (() -> Void)? = nil,
        secondaryAction: (() -> Void)? = nil,
        secondaryActionTitle: String? = nil
    ) {
        self.errorType = errorType
        self.message = message
        self.primaryAction = primaryAction
        self.secondaryAction = secondaryAction
        self.secondaryActionTitle = secondaryActionTitle
    }
    
    var body: some View {
        VStack(spacing: DesignSystem.spacing.large) {
            // Animated icon with background
            iconSection
            
            // Error content
            errorContent
            
            // Action buttons
            actionButtons
        }
        .padding(DesignSystem.spacing.large)
        .background(errorType.backgroundColor)
        .cornerRadius(CornerRadius.large)
        .shadow(
            color: errorType.iconColor.opacity(0.2),
            radius: 8,
            x: 0,
            y: 4
        )
        .offset(y: animationOffset)
        .opacity(contentOpacity)
        .onAppear {
            startAnimations()
        }
        .accessibilityElement(children: .contain)
        .accessibilityLabel("\(errorType.title): \(message)")
    }
    
    private var iconSection: some View {
        ZStack {
            // Background circle with subtle animation
            Circle()
                .fill(errorType.iconColor.opacity(0.2))
                .frame(width: 80, height: 80)
                .scaleEffect(iconScale)
                .animation(
                    DesignSystem.animations.breathingEffect,
                    value: iconScale
                )
            
            // Main error icon
            Image(systemName: errorType.icon)
                .font(.system(size: 32, weight: .medium))
                .foregroundColor(errorType.iconColor)
                .scaleEffect(iconScale)
                .animation(
                    DesignSystem.animations.spring.delay(0.2),
                    value: iconScale
                )
        }
    }
    
    private var errorContent: some View {
        VStack(spacing: DesignSystem.spacing.medium) {
            // Error title
            Text(errorType.title)
                .font(DesignSystem.typography.title3)
                .fontWeight(.semibold)
                .foregroundColor(.brandTextPrimary)
                .multilineTextAlignment(.center)
            
            // Error message
            Text(message)
                .font(DesignSystem.typography.bodySecondary)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
                .fixedSize(horizontal: false, vertical: true)
        }
    }
    
    private var actionButtons: some View {
        VStack(spacing: DesignSystem.spacing.small) {
            // Primary action button
            if let primaryAction = primaryAction {
                BrandButton(
                    errorType.primaryActionTitle,
                    style: .primary,
                    size: .medium
                ) {
                    HapticPattern.light.trigger()
                    primaryAction()
                }
            }
            
            // Secondary action button
            if let secondaryAction = secondaryAction,
               let secondaryTitle = secondaryActionTitle {
                BrandButton(
                    secondaryTitle,
                    style: .tertiary,
                    size: .medium
                ) {
                    HapticPattern.selection.trigger()
                    secondaryAction()
                }
            }
        }
    }
    
    private func startAnimations() {
        // Entrance animation
        withAnimation(DesignSystem.animations.spring) {
            animationOffset = 0
            contentOpacity = 1
            iconScale = 1.0
        }
        
        // Breathing effect for icon
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            withAnimation {
                iconScale = 1.05
            }
        }
    }
}

// MARK: - Empty State Types

enum BrandEmptyStateType: Equatable {
    case noTranscriptions
    case noSearchResults(String)
    case noHistory
    case microphoneDisabled
    case offlineMode
    case loading
    
    var title: String {
        switch self {
        case .noTranscriptions:
            return "No Transcriptions Yet"
        case .noSearchResults:
            return "No Results Found"
        case .noHistory:
            return "History is Empty"
        case .microphoneDisabled:
            return "Microphone Disabled"
        case .offlineMode:
            return "Offline Mode"
        case .loading:
            return "Loading..."
        }
    }
    
    var subtitle: String {
        switch self {
        case .noTranscriptions:
            return "Start recording to create your first transcription"
        case .noSearchResults(let query):
            return "No transcriptions match '\(query)'. Try adjusting your search terms."
        case .noHistory:
            return "Your transcription history will appear here once you start recording"
        case .microphoneDisabled:
            return "Enable microphone access to start transcribing"
        case .offlineMode:
            return "Connect to the internet to access transcription services"
        case .loading:
            return "Please wait while we load your content"
        }
    }
    
    var icon: String {
        switch self {
        case .noTranscriptions:
            return "waveform"
        case .noSearchResults:
            return "magnifyingglass"
        case .noHistory:
            return "clock"
        case .microphoneDisabled:
            return "mic.slash"
        case .offlineMode:
            return "wifi.slash"
        case .loading:
            return "arrow.clockwise"
        }
    }
    
    var illustration: String? {
        switch self {
        case .noTranscriptions:
            return "mic.badge.plus"
        case .noSearchResults:
            return "doc.text.magnifyingglass"
        case .noHistory:
            return "tray"
        case .microphoneDisabled:
            return "mic.slash.circle"
        case .offlineMode:
            return "icloud.slash"
        case .loading:
            return nil
        }
    }
    
    var primaryActionTitle: String? {
        switch self {
        case .noTranscriptions:
            return "Start Recording"
        case .noSearchResults:
            return "Clear Search"
        case .noHistory:
            return "Start Recording"
        case .microphoneDisabled:
            return "Open Settings"
        case .offlineMode:
            return "Retry Connection"
        case .loading:
            return nil
        }
    }
}

// MARK: - Brand Empty State View

struct BrandEmptyStateView: View {
    let emptyStateType: BrandEmptyStateType
    let primaryAction: (() -> Void)?
    let secondaryAction: (() -> Void)?
    let secondaryActionTitle: String?
    
    @State private var illustrationScale: CGFloat = 0.8
    @State private var contentOpacity: Double = 0
    @State private var floatingOffset: CGFloat = 0
    @State private var rotationAngle: Double = 0
    
    init(
        emptyStateType: BrandEmptyStateType,
        primaryAction: (() -> Void)? = nil,
        secondaryAction: (() -> Void)? = nil,
        secondaryActionTitle: String? = nil
    ) {
        self.emptyStateType = emptyStateType
        self.primaryAction = primaryAction
        self.secondaryAction = secondaryAction
        self.secondaryActionTitle = secondaryActionTitle
    }
    
    var body: some View {
        VStack(spacing: DesignSystem.spacing.xLarge) {
            // Illustration section
            illustrationSection
            
            // Content section
            contentSection
            
            // Action buttons
            if emptyStateType != .loading {
                actionSection
            }
        }
        .padding(DesignSystem.spacing.large)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .opacity(contentOpacity)
        .onAppear {
            startAnimations()
        }
        .accessibilityElement(children: .contain)
        .accessibilityLabel("\(emptyStateType.title): \(emptyStateType.subtitle)")
    }
    
    private var illustrationSection: some View {
        VStack(spacing: DesignSystem.spacing.medium) {
            // Main illustration
            if let illustration = emptyStateType.illustration {
                ZStack {
                    // Background gradient circle
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.brandSurface,
                                    Color.brandSecondary.opacity(0.1)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 120, height: 120)
                        .scaleEffect(illustrationScale)
                        .offset(y: floatingOffset)
                        .animation(
                            DesignSystem.animations.breathingEffect,
                            value: floatingOffset
                        )
                    
                    // Main illustration icon
                    Image(systemName: illustration)
                        .font(.system(size: 48, weight: .light))
                        .foregroundColor(.brandSecondary)
                        .scaleEffect(illustrationScale)
                        .rotationEffect(.degrees(rotationAngle))
                        .offset(y: floatingOffset)
                        .animation(
                            DesignSystem.animations.spring.delay(0.3),
                            value: illustrationScale
                        )
                }
            } else if emptyStateType == .loading {
                // Loading spinner
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .brandSecondary))
                    .scaleEffect(1.5)
                    .rotationEffect(.degrees(rotationAngle))
                    .animation(
                        .linear(duration: 1.0).repeatForever(autoreverses: false),
                        value: rotationAngle
                    )
            }
        }
    }
    
    private var contentSection: some View {
        VStack(spacing: DesignSystem.spacing.medium) {
            // Title
            Text(emptyStateType.title)
                .font(DesignSystem.typography.title2)
                .fontWeight(.semibold)
                .foregroundColor(.brandTextPrimary)
                .multilineTextAlignment(.center)
            
            // Subtitle
            Text(emptyStateType.subtitle)
                .font(DesignSystem.typography.bodySecondary)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
                .fixedSize(horizontal: false, vertical: true)
        }
    }
    
    private var actionSection: some View {
        VStack(spacing: DesignSystem.spacing.small) {
            // Primary action
            if let primaryAction = primaryAction,
               let actionTitle = emptyStateType.primaryActionTitle {
                BrandButton(
                    actionTitle,
                    style: .primary,
                    size: .medium
                ) {
                    HapticPattern.light.trigger()
                    primaryAction()
                }
            }
            
            // Secondary action
            if let secondaryAction = secondaryAction,
               let secondaryTitle = secondaryActionTitle {
                BrandButton(
                    secondaryTitle,
                    style: .tertiary,
                    size: .medium
                ) {
                    HapticPattern.selection.trigger()
                    secondaryAction()
                }
            }
        }
    }
    
    private func startAnimations() {
        // Initial entrance animation
        withAnimation(DesignSystem.animations.spring) {
            contentOpacity = 1.0
            illustrationScale = 1.0
        }
        
        // Floating animation for illustration
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            withAnimation {
                floatingOffset = -8
            }
        }
        
        // Continuous rotation for loading state
        if emptyStateType == .loading {
            withAnimation {
                rotationAngle = 360
            }
        }
    }
}

// MARK: - Loading State Component

struct BrandLoadingStateView: View {
    let message: String
    let showProgress: Bool
    let progress: Double
    
    @State private var shimmerOffset: CGFloat = -200
    @State private var pulseScale: CGFloat = 1.0
    
    init(
        message: String = "Loading...",
        showProgress: Bool = false,
        progress: Double = 0.0
    ) {
        self.message = message
        self.showProgress = showProgress
        self.progress = progress
    }
    
    var body: some View {
        VStack(spacing: DesignSystem.spacing.large) {
            // Loading animation
            loadingAnimation
            
            // Loading message
            Text(message)
                .font(DesignSystem.typography.bodyPrimary)
                .foregroundColor(.brandTextSecondary)
                .multilineTextAlignment(.center)
            
            // Progress bar (if needed)
            if showProgress {
                progressBar
            }
        }
        .padding(DesignSystem.spacing.large)
        .onAppear {
            startLoadingAnimations()
        }
        .accessibilityLabel("Loading: \(message)")
    }
    
    private var loadingAnimation: some View {
        ZStack {
            // Background circle
            Circle()
                .stroke(Color.brandTertiary, lineWidth: 4)
                .frame(width: 60, height: 60)
            
            // Animated progress circle
            Circle()
                .trim(from: 0, to: 0.7)
                .stroke(
                    AngularGradient(
                        colors: [.brandSecondary, .brandPrimary],
                        center: .center
                    ),
                    style: StrokeStyle(lineWidth: 4, lineCap: .round)
                )
                .frame(width: 60, height: 60)
                .rotationEffect(.degrees(-90))
                .scaleEffect(pulseScale)
                .animation(
                    .linear(duration: 1.0).repeatForever(autoreverses: false),
                    value: shimmerOffset
                )
        }
    }
    
    private var progressBar: some View {
        VStack(spacing: DesignSystem.spacing.xSmall) {
            // Progress bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // Background
                    Rectangle()
                        .fill(Color.brandFrenchLilac.opacity(0.3))
                        .frame(height: 4)
                        .cornerRadius(2)
                    
                    // Progress fill
                    Rectangle()
                        .fill(
                            LinearGradient(
                                colors: [.brandOrchid, .brandPersianPurple],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(width: geometry.size.width * CGFloat(progress), height: 4)
                        .cornerRadius(2)
                        .animation(DesignSystem.animations.standard, value: progress)
                }
            }
            .frame(height: 4)
            
            // Progress percentage
            Text("\(Int(progress * 100))%")
                .font(DesignSystem.typography.captionPrimary)
                .foregroundColor(.brandOrchid)
        }
    }
    
    private func startLoadingAnimations() {
        // Shimmer animation
        withAnimation(
            .linear(duration: 1.5).repeatForever(autoreverses: false)
        ) {
            shimmerOffset = 200
        }
        
        // Pulse animation
        withAnimation(
            .easeInOut(duration: 1.0).repeatForever(autoreverses: true)
        ) {
            pulseScale = 1.1
        }
    }
}

// MARK: - Skeleton Loading Components

struct BrandSkeletonView: View {
    let width: CGFloat?
    let height: CGFloat
    let cornerRadius: CGFloat
    
    @State private var shimmerOffset: CGFloat = -200
    
    init(
        width: CGFloat? = nil,
        height: CGFloat = 20,
        cornerRadius: CGFloat = 4
    ) {
        self.width = width
        self.height = height
        self.cornerRadius = cornerRadius
    }
    
    var body: some View {
        Rectangle()
            .fill(Color.brandFrenchLilac.opacity(0.3))
            .frame(width: width, height: height)
            .cornerRadius(cornerRadius)
            .overlay(
                Rectangle()
                    .fill(
                        LinearGradient(
                            colors: [
                                .clear,
                                .brandAlabaster.opacity(0.6),
                                .clear
                            ],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .offset(x: shimmerOffset)
                    .animation(
                        DesignSystem.animations.shimmer,
                        value: shimmerOffset
                    )
            )
            .clipped()
            .onAppear {
                shimmerOffset = 200
            }
            .accessibilityLabel("Loading content")
    }
}

// MARK: - Skeleton Card Component

struct BrandSkeletonCard: View {
    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.spacing.small) {
            // Title skeleton
            BrandSkeletonView(width: 200, height: 20, cornerRadius: 4)
            
            // Subtitle skeleton
            BrandSkeletonView(width: 150, height: 16, cornerRadius: 3)
            
            // Content skeletons
            VStack(alignment: .leading, spacing: DesignSystem.spacing.xSmall) {
                BrandSkeletonView(height: 14, cornerRadius: 2)
                BrandSkeletonView(width: 180, height: 14, cornerRadius: 2)
                BrandSkeletonView(width: 120, height: 14, cornerRadius: 2)
            }
            
            Spacer()
            
            // Action skeleton
            HStack {
                Spacer()
                BrandSkeletonView(width: 80, height: 32, cornerRadius: 6)
            }
        }
        .padding(DesignSystem.spacing.cardPadding)
        .background(Color.brandAlabaster)
        .cornerRadius(CornerRadius.card)
        .shadow(
            color: Color.brandPersianPurple.opacity(0.1),
            radius: 4,
            x: 0,
            y: 2
        )
        .accessibilityLabel("Loading card content")
    }
}

// MARK: - Convenience Extensions

extension BrandErrorStateView {
    static func networkError(
        _ message: String,
        retryAction: @escaping () -> Void
    ) -> BrandErrorStateView {
        BrandErrorStateView(
            errorType: .networkError(message),
            message: message,
            primaryAction: retryAction
        )
    }
    
    static func permissionError(
        _ message: String,
        settingsAction: @escaping () -> Void
    ) -> BrandErrorStateView {
        BrandErrorStateView(
            errorType: .permissionError(message),
            message: message,
            primaryAction: settingsAction
        )
    }
    
    static func serverError(
        _ message: String,
        retryAction: @escaping () -> Void,
        supportAction: (() -> Void)? = nil
    ) -> BrandErrorStateView {
        BrandErrorStateView(
            errorType: .serverError(message),
            message: message,
            primaryAction: retryAction,
            secondaryAction: supportAction,
            secondaryActionTitle: "Contact Support"
        )
    }
}

extension BrandEmptyStateView {
    static func noTranscriptions(
        startRecordingAction: @escaping () -> Void
    ) -> BrandEmptyStateView {
        BrandEmptyStateView(
            emptyStateType: .noTranscriptions,
            primaryAction: startRecordingAction
        )
    }
    
    static func noSearchResults(
        query: String,
        clearSearchAction: @escaping () -> Void
    ) -> BrandEmptyStateView {
        BrandEmptyStateView(
            emptyStateType: .noSearchResults(query),
            primaryAction: clearSearchAction
        )
    }
    
    static func noHistory(
        startRecordingAction: @escaping () -> Void
    ) -> BrandEmptyStateView {
        BrandEmptyStateView(
            emptyStateType: .noHistory,
            primaryAction: startRecordingAction
        )
    }
}

// MARK: - Preview Support

#if DEBUG
struct BrandErrorStatesPreview: View {
    @State private var currentErrorType: BrandErrorType = .networkError("Unable to connect to transcription service")
    @State private var currentEmptyType: BrandEmptyStateType = .noTranscriptions
    @State private var showError = true
    
    var body: some View {
        ScrollView {
            VStack(spacing: DesignSystem.spacing.large) {
                // Toggle between error and empty states
                Picker("State Type", selection: $showError) {
                    Text("Error States").tag(true)
                    Text("Empty States").tag(false)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()
                
                if showError {
                    // Error states
                    VStack(spacing: DesignSystem.spacing.medium) {
                        BrandErrorStateView(
                            errorType: currentErrorType,
                            message: "Unable to connect to transcription service. Please check your internet connection and try again.",
                            primaryAction: {
                                print("Retry tapped")
                            },
                            secondaryAction: {
                                print("Support tapped")
                            },
                            secondaryActionTitle: "Contact Support"
                        )
                        
                        // Error type switcher
                        VStack {
                            Button("Network Error") {
                                currentErrorType = .networkError("Connection failed")
                            }
                            Button("Permission Error") {
                                currentErrorType = .permissionError("Microphone access required")
                            }
                            Button("Server Error") {
                                currentErrorType = .serverError("Server unavailable")
                            }
                        }
                        .padding()
                    }
                } else {
                    // Empty states
                    VStack(spacing: DesignSystem.spacing.medium) {
                        BrandEmptyStateView(
                            emptyStateType: currentEmptyType,
                            primaryAction: {
                                print("Primary action tapped")
                            },
                            secondaryAction: {
                                print("Secondary action tapped")
                            },
                            secondaryActionTitle: "Learn More"
                        )
                        
                        // Empty type switcher
                        VStack {
                            Button("No Transcriptions") {
                                currentEmptyType = .noTranscriptions
                            }
                            Button("No Search Results") {
                                currentEmptyType = .noSearchResults("test query")
                            }
                            Button("No History") {
                                currentEmptyType = .noHistory
                            }
                            Button("Loading") {
                                currentEmptyType = .loading
                            }
                        }
                        .padding()
                    }
                }
                
                // Loading states
                VStack(spacing: DesignSystem.spacing.medium) {
                    Text("Loading States")
                        .font(DesignSystem.typography.title3)
                        .foregroundColor(.brandPersianPurple)
                    
                    BrandLoadingStateView(
                        message: "Loading transcriptions...",
                        showProgress: true,
                        progress: 0.65
                    )
                    
                    // Skeleton components
                    VStack(spacing: DesignSystem.spacing.small) {
                        Text("Skeleton Loading")
                            .font(DesignSystem.typography.headline)
                            .foregroundColor(.brandOrchid)
                        
                        BrandSkeletonCard()
                        
                        HStack(spacing: DesignSystem.spacing.small) {
                            BrandSkeletonView(width: 100, height: 20)
                            BrandSkeletonView(width: 80, height: 20)
                            BrandSkeletonView(width: 60, height: 20)
                        }
                    }
                }
            }
            .padding(DesignSystem.spacing.screenPadding)
        }
        .background(DesignSystem.brandColors.adaptiveBackgroundGradient)
    }
}

struct BrandErrorStatesPreview_Previews: PreviewProvider {
    static var previews: some View {
        BrandErrorStatesPreview()
            .preferredColorScheme(.light)
            .previewDisplayName("Light Mode")
        
        BrandErrorStatesPreview()
            .preferredColorScheme(.dark)
            .previewDisplayName("Dark Mode")
    }
}
#endif