//
//  HistoryNavigationUITests.swift
//  SikTingUITests
//
//  Created by Augment Agent on 2025-07-21.
//

import XCTest

class HistoryNavigationUITests: XCTestCase {
    
    // MARK: - Properties
    
    var app: XCUIApplication!
    
    // MARK: - Setup & Teardown
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        
        // Configure for iPhone 16 testing
        app.launchArguments = ["UI_TESTING", "IPHONE_16_TESTING"]
        app.launchEnvironment = [
            "ENABLE_TEST_DATA": "true",
            "DISABLE_ANIMATIONS": "true",
            "MOCK_SPEECH_RECOGNITION": "true"
        ]
        
        app.launch()
        
        // Navigate to History tab
        navigateToHistoryTab()
    }
    
    override func tearDownWithError() throws {
        app = nil
    }
    
    // MARK: - Helper Methods
    
    private func navigateToHistoryTab() {
        let historyTab = app.tabBars.buttons["History"]
        XCTAssertTrue(historyTab.waitForExistence(timeout: 5))
        historyTab.tap()
        
        // Wait for history view to load
        let historyTitle = app.navigationBars["History"]
        XCTAssertTrue(historyTitle.waitForExistence(timeout: 5))
    }
    
    private func createTestSession() {
        // Navigate to recording tab
        app.tabBars.buttons["Record"].tap()
        
        // Start recording
        let recordButton = app.buttons["Start Recording"]
        XCTAssertTrue(recordButton.waitForExistence(timeout: 5))
        recordButton.tap()
        
        // Wait for recording to start
        let stopButton = app.buttons["Stop Recording"]
        XCTAssertTrue(stopButton.waitForExistence(timeout: 5))
        
        // Stop recording after a brief moment
        Thread.sleep(forTimeInterval: 2)
        stopButton.tap()
        
        // Wait for session to be saved
        Thread.sleep(forTimeInterval: 1)
        
        // Return to history tab
        navigateToHistoryTab()
    }
    
    // MARK: - Navigation Tests
    
    func testHistoryTabNavigation() throws {
        // Given - App is launched
        
        // When - Navigate to History tab
        let historyTab = app.tabBars.buttons["History"]
        historyTab.tap()
        
        // Then - History view should be displayed
        let historyTitle = app.navigationBars["History"]
        XCTAssertTrue(historyTitle.exists)
        
        // Verify tab bar selection
        XCTAssertTrue(historyTab.isSelected)
    }
    
    func testTabSwitchingBetweenHistoryTabs() throws {
        // Given - History view is displayed
        
        // When - Switch to Favorites tab
        let favoritesTab = app.buttons["Favorites"]
        XCTAssertTrue(favoritesTab.waitForExistence(timeout: 5))
        favoritesTab.tap()
        
        // Then - Favorites content should be displayed
        let favoritesIndicator = app.staticTexts["No favorite sessions yet"]
        XCTAssertTrue(favoritesIndicator.waitForExistence(timeout: 3))
        
        // When - Switch to Saved tab
        let savedTab = app.buttons["Saved"]
        savedTab.tap()
        
        // Then - Saved content should be displayed
        let savedIndicator = app.staticTexts["No saved sessions yet"]
        XCTAssertTrue(savedIndicator.waitForExistence(timeout: 3))
        
        // When - Switch back to Recents tab
        let recentsTab = app.buttons["Recents"]
        recentsTab.tap()
        
        // Then - Recents content should be displayed
        XCTAssertTrue(recentsTab.isSelected)
    }
    
    func testNavigationToSessionDetail() throws {
        // Given - Create a test session
        createTestSession()
        
        // When - Tap on a session card
        let sessionCard = app.cells.firstMatch
        XCTAssertTrue(sessionCard.waitForExistence(timeout: 5))
        sessionCard.tap()
        
        // Then - Session detail view should be displayed
        let detailView = app.navigationBars.element(boundBy: 1) // Second navigation bar
        XCTAssertTrue(detailView.waitForExistence(timeout: 5))
        
        // Verify back navigation
        let backButton = app.navigationBars.buttons.element(boundBy: 0)
        XCTAssertTrue(backButton.exists)
        backButton.tap()
        
        // Should return to history list
        let historyTitle = app.navigationBars["History"]
        XCTAssertTrue(historyTitle.waitForExistence(timeout: 3))
    }
    
    // MARK: - Search Interaction Tests
    
    func testSearchBarInteraction() throws {
        // Given - History view is displayed
        
        // When - Tap search bar
        let searchBar = app.searchFields["Search history"]
        XCTAssertTrue(searchBar.waitForExistence(timeout: 5))
        searchBar.tap()
        
        // Then - Search bar should become active
        XCTAssertTrue(searchBar.isSelected)
        
        // Keyboard should appear
        XCTAssertTrue(app.keyboards.element.waitForExistence(timeout: 3))
        
        // When - Type search query
        searchBar.typeText("test")
        
        // Then - Search results should update
        let searchResults = app.tables["Search Results"]
        XCTAssertTrue(searchResults.waitForExistence(timeout: 3))
        
        // When - Cancel search
        let cancelButton = app.buttons["Cancel"]
        if cancelButton.exists {
            cancelButton.tap()
        } else {
            // Alternative: tap outside search bar
            app.coordinate(withNormalizedOffset: CGVector(dx: 0.5, dy: 0.7)).tap()
        }
        
        // Then - Should return to normal history view
        XCTAssertFalse(searchBar.isSelected)
    }
    
    func testSearchFiltering() throws {
        // Given - Create multiple test sessions
        createTestSession()
        createTestSession()
        
        // When - Perform search
        let searchBar = app.searchFields["Search history"]
        searchBar.tap()
        searchBar.typeText("session")
        
        // Then - Search results should be filtered
        let searchResults = app.tables["Search Results"]
        XCTAssertTrue(searchResults.waitForExistence(timeout: 5))
        
        // Verify search results contain expected content
        let resultCells = searchResults.cells
        XCTAssertGreaterThan(resultCells.count, 0)
        
        // Clear search
        let clearButton = searchBar.buttons["Clear text"]
        if clearButton.exists {
            clearButton.tap()
        }
    }
    
    // MARK: - Card Action Tests
    
    func testSessionCardFavoriteAction() throws {
        // Given - Create a test session
        createTestSession()
        
        // When - Tap favorite button on session card
        let sessionCard = app.cells.firstMatch
        XCTAssertTrue(sessionCard.waitForExistence(timeout: 5))
        
        let favoriteButton = sessionCard.buttons["Favorite"]
        XCTAssertTrue(favoriteButton.waitForExistence(timeout: 3))
        favoriteButton.tap()
        
        // Then - Session should be marked as favorite
        let favoriteIndicator = sessionCard.images["heart.fill"]
        XCTAssertTrue(favoriteIndicator.waitForExistence(timeout: 3))
        
        // Verify in Favorites tab
        let favoritesTab = app.buttons["Favorites"]
        favoritesTab.tap()
        
        let favoriteSession = app.cells.firstMatch
        XCTAssertTrue(favoriteSession.waitForExistence(timeout: 3))
    }
    
    func testSessionCardSaveAction() throws {
        // Given - Create a test session
        createTestSession()
        
        // When - Tap save button on session card
        let sessionCard = app.cells.firstMatch
        XCTAssertTrue(sessionCard.waitForExistence(timeout: 5))
        
        let saveButton = sessionCard.buttons["Save"]
        XCTAssertTrue(saveButton.waitForExistence(timeout: 3))
        saveButton.tap()
        
        // Then - Session should be marked as saved
        let saveIndicator = sessionCard.images["bookmark.fill"]
        XCTAssertTrue(saveIndicator.waitForExistence(timeout: 3))
        
        // Verify in Saved tab
        let savedTab = app.buttons["Saved"]
        savedTab.tap()
        
        let savedSession = app.cells.firstMatch
        XCTAssertTrue(savedSession.waitForExistence(timeout: 3))
    }
    
    func testSessionCardDeleteAction() throws {
        // Given - Create a test session
        createTestSession()
        
        let initialCellCount = app.cells.count
        
        // When - Swipe to delete session
        let sessionCard = app.cells.firstMatch
        XCTAssertTrue(sessionCard.waitForExistence(timeout: 5))
        sessionCard.swipeLeft()
        
        // Then - Delete button should appear
        let deleteButton = app.buttons["Delete"]
        XCTAssertTrue(deleteButton.waitForExistence(timeout: 3))
        deleteButton.tap()
        
        // Confirm deletion if alert appears
        let confirmButton = app.alerts.buttons["Delete"]
        if confirmButton.waitForExistence(timeout: 2) {
            confirmButton.tap()
        }
        
        // Then - Session should be removed
        Thread.sleep(forTimeInterval: 1) // Wait for deletion animation
        let finalCellCount = app.cells.count
        XCTAssertLessThan(finalCellCount, initialCellCount)
    }
    
    func testSessionCardShareAction() throws {
        // Given - Create a test session
        createTestSession()
        
        // When - Tap share button on session card
        let sessionCard = app.cells.firstMatch
        XCTAssertTrue(sessionCard.waitForExistence(timeout: 5))
        
        let shareButton = sessionCard.buttons["Share"]
        XCTAssertTrue(shareButton.waitForExistence(timeout: 3))
        shareButton.tap()
        
        // Then - Share sheet should appear
        let shareSheet = app.sheets.firstMatch
        XCTAssertTrue(shareSheet.waitForExistence(timeout: 5))
        
        // Verify share options are available
        let copyButton = shareSheet.buttons["Copy"]
        XCTAssertTrue(copyButton.exists)
        
        // Cancel share
        let cancelButton = shareSheet.buttons["Cancel"]
        if cancelButton.exists {
            cancelButton.tap()
        } else {
            // Tap outside to dismiss
            app.coordinate(withNormalizedOffset: CGVector(dx: 0.5, dy: 0.2)).tap()
        }
    }
    
    // MARK: - Scrolling and Performance Tests
    
    func testScrollingPerformance() throws {
        // Given - Create multiple sessions for scrolling
        for _ in 0..<20 {
            createTestSession()
        }
        
        let historyList = app.tables.firstMatch
        XCTAssertTrue(historyList.waitForExistence(timeout: 5))
        
        // When - Perform scrolling
        measure(metrics: [XCTOSSignpostMetric.scrollingAndDecelerationMetric]) {
            historyList.swipeUp()
            historyList.swipeUp()
            historyList.swipeDown()
            historyList.swipeDown()
        }
        
        // Then - Scrolling should be smooth (measured by metrics)
    }
    
    func testPullToRefresh() throws {
        // Given - History view is displayed
        let historyList = app.tables.firstMatch
        XCTAssertTrue(historyList.waitForExistence(timeout: 5))
        
        // When - Pull down to refresh
        let firstCell = historyList.cells.firstMatch
        if firstCell.exists {
            firstCell.coordinate(withNormalizedOffset: CGVector(dx: 0.5, dy: 0.5))
                .press(forDuration: 0.1, thenDragTo: historyList.coordinate(withNormalizedOffset: CGVector(dx: 0.5, dy: 0.8)))
        } else {
            // If no cells, pull from top of list
            historyList.coordinate(withNormalizedOffset: CGVector(dx: 0.5, dy: 0.1))
                .press(forDuration: 0.1, thenDragTo: historyList.coordinate(withNormalizedOffset: CGVector(dx: 0.5, dy: 0.5)))
        }
        
        // Then - Refresh indicator should appear and disappear
        let refreshIndicator = app.activityIndicators.firstMatch
        if refreshIndicator.waitForExistence(timeout: 2) {
            XCTAssertTrue(refreshIndicator.exists)
            // Wait for refresh to complete
            XCTAssertTrue(refreshIndicator.waitForNonExistence(timeout: 5))
        }
    }
    
    // MARK: - iPhone 16 Specific Tests
    
    func testiPhone16DisplayOptimization() throws {
        // Given - App is running on iPhone 16
        
        // When - Check display elements
        let historyTitle = app.navigationBars["History"]
        XCTAssertTrue(historyTitle.exists)
        
        // Then - Verify iPhone 16 specific optimizations
        // Check that UI elements are properly sized for iPhone 16 screen
        let screenBounds = app.frame
        XCTAssertGreaterThan(screenBounds.width, 390) // iPhone 16 width
        XCTAssertGreaterThan(screenBounds.height, 844) // iPhone 16 height
        
        // Verify tab bar is accessible
        let tabBar = app.tabBars.firstMatch
        XCTAssertTrue(tabBar.exists)
        XCTAssertGreaterThan(tabBar.frame.minY, screenBounds.height - 100) // Near bottom
        
        // Verify navigation bar is at top
        XCTAssertLessThan(historyTitle.frame.maxY, 100) // Near top
    }
    
    func testiPhone16SafeAreaHandling() throws {
        // Given - App is running on iPhone 16
        
        // When - Check safe area handling
        let historyList = app.tables.firstMatch
        XCTAssertTrue(historyList.waitForExistence(timeout: 5))
        
        // Then - Content should not be obscured by notch or home indicator
        let navigationBar = app.navigationBars.firstMatch
        let tabBar = app.tabBars.firstMatch
        
        // List should be positioned between navigation and tab bars
        XCTAssertGreaterThan(historyList.frame.minY, navigationBar.frame.maxY)
        XCTAssertLessThan(historyList.frame.maxY, tabBar.frame.minY)
    }
    
    // MARK: - Complete User Journey Tests
    
    func testCompleteHistoryUserJourney() throws {
        // Given - Fresh app state
        
        // Step 1: Create a recording session
        app.tabBars.buttons["Record"].tap()
        let recordButton = app.buttons["Start Recording"]
        recordButton.tap()
        Thread.sleep(forTimeInterval: 3)
        app.buttons["Stop Recording"].tap()
        Thread.sleep(forTimeInterval: 1)
        
        // Step 2: Navigate to History
        navigateToHistoryTab()
        
        // Step 3: Verify session appears in Recents
        let sessionCard = app.cells.firstMatch
        XCTAssertTrue(sessionCard.waitForExistence(timeout: 5))
        
        // Step 4: Mark as favorite
        sessionCard.buttons["Favorite"].tap()
        
        // Step 5: Switch to Favorites tab and verify
        app.buttons["Favorites"].tap()
        XCTAssertTrue(app.cells.firstMatch.waitForExistence(timeout: 3))
        
        // Step 6: View session details
        app.cells.firstMatch.tap()
        let detailView = app.navigationBars.element(boundBy: 1)
        XCTAssertTrue(detailView.waitForExistence(timeout: 5))
        
        // Step 7: Return to list and search
        app.navigationBars.buttons.element(boundBy: 0).tap()
        app.buttons["Recents"].tap()
        
        let searchBar = app.searchFields["Search history"]
        searchBar.tap()
        searchBar.typeText("session")
        
        // Step 8: Verify search results
        let searchResults = app.tables["Search Results"]
        XCTAssertTrue(searchResults.waitForExistence(timeout: 3))
        
        // Step 9: Clear search and return to normal view
        searchBar.buttons["Clear text"].tap()
        app.coordinate(withNormalizedOffset: CGVector(dx: 0.5, dy: 0.7)).tap()
        
        // Journey completed successfully
        XCTAssertTrue(app.navigationBars["History"].exists)
    }
    
    func testMultiSessionWorkflow() throws {
        // Given - Create multiple sessions
        for i in 0..<5 {
            createTestSession()
        }
        
        // When - Perform various operations
        let cells = app.cells
        XCTAssertGreaterThanOrEqual(cells.count, 5)
        
        // Mark some as favorites
        cells.element(boundBy: 0).buttons["Favorite"].tap()
        cells.element(boundBy: 2).buttons["Favorite"].tap()
        
        // Mark some as saved
        cells.element(boundBy: 1).buttons["Save"].tap()
        cells.element(boundBy: 3).buttons["Save"].tap()
        
        // Verify in respective tabs
        app.buttons["Favorites"].tap()
        XCTAssertGreaterThanOrEqual(app.cells.count, 2)
        
        app.buttons["Saved"].tap()
        XCTAssertGreaterThanOrEqual(app.cells.count, 2)
        
        // Return to recents
        app.buttons["Recents"].tap()
        XCTAssertGreaterThanOrEqual(app.cells.count, 5)
    }
}
