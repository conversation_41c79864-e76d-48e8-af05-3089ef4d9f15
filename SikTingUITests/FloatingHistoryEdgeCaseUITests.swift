//
//  FloatingHistoryEdgeCaseUITests.swift
//  SikTingUITests
//
//  Created by Augment Agent on 2025/1/25.
//

import XCTest

final class FloatingHistoryEdgeCaseUITests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        
        // Set up test environment for edge cases
        app.launchArguments.append("--ui-testing")
        app.launchArguments.append("--reset-data")
        app.launch()
        
        navigateToFloatingHistory()
    }
    
    override func tearDownWithError() throws {
        app = nil
    }
    
    // MARK: - Helper Methods
    
    private func navigateToFloatingHistory() {
        let historyButton = app.buttons["History"]
        if historyButton.exists {
            historyButton.tap()
        }
        
        let floatingHistoryView = app.otherElements["FloatingHistoryView"]
        XCTAssertTrue(floatingHistoryView.waitForExistence(timeout: 5.0))
    }
    
    private func waitForAnimationsToComplete() {
        Thread.sleep(forTimeInterval: 0.5)
    }
    
    // MARK: - Empty State UI Tests
    
    func testEmptyStateForAllTabs() throws {
        // Given - No sessions exist (fresh app state)
        let tabBar = app.otherElements["FloatingTabBar"]
        XCTAssertTrue(tabBar.exists)
        
        // Test Recents tab empty state
        let recentsTab = app.buttons["RecentsTab"]
        recentsTab.tap()
        waitForAnimationsToComplete()
        
        let emptyStateView = app.otherElements["EmptyStateView"]
        XCTAssertTrue(emptyStateView.waitForExistence(timeout: 2.0))
        
        // Test Favorites tab empty state
        let favoritesTab = app.buttons["FavoritesTab"]
        favoritesTab.tap()
        waitForAnimationsToComplete()
        
        XCTAssertTrue(emptyStateView.exists)
        
        // Test Saved tab empty state
        let savedTab = app.buttons["SavedTab"]
        savedTab.tap()
        waitForAnimationsToComplete()
        
        XCTAssertTrue(emptyStateView.exists)
    }
    
    func testEmptyStateActionButton() throws {
        // Given - Empty state is displayed
        let recentsTab = app.buttons["RecentsTab"]
        recentsTab.tap()
        waitForAnimationsToComplete()
        
        let emptyStateView = app.otherElements["EmptyStateView"]
        XCTAssertTrue(emptyStateView.waitForExistence(timeout: 2.0))
        
        // When - Tap on empty state action button
        let actionButton = emptyStateView.buttons.firstMatch
        if actionButton.exists {
            actionButton.tap()
            waitForAnimationsToComplete()
            
            // Then - Should trigger appropriate action (e.g., start recording)
            // Verify the action was triggered (depends on implementation)
            XCTAssertTrue(true) // Placeholder for actual verification
        }
    }
    
    func testEmptyStateAccessibility() throws {
        // Given - Empty state is displayed
        let favoritesTab = app.buttons["FavoritesTab"]
        favoritesTab.tap()
        waitForAnimationsToComplete()
        
        let emptyStateView = app.otherElements["EmptyStateView"]
        XCTAssertTrue(emptyStateView.waitForExistence(timeout: 2.0))
        
        // Then - Empty state should have proper accessibility
        XCTAssertFalse(emptyStateView.label.isEmpty)
        XCTAssertTrue(emptyStateView.isAccessibilityElement)
    }
    
    // MARK: - Network Error UI Tests
    
    func testNetworkErrorHandling() throws {
        // Given - Simulate network error
        app.launchArguments.append("--simulate-network-error")
        app.terminate()
        app.launch()
        navigateToFloatingHistory()
        
        // When - Try to load sessions
        let tabBar = app.otherElements["FloatingTabBar"]
        XCTAssertTrue(tabBar.exists)
        
        // Then - Should handle error gracefully
        let errorView = app.otherElements["ErrorView"]
        if errorView.waitForExistence(timeout: 3.0) {
            XCTAssertTrue(errorView.exists)
            
            // Test retry functionality
            let retryButton = errorView.buttons["RetryButton"]
            if retryButton.exists {
                retryButton.tap()
                waitForAnimationsToComplete()
            }
        }
    }
    
    func testOfflineMode() throws {
        // Given - Simulate offline mode
        app.launchArguments.append("--offline-mode")
        app.terminate()
        app.launch()
        navigateToFloatingHistory()
        
        // When - Navigate through tabs
        let tabBar = app.otherElements["FloatingTabBar"]
        XCTAssertTrue(tabBar.exists)
        
        let favoritesTab = app.buttons["FavoritesTab"]
        favoritesTab.tap()
        waitForAnimationsToComplete()
        
        // Then - Should work with cached data
        // Verify offline indicator if present
        let offlineIndicator = app.staticTexts["OfflineIndicator"]
        if offlineIndicator.exists {
            XCTAssertTrue(offlineIndicator.exists)
        }
    }
    
    // MARK: - Memory Pressure UI Tests
    
    func testMemoryPressureHandling() throws {
        // Given - Simulate memory pressure
        app.launchArguments.append("--simulate-memory-pressure")
        app.terminate()
        app.launch()
        navigateToFloatingHistory()
        
        // When - Perform memory-intensive operations
        let tabBar = app.otherElements["FloatingTabBar"]
        XCTAssertTrue(tabBar.exists)
        
        // Rapidly switch tabs to stress test
        let recentsTab = app.buttons["RecentsTab"]
        let favoritesTab = app.buttons["FavoritesTab"]
        let savedTab = app.buttons["SavedTab"]
        
        for _ in 0..<10 {
            favoritesTab.tap()
            Thread.sleep(forTimeInterval: 0.1)
            savedTab.tap()
            Thread.sleep(forTimeInterval: 0.1)
            recentsTab.tap()
            Thread.sleep(forTimeInterval: 0.1)
        }
        
        // Then - App should remain responsive
        XCTAssertTrue(tabBar.exists)
        XCTAssertTrue(recentsTab.isSelected)
    }
    
    // MARK: - Device Rotation UI Tests
    
    func testDeviceRotationHandling() throws {
        // Given - Floating history view in portrait
        let tabBar = app.otherElements["FloatingTabBar"]
        XCTAssertTrue(tabBar.exists)
        
        // When - Rotate to landscape
        XCUIDevice.shared.orientation = .landscapeLeft
        waitForAnimationsToComplete()
        
        // Then - Tab bar should adapt to landscape
        XCTAssertTrue(tabBar.exists)
        
        // Test tab functionality in landscape
        let favoritesTab = app.buttons["FavoritesTab"]
        favoritesTab.tap()
        waitForAnimationsToComplete()
        
        XCTAssertTrue(favoritesTab.isSelected)
        
        // When - Rotate back to portrait
        XCUIDevice.shared.orientation = .portrait
        waitForAnimationsToComplete()
        
        // Then - Should maintain state and functionality
        XCTAssertTrue(tabBar.exists)
        XCTAssertTrue(favoritesTab.isSelected)
    }
    
    func testTabBarLayoutInLandscape() throws {
        // Given - Rotate to landscape
        XCUIDevice.shared.orientation = .landscapeLeft
        waitForAnimationsToComplete()
        
        let tabBar = app.otherElements["FloatingTabBar"]
        XCTAssertTrue(tabBar.exists)
        
        // When - Test all tabs in landscape
        let recentsTab = app.buttons["RecentsTab"]
        let favoritesTab = app.buttons["FavoritesTab"]
        let savedTab = app.buttons["SavedTab"]
        
        // Then - All tabs should be accessible
        XCTAssertTrue(recentsTab.isHittable)
        XCTAssertTrue(favoritesTab.isHittable)
        XCTAssertTrue(savedTab.isHittable)
        
        // Test tab switching in landscape
        favoritesTab.tap()
        waitForAnimationsToComplete()
        XCTAssertTrue(favoritesTab.isSelected)
    }
    
    // MARK: - Large Data Set UI Tests
    
    func testLargeDataSetPerformance() throws {
        // Given - Simulate large data set
        app.launchArguments.append("--large-dataset")
        app.terminate()
        app.launch()
        navigateToFloatingHistory()
        
        // When - Navigate through tabs with large data
        let tabBar = app.otherElements["FloatingTabBar"]
        XCTAssertTrue(tabBar.exists)
        
        measure(metrics: [XCTOSSignpostMetric.applicationLaunch]) {
            let favoritesTab = app.buttons["FavoritesTab"]
            favoritesTab.tap()
            waitForAnimationsToComplete()
            
            let savedTab = app.buttons["SavedTab"]
            savedTab.tap()
            waitForAnimationsToComplete()
            
            let recentsTab = app.buttons["RecentsTab"]
            recentsTab.tap()
            waitForAnimationsToComplete()
        }
    }
    
    func testScrollingWithLargeDataSet() throws {
        // Given - Large data set is loaded
        app.launchArguments.append("--large-dataset")
        app.terminate()
        app.launch()
        navigateToFloatingHistory()
        
        let gridView = app.scrollViews["HistoryGridView"]
        XCTAssertTrue(gridView.exists)
        
        // When - Perform extensive scrolling
        measure(metrics: [XCTOSSignpostMetric.scrollDecelerationMetric]) {
            for _ in 0..<20 {
                gridView.swipeUp()
                Thread.sleep(forTimeInterval: 0.05)
            }
            
            for _ in 0..<20 {
                gridView.swipeDown()
                Thread.sleep(forTimeInterval: 0.05)
            }
        }
        
        // Then - Tab bar should still be responsive
        let tabBar = app.otherElements["FloatingTabBar"]
        XCTAssertTrue(tabBar.exists)
    }
    
    // MARK: - Accessibility Edge Cases
    
    func testVoiceOverWithRapidTabSwitching() throws {
        // Given - VoiceOver simulation
        let tabBar = app.otherElements["FloatingTabBar"]
        XCTAssertTrue(tabBar.exists)
        
        // When - Rapidly switch tabs with VoiceOver
        let recentsTab = app.buttons["RecentsTab"]
        let favoritesTab = app.buttons["FavoritesTab"]
        let savedTab = app.buttons["SavedTab"]
        
        for _ in 0..<5 {
            favoritesTab.tap()
            Thread.sleep(forTimeInterval: 0.2) // Slower for VoiceOver
            savedTab.tap()
            Thread.sleep(forTimeInterval: 0.2)
            recentsTab.tap()
            Thread.sleep(forTimeInterval: 0.2)
        }
        
        // Then - Should maintain accessibility
        XCTAssertFalse(recentsTab.label.isEmpty)
        XCTAssertTrue(recentsTab.isSelected)
    }
    
    func testReducedMotionSupport() throws {
        // Given - Simulate reduced motion preference
        app.launchArguments.append("--reduced-motion")
        app.terminate()
        app.launch()
        navigateToFloatingHistory()
        
        // When - Perform tab switches
        let favoritesTab = app.buttons["FavoritesTab"]
        favoritesTab.tap()
        waitForAnimationsToComplete()
        
        // Then - Should work without animations
        XCTAssertTrue(favoritesTab.isSelected)
        
        // Test scroll behavior with reduced motion
        let gridView = app.scrollViews["HistoryGridView"]
        if gridView.exists {
            gridView.swipeUp()
            waitForAnimationsToComplete()
            
            let tabBar = app.otherElements["FloatingTabBar"]
            // Tab bar behavior might be different with reduced motion
            XCTAssertTrue(tabBar.exists)
        }
    }
    
    // MARK: - Stress Testing
    
    func testConcurrentUserInteractions() throws {
        // Given - Floating history view is loaded
        let tabBar = app.otherElements["FloatingTabBar"]
        let gridView = app.scrollViews["HistoryGridView"]
        
        // When - Perform concurrent interactions
        let recentsTab = app.buttons["RecentsTab"]
        let favoritesTab = app.buttons["FavoritesTab"]
        
        // Simulate user performing multiple actions simultaneously
        for _ in 0..<10 {
            // Tab switch
            favoritesTab.tap()
            
            // Scroll
            if gridView.exists {
                gridView.swipeUp()
            }
            
            // Another tab switch
            recentsTab.tap()
            
            Thread.sleep(forTimeInterval: 0.1)
        }
        
        // Then - Should handle concurrent interactions gracefully
        XCTAssertTrue(tabBar.exists)
        XCTAssertTrue(recentsTab.isSelected)
    }
    
    func testAppBackgroundingAndForegrounding() throws {
        // Given - Floating history view is active
        let tabBar = app.otherElements["FloatingTabBar"]
        XCTAssertTrue(tabBar.exists)
        
        let favoritesTab = app.buttons["FavoritesTab"]
        favoritesTab.tap()
        waitForAnimationsToComplete()
        
        // When - Background and foreground the app
        XCUIDevice.shared.press(.home)
        Thread.sleep(forTimeInterval: 1.0)
        
        app.activate()
        waitForAnimationsToComplete()
        
        // Then - Should restore state correctly
        XCTAssertTrue(tabBar.waitForExistence(timeout: 3.0))
        XCTAssertTrue(favoritesTab.isSelected)
    }
}
