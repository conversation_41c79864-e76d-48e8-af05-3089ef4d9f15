//
//  FloatingHistoryUITests.swift
//  SikTingUITests
//
//  Created by Augment Agent on 2025/1/25.
//

import XCTest

final class FloatingHistoryUITests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        app.launch()
        
        // Navigate to floating history view
        navigateToFloatingHistory()
    }
    
    override func tearDownWithError() throws {
        app = nil
    }
    
    // MARK: - Helper Methods
    
    private func navigateToFloatingHistory() {
        // Navigate to the floating history view
        // This assumes there's a way to get to the floating history from the main app
        let historyButton = app.buttons["History"]
        if historyButton.exists {
            historyButton.tap()
        }
        
        // Wait for the floating history view to appear
        let floatingHistoryView = app.otherElements["FloatingHistoryView"]
        XCTAssertTrue(floatingHistoryView.waitForExistence(timeout: 5.0))
    }
    
    private func waitForAnimationsToComplete() {
        // Wait for animations to complete
        Thread.sleep(forTimeInterval: 0.5)
    }
    
    // MARK: - Tab Switching UI Tests
    
    func testTabSwitchingBasicFunctionality() throws {
        // Given - Floating history view is displayed
        let tabBar = app.otherElements["FloatingTabBar"]
        XCTAssertTrue(tabBar.exists)
        
        // When - Tap on Favorites tab
        let favoritesTab = app.buttons["FavoritesTab"]
        XCTAssertTrue(favoritesTab.exists)
        favoritesTab.tap()
        
        waitForAnimationsToComplete()
        
        // Then - Favorites tab should be selected
        XCTAssertTrue(favoritesTab.isSelected)
        
        // When - Tap on Saved tab
        let savedTab = app.buttons["SavedTab"]
        XCTAssertTrue(savedTab.exists)
        savedTab.tap()
        
        waitForAnimationsToComplete()
        
        // Then - Saved tab should be selected
        XCTAssertTrue(savedTab.isSelected)
        XCTAssertFalse(favoritesTab.isSelected)
    }
    
    func testTabSwitchingContentFiltering() throws {
        // Given - Some sessions exist in different categories
        let gridView = app.scrollViews["HistoryGridView"]
        XCTAssertTrue(gridView.exists)
        
        // When - Switch to Recents tab
        let recentsTab = app.buttons["RecentsTab"]
        recentsTab.tap()
        waitForAnimationsToComplete()
        
        let recentsCount = gridView.cells.count
        
        // When - Switch to Favorites tab
        let favoritesTab = app.buttons["FavoritesTab"]
        favoritesTab.tap()
        waitForAnimationsToComplete()
        
        let favoritesCount = gridView.cells.count
        
        // When - Switch to Saved tab
        let savedTab = app.buttons["SavedTab"]
        savedTab.tap()
        waitForAnimationsToComplete()
        
        let savedCount = gridView.cells.count
        
        // Then - Content should be filtered appropriately
        // Note: Exact counts depend on test data, but we can verify filtering occurs
        XCTAssertTrue(recentsCount >= 0)
        XCTAssertTrue(favoritesCount >= 0)
        XCTAssertTrue(savedCount >= 0)
    }
    
    func testTabSwitchingAnimations() throws {
        // Given - Floating history view is displayed
        let tabBar = app.otherElements["FloatingTabBar"]
        XCTAssertTrue(tabBar.exists)
        
        // When - Rapidly switch between tabs
        let recentsTab = app.buttons["RecentsTab"]
        let favoritesTab = app.buttons["FavoritesTab"]
        let savedTab = app.buttons["SavedTab"]
        
        // Perform rapid tab switches
        favoritesTab.tap()
        Thread.sleep(forTimeInterval: 0.1)
        savedTab.tap()
        Thread.sleep(forTimeInterval: 0.1)
        recentsTab.tap()
        
        waitForAnimationsToComplete()
        
        // Then - Should handle rapid switches gracefully
        XCTAssertTrue(recentsTab.isSelected)
        XCTAssertTrue(tabBar.exists) // Tab bar should still be visible
    }
    
    // MARK: - Tab Bar Visibility UI Tests
    
    func testTabBarShowHideBehaviorOnScroll() throws {
        // Given - Floating history view with scrollable content
        let gridView = app.scrollViews["HistoryGridView"]
        XCTAssertTrue(gridView.exists)
        
        let tabBar = app.otherElements["FloatingTabBar"]
        XCTAssertTrue(tabBar.exists)
        XCTAssertTrue(tabBar.isHittable) // Initially visible
        
        // When - Scroll down significantly
        gridView.swipeUp() // Scroll down
        gridView.swipeUp() // Scroll down more
        
        waitForAnimationsToComplete()
        
        // Then - Tab bar should be hidden
        XCTAssertFalse(tabBar.isHittable)
        
        // When - Scroll up
        gridView.swipeDown()
        
        waitForAnimationsToComplete()
        
        // Then - Tab bar should be visible again
        XCTAssertTrue(tabBar.isHittable)
    }
    
    func testTabBarAutoShowTimeout() throws {
        // Given - Tab bar is hidden due to scrolling
        let gridView = app.scrollViews["HistoryGridView"]
        let tabBar = app.otherElements["FloatingTabBar"]
        
        // Hide tab bar by scrolling
        gridView.swipeUp()
        gridView.swipeUp()
        waitForAnimationsToComplete()
        
        XCTAssertFalse(tabBar.isHittable)
        
        // When - Wait for auto-show timeout (2+ seconds)
        Thread.sleep(forTimeInterval: 3.0)
        
        // Then - Tab bar should automatically show
        XCTAssertTrue(tabBar.isHittable)
    }
    
    func testTapToShowFunctionality() throws {
        // Given - Tab bar is hidden due to scrolling
        let gridView = app.scrollViews["HistoryGridView"]
        let tabBar = app.otherElements["FloatingTabBar"]
        
        // Hide tab bar by scrolling
        gridView.swipeUp()
        gridView.swipeUp()
        waitForAnimationsToComplete()
        
        XCTAssertFalse(tabBar.isHittable)
        
        // When - Tap on the tab bar area (even when hidden)
        let tabBarFrame = tabBar.frame
        let tapPoint = CGPoint(x: tabBarFrame.midX, y: tabBarFrame.midY)
        app.coordinate(withNormalizedOffset: CGVector(dx: 0, dy: 0)).withOffset(CGVector(dx: tapPoint.x, dy: tapPoint.y)).tap()
        
        waitForAnimationsToComplete()
        
        // Then - Tab bar should become visible
        XCTAssertTrue(tabBar.isHittable)
    }
    
    func testTabBarVisibilityAtTopOfContent() throws {
        // Given - Floating history view with content
        let gridView = app.scrollViews["HistoryGridView"]
        let tabBar = app.otherElements["FloatingTabBar"]
        
        // Hide tab bar by scrolling
        gridView.swipeUp()
        waitForAnimationsToComplete()
        
        // When - Scroll to top of content
        gridView.swipeDown()
        gridView.swipeDown()
        gridView.swipeDown() // Ensure we're at the top
        
        waitForAnimationsToComplete()
        
        // Then - Tab bar should be visible at top
        XCTAssertTrue(tabBar.isHittable)
    }
    
    // MARK: - Accessibility UI Tests
    
    func testVoiceOverTabNavigation() throws {
        // Given - VoiceOver is enabled (simulated)
        // Note: In real tests, you might need to enable VoiceOver programmatically
        
        let tabBar = app.otherElements["FloatingTabBar"]
        XCTAssertTrue(tabBar.exists)
        
        // When - Navigate through tabs with VoiceOver
        let recentsTab = app.buttons["RecentsTab"]
        let favoritesTab = app.buttons["FavoritesTab"]
        let savedTab = app.buttons["SavedTab"]
        
        // Test accessibility labels
        XCTAssertNotNil(recentsTab.label)
        XCTAssertNotNil(favoritesTab.label)
        XCTAssertNotNil(savedTab.label)
        
        // Test accessibility hints
        XCTAssertFalse(recentsTab.label.isEmpty)
        XCTAssertFalse(favoritesTab.label.isEmpty)
        XCTAssertFalse(savedTab.label.isEmpty)
        
        // When - Activate tabs via accessibility
        favoritesTab.tap()
        waitForAnimationsToComplete()
        
        // Then - Tab should be selected and announced
        XCTAssertTrue(favoritesTab.isSelected)
    }
    
    func testDynamicTypeSupport() throws {
        // Given - Floating history view is displayed
        let tabBar = app.otherElements["FloatingTabBar"]
        XCTAssertTrue(tabBar.exists)
        
        // When - Simulate larger text size
        // Note: In real tests, you might change system text size
        
        // Then - Tab bar should accommodate larger text
        let recentsTab = app.buttons["RecentsTab"]
        XCTAssertTrue(recentsTab.exists)
        
        // Verify tab is still accessible with larger text
        XCTAssertTrue(recentsTab.isHittable)
    }
    
    func testAccessibilityStateAnnouncements() throws {
        // Given - Floating history view is displayed
        let recentsTab = app.buttons["RecentsTab"]
        let favoritesTab = app.buttons["FavoritesTab"]
        
        // When - Switch tabs
        favoritesTab.tap()
        waitForAnimationsToComplete()
        
        // Then - Selected state should be properly announced
        XCTAssertTrue(favoritesTab.isSelected)
        
        // Verify accessibility value indicates selection
        if let accessibilityValue = favoritesTab.value as? String {
            XCTAssertTrue(accessibilityValue.contains("selected") || !accessibilityValue.isEmpty)
        }
    }
    
    // MARK: - Performance UI Tests
    
    func testSmoothAnimationsOnIPhone16() throws {
        // Given - Floating history view is displayed
        let tabBar = app.otherElements["FloatingTabBar"]
        let gridView = app.scrollViews["HistoryGridView"]
        
        // When - Perform multiple rapid interactions
        measure(metrics: [XCTOSSignpostMetric.animationHitchTimeRatio]) {
            // Rapid tab switching
            let recentsTab = app.buttons["RecentsTab"]
            let favoritesTab = app.buttons["FavoritesTab"]
            let savedTab = app.buttons["SavedTab"]
            
            favoritesTab.tap()
            Thread.sleep(forTimeInterval: 0.1)
            savedTab.tap()
            Thread.sleep(forTimeInterval: 0.1)
            recentsTab.tap()
            
            // Scroll interactions
            gridView.swipeUp()
            Thread.sleep(forTimeInterval: 0.1)
            gridView.swipeDown()
            
            waitForAnimationsToComplete()
        }
    }
    
    func testTabSwitchingPerformance() throws {
        // Given - Floating history view with content
        let favoritesTab = app.buttons["FavoritesTab"]
        let savedTab = app.buttons["SavedTab"]
        let recentsTab = app.buttons["RecentsTab"]
        
        // When - Measure tab switching performance
        measure(metrics: [XCTOSSignpostMetric.applicationLaunchMetric]) {
            // Perform multiple tab switches
            for _ in 0..<5 {
                favoritesTab.tap()
                Thread.sleep(forTimeInterval: 0.1)
                savedTab.tap()
                Thread.sleep(forTimeInterval: 0.1)
                recentsTab.tap()
                Thread.sleep(forTimeInterval: 0.1)
            }
        }
    }
    
    func testScrollPerformanceWithTabBarVisibility() throws {
        // Given - Floating history view with scrollable content
        let gridView = app.scrollViews["HistoryGridView"]
        
        // When - Measure scroll performance with tab bar animations
        measure(metrics: [XCTOSSignpostMetric.scrollHitchTimeRatio]) {
            // Perform multiple scroll gestures
            for _ in 0..<10 {
                gridView.swipeUp()
                Thread.sleep(forTimeInterval: 0.05)
                gridView.swipeDown()
                Thread.sleep(forTimeInterval: 0.05)
            }
        }
    }
    
    // MARK: - Integration UI Tests
    
    func testSearchIntegrationWithTabBar() throws {
        // Given - Floating history view is displayed
        let searchButton = app.buttons["SearchButton"]
        
        if searchButton.exists {
            // When - Activate search
            searchButton.tap()
            waitForAnimationsToComplete()
            
            let searchField = app.searchFields.firstMatch
            if searchField.exists {
                searchField.tap()
                searchField.typeText("test")
                
                // Then - Tab bar should remain accessible during search
                let tabBar = app.otherElements["FloatingTabBar"]
                XCTAssertTrue(tabBar.exists)
                
                // When - Switch tabs during search
                let favoritesTab = app.buttons["FavoritesTab"]
                favoritesTab.tap()
                waitForAnimationsToComplete()
                
                // Then - Search should be maintained with filtered results
                XCTAssertTrue(favoritesTab.isSelected)
                XCTAssertTrue(searchField.exists)
            }
        }
    }
    
    func testNavigationFlowIntegration() throws {
        // Given - Floating history view with sessions
        let gridView = app.scrollViews["HistoryGridView"]
        
        if gridView.cells.count > 0 {
            let firstCell = gridView.cells.firstMatch
            
            // When - Tap on a session card
            firstCell.tap()
            waitForAnimationsToComplete()
            
            // Then - Should navigate to detail view
            let detailView = app.otherElements["HistoryDetailView"]
            if detailView.waitForExistence(timeout: 3.0) {
                // When - Navigate back
                let backButton = app.navigationBars.buttons.firstMatch
                if backButton.exists {
                    backButton.tap()
                    waitForAnimationsToComplete()
                    
                    // Then - Should return to floating history view
                    let tabBar = app.otherElements["FloatingTabBar"]
                    XCTAssertTrue(tabBar.waitForExistence(timeout: 3.0))
                }
            }
        }
    }
}
