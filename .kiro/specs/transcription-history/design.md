# Design Document

## Overview

The Transcription History feature will provide a comprehensive content management system for saved transcription sessions, following the specific design patterns shown in the provided JSON specifications. The feature includes a tab-based interface with "Recents", "Favourites", and "Saved" categories, supporting both list and grid layouts with colorful cards for different content types.

## Architecture

### Core Components

```
TranscriptionHistory/
├── Models/
│   ├── HistorySession.swift          # Core data model for saved sessions
│   ├── HistoryEntry.swift            # Individual transcription entries within sessions
│   └── HistoryTag.swift              # Tagging system for organization
├── Services/
│   ├── HistoryStorageService.swift   # Core Data management and persistence
│   ├── HistorySearchService.swift    # Search and filtering logic
│   └── HistoryExportService.swift    # Export functionality
├── ViewModels/
│   ├── HistoryListViewModel.swift    # List view business logic
│   ├── HistoryDetailViewModel.swift  # Detail view business logic
│   └── HistorySearchViewModel.swift  # Search functionality
└── Views/
    ├── HistoryListView.swift         # Main history interface with tabs
    ├── HistoryDetailView.swift       # Detailed session view
    ├── HistorySessionCard.swift      # Individual session card component
    ├── HistoryGridView.swift         # Grid layout for "All Notes" style
    ├── HistoryTabBar.swift           # Custom tab bar component
    ├── HistorySearchView.swift       # Search interface
    └── HistoryEmptyStateView.swift   # Empty state handling
```

### Integration Points

- **SpeechRecognitionViewModel**: Modified to automatically save sessions when recording stops
- **TranscriptionEntry**: Extended with history metadata and relationships
- **NavigationStructure**: New tab or navigation item for History access
- **DesignSystem**: Extended with history-specific colors and components

## Components and Interfaces

### 1. History Main View (HistoryListView)

**Visual Design:**

- Light gradient background (#E8F6E9 to #CFEFCC) as specified in design JSON
- Tab-based navigation with "Recents", "Favourites", "Saved" categories
- Search functionality accessible via navigation bar button
- Smooth transitions between tab content

**Layout Structure:**

```swift
NavigationView {
    VStack(spacing: 0) {
        // Custom Tab Bar
        HistoryTabBar(
            selectedTab: $selectedTab,
            tabs: ["Recents", "Favourites", "Saved"],
            highlightColor: Color(hex: "#8AC6FF")
        )
        .padding(.horizontal, 16)
        .padding(.top, 8)

        // Content Area with TabView
        TabView(selection: $selectedTab) {
            // Recents Tab - List Layout
            HistoryListContent(sessions: recentSessions)
                .tag("Recents")

            // Favourites Tab - Grid Layout (All Notes style)
            HistoryGridContent(sessions: favouriteSessions)
                .tag("Favourites")

            // Saved Tab - List Layout
            HistoryListContent(sessions: savedSessions)
                .tag("Saved")
        }
        .tabViewStyle(.page(indexDisplayMode: .never))
    }
    .background(historyGradientBackground)
    .navigationTitle("History")
    .navigationBarTitleDisplayMode(.inline)
    .toolbar {
        ToolbarItem(placement: .navigationBarTrailing) {
            Button(action: { showingSearch = true }) {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.primary)
            }
        }
    }
}
```

### 2. History Tab Bar (HistoryTabBar)

**Design Specifications:**

- Custom tab bar matching the JSON design specifications
- Highlight color: #8AC6FF for active tab
- Background: rgba(0,0,0,0.05) with rounded corners
- Font: 14pt regular weight

**Implementation:**

```swift
struct HistoryTabBar: View {
    @Binding var selectedTab: String
    let tabs: [String]
    let highlightColor: Color

    var body: some View {
        HStack(spacing: 0) {
            ForEach(tabs, id: \.self) { tab in
                Button(action: { selectedTab = tab }) {
                    Text(tab)
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(selectedTab == tab ? .primary : .secondary)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(
                            selectedTab == tab ?
                            highlightColor.opacity(0.2) : Color.clear
                        )
                        .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(4)
        .background(Color.black.opacity(0.05))
        .cornerRadius(12)
    }
}
```

### 3. History List Content (List Layout)

**Design Specifications:**

- White background cards (#FFFFFF)
- 12pt corner radius
- 12pt internal padding
- 12pt spacing between cards
- Action buttons: delete, copy, speak

**Card Layout:**

```swift
struct HistoryListCard: View {
    let session: HistorySession

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Main content text
            Text(session.contentPreview)
                .font(.system(size: 14, weight: .regular))
                .lineSpacing(1.5 * 14 - 14) // lineHeight 1.5
                .foregroundColor(.primary)
                .lineLimit(3)

            // Action buttons row
            HStack {
                Spacer()

                Button(action: { speakContent() }) {
                    Image(systemName: "speaker.2")
                        .foregroundColor(.blue)
                }

                Button(action: { copyContent() }) {
                    Image(systemName: "doc.on.doc")
                        .foregroundColor(.blue)
                }

                Button(action: { deleteSession() }) {
                    Image(systemName: "trash")
                        .foregroundColor(.red)
                }
            }
            .font(.system(size: 16))
        }
        .padding(12)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}
```

### 4. History Grid Content (Grid Layout for Favourites)

**Design Specifications:**

- 2-column grid layout
- Colorful background cards with varied colors
- Same 12pt corner radius and padding
- Staggered heights based on content

**Grid Implementation:**

```swift
struct HistoryGridContent: View {
    let sessions: [HistorySession]
    let cardColors: [Color] = [
        Color(hex: "#E6E6E6"),
        Color(hex: "#F5CEDA"),
        Color(hex: "#F6EBD9"),
        Color(hex: "#DDF3F4"),
        Color(hex: "#DBDFF4"),
        Color(hex: "#EAEAEA")
    ]

    var body: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible(), spacing: 12),
                GridItem(.flexible(), spacing: 12)
            ], spacing: 12) {
                ForEach(Array(sessions.enumerated()), id: \.element.id) { index, session in
                    HistoryGridCard(
                        session: session,
                        backgroundColor: cardColors[index % cardColors.count]
                    )
                }
            }
            .padding(16)
        }
    }
}

struct HistoryGridCard: View {
    let session: HistorySession
    let backgroundColor: Color

    var body: some View {
        Text(session.contentPreview)
            .font(.system(size: 14, weight: .regular))
            .lineSpacing(1.5 * 14 - 14)
            .foregroundColor(.primary)
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(12)
            .background(backgroundColor)
            .cornerRadius(12)
    }
}
```

### 5. Search Interface

**Design Specifications:**

- Full-screen modal presentation
- Search bar with real-time filtering
- Results displayed in same card format as main views
- Highlighting of search terms in results

**Search View:**

```swift
struct HistorySearchView: View {
    @State private var searchText = ""
    @ObservedObject var viewModel: HistorySearchViewModel

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search Bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)

                    TextField("Search transcriptions...", text: $searchText)
                        .textFieldStyle(.plain)
                        .onChange(of: searchText) { newValue in
                            viewModel.search(query: newValue)
                        }

                    if !searchText.isEmpty {
                        Button("Clear") {
                            searchText = ""
                            viewModel.clearSearch()
                        }
                        .foregroundColor(.blue)
                    }
                }
                .padding(12)
                .background(Color(.systemGray6))
                .cornerRadius(10)
                .padding(.horizontal, 16)
                .padding(.top, 8)

                // Search Results
                if viewModel.searchResults.isEmpty && !searchText.isEmpty {
                    HistoryEmptyStateView(type: .noSearchResults)
                } else {
                    List(viewModel.searchResults) { session in
                        HistorySearchResultCard(
                            session: session,
                            searchTerm: searchText
                        )
                        .listRowBackground(Color.clear)
                        .listRowSeparator(.hidden)
                    }
                    .listStyle(.plain)
                }
            }
            .background(historyGradientBackground)
            .navigationTitle("Search")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        // Dismiss search
                    }
                }
            }
        }
    }
}
```

## Data Models

### Core Data Schema

**HistorySession Entity:**

```swift
@objc(HistorySession)
public class HistorySession: NSManagedObject {
    @NSManaged public var id: UUID
    @NSManaged public var title: String
    @NSManaged public var createdAt: Date
    @NSManaged public var updatedAt: Date
    @NSManaged public var duration: TimeInterval
    @NSManaged public var entryCount: Int32
    @NSManaged public var contentPreview: String
    @NSManaged public var detectedLanguages: [String] // JSON encoded
    @NSManaged public var translationLanguages: [String] // JSON encoded
    @NSManaged public var hasTranslations: Bool
    @NSManaged public var isFavourite: Bool
    @NSManaged public var isSaved: Bool
    @NSManaged public var cardColorIndex: Int16 // For grid view colors
    @NSManaged public var entries: NSSet? // Relationship to HistoryEntry
}
```

**HistoryEntry Entity:**

```swift
@objc(HistoryEntry)
public class HistoryEntry: NSManagedObject {
    @NSManaged public var id: UUID
    @NSManaged public var timestamp: Date
    @NSManaged public var originalText: String
    @NSManaged public var translatedText: String?
    @NSManaged public var detectedLanguage: String
    @NSManaged public var targetLanguage: String?
    @NSManaged public var emotion: String
    @NSManaged public var audioType: String
    @NSManaged public var isFinal: Bool
    @NSManaged public var session: HistorySession // Relationship
}
```

### Session Categories

- **Recents**: All sessions ordered by creation date (last 30 days)
- **Favourites**: User-marked favourite sessions (displayed in grid layout)
- **Saved**: Explicitly saved sessions for long-term storage

## Error Handling

### Storage Errors

- **Core Data failures**: Graceful degradation with user notification
- **Storage space issues**: Automatic cleanup suggestions
- **Data corruption**: Recovery mechanisms with backup validation

### User Experience Errors

- **Empty states**: Different messages for each tab (no recents, no favourites, etc.)
- **Search no results**: Helpful suggestions and clear messaging
- **Action failures**: Toast notifications for copy, delete, speak actions

## Testing Strategy

### Unit Testing

- **Data Models**: Core Data entity validation and relationships
- **Services**: Storage operations, search functionality, categorization logic
- **ViewModels**: Business logic, state management, data transformation

### Integration Testing

- **Core Data Stack**: Database operations and migration testing
- **Search Performance**: Large dataset search response times
- **Tab Navigation**: Smooth transitions and state preservation

### UI Testing

- **Navigation Flow**: Complete user journey through history features
- **Search Interactions**: Real-time search and filtering behavior
- **Card Actions**: Copy, delete, speak functionality testing

## Visual Design Specifications

### Color Palette

- **Background Gradient**: #E8F6E9 to #CFEFCC (top to bottom)
- **Card Backgrounds**:
  - List cards: #FFFFFF (white)
  - Grid cards: #E6E6E6, #F5CEDA, #F6EBD9, #DDF3F4, #DBDFF4, #EAEAEA
- **Tab Highlight**: #8AC6FF
- **Tab Background**: rgba(0,0,0,0.05)

### Typography

- **Navigation Title**: 20pt bold
- **Tab Labels**: 14pt regular
- **Card Content**: 14pt regular with 1.5 line height
- **Action Buttons**: 16pt system icons

### Layout Specifications

- **Grid**: 2 columns with 12pt spacing
- **List**: Single column with 12pt spacing
- **Padding**: 16pt screen margins, 12pt card internal padding
- **Corner Radius**: 12pt for all cards and containers
- **Shadows**: Subtle shadow (black 5% opacity, 2pt radius, 1pt offset)

### Animation and Interactions

- **Tab Switching**: Smooth page transitions with TabView
- **Card Actions**: Haptic feedback for all button interactions
- **Search**: Real-time filtering with debounced input
- **Loading States**: Skeleton loading for card content

This design ensures the history feature matches your specified visual requirements while maintaining integration with SikTing's existing architecture and functionality.
