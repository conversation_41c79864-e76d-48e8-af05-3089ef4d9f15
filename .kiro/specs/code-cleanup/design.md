# Design Document

## Overview

This design outlines a systematic approach to cleaning up the SikTing codebase by removing unused files, methods, properties, and commented-out code. The cleanup will be performed in a safe, incremental manner to ensure the application continues to function correctly.

## Architecture

The cleanup will follow a layered approach:

1. **File-level cleanup**: Remove entire unused files
2. **Class-level cleanup**: Remove unused methods and properties within classes
3. **Code-level cleanup**: Remove commented-out code blocks and unused imports
4. **Validation**: Ensure application still compiles and functions correctly

## Components and Interfaces

### Files to be Removed

#### Complete File Removal

- **MergingTestPlayground.swift**: Test/playground file that is not integrated into the app
  - Only used for manual testing of transcript merging
  - Contains commented-out execution code
  - No references from main application code

### Methods and Properties to be Removed

#### AudioEngineManager.swift

- **convertBufferToData()**: Private method that is defined but never called
  - Alternative method `convertBufferToInt16Samples()` is used instead
  - Represents dead code from earlier implementation

#### WebSocketManager.swift

- **mergeTranscript()** and all related fuzzy matching methods:
  - `findCharacterOverlap()`
  - `findTokenOverlap()`
  - `findFuzzyOverlap()`
  - `calculateStringSimilarity()`
  - `calculateTokenSimilarity()`
  - `calculateSequenceSimilarity()`
  - `levenshteinDistance()`
  - `tokenize()`
  - `findBestTokenOverlap()`
  - Supporting structs: `OverlapMatch`, `FuzzyMatch`
- **audioBuffer** and **chunkSize** properties (legacy chunking system)
  - Current implementation uses direct audio data sending
  - These represent an older buffering approach that's no longer used

#### SpeechRecognitionViewModel.swift

- **handleWithTimestamp()**: Stub method that just returns input unchanged
- **parseHotwords()**: Method that parses hotwords but hotwords are commented out in config
- **hotwords** property and related AppStorage: Feature is implemented but disabled
- **backgroundColor** property in RecognizedLanguage enum: UI feature not implemented

#### Debug/Testing Methods (Refactor, Don't Remove)

- **testTranscriptionFlow()**: Move to dedicated test utility class for better organization
- **processTestMessagesWithDelay()**: Move to test utility class alongside testTranscriptionFlow

#### TranscriptionView.swift

- **cursorVisible** state variable: Part of commented-out cursor animation
- All commented-out cursor animation code blocks
- **buttonText** computed property: Defined but never used in UI

### Commented-Out Code Blocks

#### FunASRConfiguration.swift (within SpeechRecognitionViewModel.swift)

- Commented-out hotwords property and related CodingKeys
- Multiple commented-out hotwords references in createFunASRConfiguration()

#### TranscriptionView.swift

- Large blocks of commented-out cursor animation code
- Commented-out UI styling code

## Data Models

### New Components to be Created

#### TranscriptionTestUtility.swift

- **Purpose**: Dedicated utility class for testing transcription flow
- **Methods**:
  - `testTranscriptionFlow()`: Moved from SpeechRecognitionViewModel
  - `processTestMessagesWithDelay()`: Moved from SpeechRecognitionViewModel
- **Benefits**:
  - Separates testing concerns from main view model
  - Makes testing methods easily discoverable
  - Allows for expansion of testing capabilities without cluttering main classes

### Simplified Models After Cleanup

The cleanup will result in:

- Streamlined AudioEngineManager focused only on audio capture
- Simplified WebSocketManager focused only on basic WebSocket communication
- Cleaner SpeechRecognitionViewModel with testing methods moved to dedicated utility
- Simplified TranscriptionView without unused UI state
- New TranscriptionTestUtility for organized testing functionality

## Error Handling

### Safety Measures

1. **Incremental Removal**: Remove code in small, testable chunks
2. **Compilation Verification**: Ensure project compiles after each removal
3. **Functionality Testing**: Verify core features work after cleanup
4. **Git Tracking**: Each removal will be a separate commit for easy rollback

### Risk Mitigation

- Keep backup of original code in version control
- Test audio recording functionality after AudioEngineManager cleanup
- Test WebSocket communication after WebSocketManager cleanup
- Verify UI functionality after TranscriptionView cleanup

## Testing Strategy

### Testing Environment

- **Primary Testing Platform**: iPhone 16 simulator
- All functionality testing must be performed on iPhone 16 simulator to ensure compatibility

### Validation Steps

1. **Compilation Test**: Project must compile without errors or warnings
2. **Core Functionality Test** (on iPhone 16 simulator):
   - Audio recording starts and stops correctly
   - WebSocket connection establishes successfully
   - Transcription display updates in real-time
   - Settings can be modified and saved
3. **UI Test** (on iPhone 16 simulator): All user interface elements function as expected
4. **Integration Test** (on iPhone 16 simulator): End-to-end transcription workflow works correctly

### Test Cases

All test cases must be executed on iPhone 16 simulator:

- Start/stop recording
- Connect to WebSocket server
- Display transcription results
- Navigate to settings and back
- Clear transcription history
- Test utility functionality from Test button

## Implementation Phases

### Phase 1: File Removal

- Remove MergingTestPlayground.swift
- Update any import statements if necessary

### Phase 2: Create Test Utility Class

- Create new TranscriptionTestUtility.swift file
- Move testTranscriptionFlow() and processTestMessagesWithDelay() methods
- Update TranscriptionView to use new test utility class

### Phase 3: Method Cleanup

- Remove unused methods from AudioEngineManager
- Remove complex merging logic from WebSocketManager
- Remove stub methods from SpeechRecognitionViewModel

### Phase 3: Property Cleanup

- Remove unused properties and state variables
- Clean up commented-out property declarations

### Phase 4: Code Block Cleanup

- Remove all commented-out code blocks
- Clean up unused imports and declarations

### Phase 5: Validation

- Full compilation and functionality testing
- Performance verification to ensure no regressions
