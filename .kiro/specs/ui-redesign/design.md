# Design Document

## Overview

This design document outlines a comprehensive UI/UX redesign for the SikTing iOS speech-to-text application. The redesign transforms the current interface into a modern, professional, and elegant experience that follows Apple's Human Interface Guidelines while addressing existing issues such as redundant header views and visually unappealing history interface.

The design leverages the specified color palette (Persian Purple, Orchid, French Lilac, Amber, Alabaster) to create a cohesive brand experience across all screens, with full support for light and dark modes optimized for iPhone 16.

## Architecture

### Design System Architecture

The redesign is built upon a comprehensive design system that ensures consistency and scalability:

```
DesignSystem/
├── ColorPalette (Updated with brand colors)
├── TypographyScale (iOS-optimized hierarchy)
├── SpacingScale (8pt grid system)
├── ComponentLibrary (Reusable UI components)
├── AnimationSystem (Smooth transitions)
└── AccessibilitySupport (WCAG compliance)
```

### Information Architecture

The app maintains its three-tab structure but with improved navigation flow:

1. **Record Tab** - Primary transcription interface
2. **History Tab** - Enhanced browsing and search experience
3. **Settings Tab** - Organized configuration options

### Visual Hierarchy

- **Primary Level**: Tab navigation and main content areas
- **Secondary Level**: Section headers and key actions
- **Tertiary Level**: Supporting information and metadata
- **Interactive Level**: Buttons, controls, and feedback elements

## Components and Interfaces

### Updated Color Palette Integration

The existing design system will be enhanced with the specified brand colors:

```swift
struct BrandColorPalette {
    // Primary Brand Colors
    let persianPurple = Color(hex: "#3D2593")     // Primary actions, headers
    let orchid = Color(hex: "#8C43D0")           // Secondary actions, accents
    let frenchLilac = Color(hex: "#E9D3FD")      // Backgrounds, subtle highlights
    let amber = Color(hex: "#FDC500")            // Warnings, notifications, CTAs
    let alabaster = Color(hex: "#FFFFFF")        // Text, clean backgrounds

    // Gradient Combinations
    let primaryGradient = LinearGradient(
        colors: [persianPurple, orchid],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    let backgroundGradient = LinearGradient(
        colors: [frenchLilac.opacity(0.3), alabaster],
        startPoint: .top,
        endPoint: .bottom
    )

    // 50% Opacity Variants
    let persianPurple50 = persianPurple.opacity(0.5)
    let orchid50 = orchid.opacity(0.5)
    let frenchLilac50 = frenchLilac.opacity(0.5)
}
```

### Core UI Components

#### 1. Enhanced Tab Bar

- **Design**: Custom tab bar with brand colors and smooth transitions
- **Active State**: Persian Purple background with white icons
- **Inactive State**: Orchid tint with subtle opacity
- **Badge Support**: Amber badges for notifications

#### 2. Redesigned Recording Interface

- **Main Button**: Large circular button with Persian Purple gradient
- **Audio Visualization**: Animated rings using Orchid with opacity variations
- **Status Indicators**: Amber for warnings, French Lilac for neutral states
- **Background**: Subtle gradient from French Lilac to Alabaster

#### 3. Modern History Cards

- **Card Design**: Rounded corners (12pt) with subtle shadows
- **Background**: Alabaster with French Lilac accent borders
- **Typography**: Clear hierarchy with Persian Purple headers
- **Actions**: Orchid-tinted action buttons with haptic feedback

#### 4. Enhanced Search Interface

- **Search Bar**: French Lilac background with Persian Purple focus state
- **Filter Pills**: Orchid background with white text
- **Results**: Highlighted matches in Amber

#### 5. Settings Interface

- **Section Headers**: Persian Purple with medium weight
- **Toggle Controls**: Orchid accent color
- **Destructive Actions**: System red maintained for consistency
- **Background**: Clean Alabaster with French Lilac section dividers

### Navigation Patterns

#### Tab Navigation

- **Implementation**: Custom TabView with brand styling
- **Transitions**: Smooth cross-fade animations (0.3s ease-in-out)
- **State Management**: Persistent tab selection with deep linking support

#### Modal Presentations

- **Sheet Style**: Large detent with rounded corners
- **Background**: French Lilac overlay with blur effect
- **Dismissal**: Swipe gestures with haptic feedback

#### Navigation Stack

- **Back Button**: Persian Purple tint with custom chevron
- **Title Display**: Large titles where appropriate, inline for detail views
- **Toolbar**: Consistent button styling with proper spacing

## Data Models

### Theme Configuration Model

```swift
struct ThemeConfiguration {
    let colorPalette: BrandColorPalette
    let isDarkMode: Bool
    let accessibilitySettings: AccessibilityConfiguration

    var adaptiveColors: AdaptiveColorPalette {
        AdaptiveColorPalette(
            primary: isDarkMode ? colorPalette.orchid : colorPalette.persianPurple,
            background: isDarkMode ? Color.black : colorPalette.alabaster,
            surface: isDarkMode ? Color(.systemGray6) : colorPalette.frenchLilac.opacity(0.3)
        )
    }
}
```

### Component State Models

```swift
struct RecordingInterfaceState {
    var isRecording: Bool
    var audioLevel: Float
    var connectionStatus: ConnectionState
    var visualizationData: [Float]
}

struct HistoryViewState {
    var selectedTab: HistoryTab
    var searchQuery: String
    var filterOptions: FilterConfiguration
    var sortOrder: SortOrder
}
```

## Error Handling

### Visual Error States

#### Connection Errors

- **Design**: Amber background with Persian Purple text
- **Icon**: System warning icon with subtle animation
- **Actions**: Retry button with Orchid styling

#### Permission Errors

- **Design**: French Lilac background with clear messaging
- **Icon**: Microphone with slash overlay
- **Actions**: Settings navigation with Persian Purple CTA

#### Empty States

- **Illustration**: Custom illustrations using brand colors
- **Typography**: Clear hierarchy with encouraging messaging
- **Actions**: Primary actions in Persian Purple gradient

### Error Recovery Patterns

```swift
struct ErrorRecoveryView {
    let errorType: AppError
    let retryAction: () -> Void

    var errorConfiguration: ErrorConfiguration {
        switch errorType {
        case .networkError:
            return ErrorConfiguration(
                backgroundColor: BrandColors.amber.opacity(0.1),
                iconColor: BrandColors.amber,
                actionColor: BrandColors.persianPurple
            )
        case .permissionError:
            return ErrorConfiguration(
                backgroundColor: BrandColors.frenchLilac.opacity(0.3),
                iconColor: BrandColors.orchid,
                actionColor: BrandColors.persianPurple
            )
        }
    }
}
```

## Testing Strategy

### Visual Testing Approach

#### Device Testing Matrix

- **Primary**: iPhone 16 (6.1" display, 2556×1179 resolution)
- **Secondary**: iPhone 16 Pro (6.3" display, 2622×1206 resolution)
- **Accessibility**: Large text sizes, high contrast modes

#### Color Testing

- **Light Mode**: Full brand palette implementation
- **Dark Mode**: Adapted colors maintaining brand recognition
- **Accessibility**: High contrast variants meeting WCAG AA standards

#### Animation Testing

- **Performance**: 60fps target for all transitions
- **Reduced Motion**: Respect system accessibility settings
- **Battery Impact**: Optimized animations for power efficiency

### Component Testing Strategy

#### Unit Testing

- Color palette accuracy across light/dark modes
- Typography scaling with Dynamic Type
- Component state management

#### Integration Testing

- Tab navigation flow
- Search and filter functionality
- Recording interface state transitions

#### Accessibility Testing

- VoiceOver navigation
- Switch Control compatibility
- Voice Control support

### User Experience Testing

#### Usability Testing Scenarios

1. **First-time user onboarding**
2. **Recording a transcription session**
3. **Searching and filtering history**
4. **Configuring app settings**
5. **Handling error states**

#### Performance Benchmarks

- **App Launch**: < 2 seconds to interactive
- **Tab Switching**: < 0.3 seconds transition
- **Search Results**: < 1 second for local queries
- **History Loading**: Progressive loading with skeleton states

### Implementation Phases

#### Phase 1: Foundation (Week 1-2)

- Update design system with brand colors
- Implement core component library
- Create adaptive color system for light/dark modes

#### Phase 2: Core Interfaces (Week 3-4)

- Redesign recording interface
- Implement new tab bar design
- Update navigation patterns

#### Phase 3: History Experience (Week 5-6)

- Redesign history list and cards
- Implement enhanced search interface
- Add filtering and sorting capabilities

#### Phase 4: Settings & Polish (Week 7-8)

- Redesign settings interface
- Implement error states and empty states
- Add animations and micro-interactions

#### Phase 5: Testing & Refinement (Week 9-10)

- Comprehensive testing across devices
- Accessibility compliance verification
- Performance optimization
- User feedback integration

### Design Deliverables

#### Figma Design System

- **Component Library**: All UI components with variants
- **Color Styles**: Complete brand palette with semantic naming
- **Text Styles**: Typography scale with Dynamic Type support
- **Effect Styles**: Shadows, blurs, and gradients

#### Screen Designs

- **Light Mode**: Complete interface designs
- **Dark Mode**: Adapted designs maintaining brand consistency
- **Accessibility**: High contrast and large text variants

#### Interactive Prototypes

- **Navigation Flow**: Tab switching and deep linking
- **Recording Experience**: Start/stop recording with feedback
- **Search Experience**: Query input and results display

#### Style Guide Documentation

- **Color Usage**: When and how to use each brand color
- **Typography Guidelines**: Hierarchy and sizing rules
- **Spacing System**: 8pt grid implementation
- **Animation Principles**: Timing and easing guidelines

This comprehensive design approach ensures a cohesive, accessible, and modern interface that elevates the SikTing user experience while maintaining technical feasibility and performance standards.
